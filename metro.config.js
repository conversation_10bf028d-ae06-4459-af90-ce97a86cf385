const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Add web-specific resolver configuration
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Configure platform-specific extensions
config.resolver.platformExtensions = ['web.tsx', 'web.ts', 'web.jsx', 'web.js', 'tsx', 'ts', 'jsx', 'js'];

// Add alias for web compatibility
config.resolver.alias = {
  ...config.resolver.alias,
  // Mock native-only modules for web
  'react-native/Libraries/Utilities/codegenNativeCommands': path.resolve(__dirname, 'web-mocks/codegenNativeCommands.js'),
  'react-native/Libraries/TurboModule/TurboModuleRegistry': path.resolve(__dirname, 'web-mocks/TurboModuleRegistry.js'),
  // Mock the entire Stripe package for web
  '@stripe/stripe-react-native': path.resolve(__dirname, 'web-mocks/stripe-react-native.js'),
};

// Add custom resolver for web platform
const originalResolver = config.resolver.resolverMainFields;
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// Override resolver to handle native modules on web
config.resolver.resolveRequest = (context, moduleName, platform) => {
  if (platform === 'web') {
    // Handle Stripe React Native
    if (moduleName === '@stripe/stripe-react-native') {
      return {
        filePath: path.resolve(__dirname, 'web-mocks/stripe-react-native.js'),
        type: 'sourceFile',
      };
    }

    // Handle native commands
    if (moduleName === 'react-native/Libraries/Utilities/codegenNativeCommands') {
      return {
        filePath: path.resolve(__dirname, 'web-mocks/codegenNativeCommands.js'),
        type: 'sourceFile',
      };
    }

    // Handle TurboModule
    if (moduleName === 'react-native/Libraries/TurboModule/TurboModuleRegistry') {
      return {
        filePath: path.resolve(__dirname, 'web-mocks/TurboModuleRegistry.js'),
        type: 'sourceFile',
      };
    }
  }

  // Fall back to default resolver
  return context.resolveRequest(context, moduleName, platform);
};

module.exports = config;
