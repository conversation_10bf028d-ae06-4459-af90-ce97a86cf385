import { useAuthStore } from '../../store/authStore';
import { API_CONFIG } from '../../utils/constants';
import { axiosInstance, AxiosRequestConfig } from '../config/axios';

export interface ApiError {
  message: string;
  status: number;
  code?: string;
  retryable?: boolean;
  timestamp?: string;
}

export interface RailwayHealthCheck {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  services: {
    database: 'connected' | 'disconnected';
    redis: 'connected' | 'disconnected';
  };
}

class ApiClient {
  private baseURL: string;
  private timeout: number;
  private retryAttempts: number;
  private retryDelay: number;

  constructor() {
    // Use Railway environment configuration if available, fallback to constants
    this.baseURL = process.env.EXPO_PUBLIC_API_URL || API_CONFIG.BASE_URL;
    this.timeout = API_CONFIG.TIMEOUT;
    this.retryAttempts = API_CONFIG.RETRY_ATTEMPTS || 3;
    this.retryDelay = 1000; // 1 second base delay
  }

  private async getAuthToken(): Promise<string | null> {
    return await useAuthStore.getState().getToken();
  }

  // Railway health check
  async healthCheck(): Promise<RailwayHealthCheck> {
    try {
      const response = await axiosInstance.get('/health', {
        headers: { skipAuth: true } as any
      });
      return response.data;
    } catch (error) {
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        services: {
          database: 'disconnected',
          redis: 'disconnected',
        },
      };
    }
  }

  // Retry logic for Railway deployments
  private async retryRequest<T>(
    requestFn: () => Promise<T>,
    attempt: number = 1
  ): Promise<T> {
    try {
      return await requestFn();
    } catch (error) {
      if (attempt >= this.retryAttempts) {
        throw error;
      }

      // Check if error is retryable (network errors, 5xx errors)
      const isRetryable = this.isRetryableError(error);
      if (!isRetryable) {
        throw error;
      }

      // Exponential backoff
      const delay = this.retryDelay * Math.pow(2, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, delay));

      return this.retryRequest(requestFn, attempt + 1);
    }
  }

  private isRetryableError(error: any): boolean {
    if (error instanceof Error) {
      // Network errors
      if (error.name === 'AbortError' || error.message.includes('fetch')) {
        return true;
      }
    }

    // HTTP errors
    if (error.status) {
      // Retry on 5xx server errors and 429 rate limiting
      return error.status >= 500 || error.status === 429;
    }

    return false;
  }

  private async request<T>(
    endpoint: string,
    options: AxiosRequestConfig = {}
  ): Promise<T> {
    return this.retryRequest(() => this.makeRequest<T>(endpoint, options));
  }

  private async makeRequest<T>(
    endpoint: string,
    options: AxiosRequestConfig = {}
  ): Promise<T> {
    try {
      const response = await axiosInstance.request<T>({
        url: endpoint,
        timeout: this.timeout,
        ...options,
      });
      return response.data;
    } catch (error) {
      // Axios interceptors will handle error transformation
      throw error;
    }
  }

  // HTTP Methods
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'GET',
      params,
    });
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      data,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      data,
    });
  }

  async patch<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      data,
    });
  }

  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
    });
  }

  // File upload
  async uploadFile<T>(
    endpoint: string,
    file: File | Blob,
    additionalData?: Record<string, any>
  ): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);

    if (additionalData) {
      Object.keys(additionalData).forEach(key => {
        formData.append(key, String(additionalData[key]));
      });
    }

    return this.request<T>(endpoint, {
      method: 'POST',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient();

// Convenience methods for common API patterns
export const api = {
  // Railway system endpoints
  system: {
    health: () => apiClient.healthCheck(),
    status: () => apiClient.get('/system/status'),
    metrics: () => apiClient.get('/system/metrics'),
  },
  // Auth endpoints
  auth: {
    login: (credentials: { email: string; password: string }) =>
      apiClient.post('/auth/login', credentials),
    register: (userData: any) =>
      apiClient.post('/auth/register', userData),
    logout: () =>
      apiClient.post('/auth/logout'),
    refreshToken: (refreshToken: string) =>
      apiClient.post('/auth/refresh', { refreshToken }),
    forgotPassword: (email: string) =>
      apiClient.post('/auth/forgot-password', { email }),
    resetPassword: (token: string, password: string) =>
      apiClient.post('/auth/reset-password', { token, password }),
    verifyOtp: (email: string, otp: string) =>
      apiClient.post('/auth/verify-otp', { email, otp }),
  },

  // User endpoints
  user: {
    getProfile: () =>
      apiClient.get('/user/profile'),
    updateProfile: (data: any) =>
      apiClient.put('/user/profile', data),
    getAddresses: () =>
      apiClient.get('/user/addresses'),
    addAddress: (address: any) =>
      apiClient.post('/user/addresses', address),
    updateAddress: (id: string, address: any) =>
      apiClient.put(`/user/addresses/${id}`, address),
    deleteAddress: (id: string) =>
      apiClient.delete(`/user/addresses/${id}`),
    getPaymentMethods: () =>
      apiClient.get('/user/payment-methods'),
    addPaymentMethod: (paymentMethod: any) =>
      apiClient.post('/user/payment-methods', paymentMethod),
    deletePaymentMethod: (id: string) =>
      apiClient.delete(`/user/payment-methods/${id}`),
  },

  // Restaurant endpoints
  restaurants: {
    getAll: (params?: any) =>
      apiClient.get('/restaurants', params),
    getById: (id: string) =>
      apiClient.get(`/restaurants/${id}`),
    getMenu: (id: string) =>
      apiClient.get(`/restaurants/${id}/menu`),
    search: (query: string, filters?: any) =>
      apiClient.get('/restaurants/search', { query, ...filters }),
    getCategories: () =>
      apiClient.get('/categories'),
    getFavorites: () =>
      apiClient.get('/user/favorites'),
    addToFavorites: (restaurantId: string) =>
      apiClient.post('/user/favorites', { restaurantId }),
    removeFromFavorites: (restaurantId: string) =>
      apiClient.delete(`/user/favorites/${restaurantId}`),
  },

  // Order endpoints
  orders: {
    create: (orderData: any) =>
      apiClient.post('/orders', orderData),
    getAll: (params?: any) =>
      apiClient.get('/orders', params),
    getById: (id: string) =>
      apiClient.get(`/orders/${id}`),
    updateStatus: (id: string, status: string) =>
      apiClient.patch(`/orders/${id}/status`, { status }),
    getTracking: (id: string) =>
      apiClient.get(`/orders/${id}/tracking`),
    cancel: (id: string, reason?: string) =>
      apiClient.post(`/orders/${id}/cancel`, { reason }),
  },

  // Review endpoints
  reviews: {
    create: (reviewData: any) =>
      apiClient.post('/reviews', reviewData),
    getByRestaurant: (restaurantId: string, params?: any) =>
      apiClient.get(`/restaurants/${restaurantId}/reviews`, params),
    getByUser: (params?: any) =>
      apiClient.get('/user/reviews', params),
    update: (id: string, reviewData: any) =>
      apiClient.put(`/reviews/${id}`, reviewData),
    delete: (id: string) =>
      apiClient.delete(`/reviews/${id}`),
  },

  // Notification endpoints
  notifications: {
    getAll: (params?: any) =>
      apiClient.get('/notifications', params),
    markAsRead: (id: string) =>
      apiClient.patch(`/notifications/${id}/read`),
    markAllAsRead: () =>
      apiClient.patch('/notifications/read-all'),
    updateSettings: (settings: any) =>
      apiClient.put('/user/notification-settings', settings),
  },

  // Promotion endpoints
  promotions: {
    getAll: (params?: any) =>
      apiClient.get('/promotions', params),
    getById: (id: string) =>
      apiClient.get(`/promotions/${id}`),
    validateCode: (code: string) =>
      apiClient.post('/promotions/validate', { code }),
  },
};
