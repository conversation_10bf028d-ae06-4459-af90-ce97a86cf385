import React from 'react';
import { Image, StyleSheet, Text, View, ViewStyle } from 'react-native';
import { COLORS } from '../utils/constants';

interface FoodWayLogoProps {
  size?: number;
  variant?: 'full' | 'icon' | 'text';
  color?: string;
  style?: ViewStyle;
  showText?: boolean;
}

export const FoodWayLogo: React.FC<FoodWayLogoProps> = ({
  size = 60,
  variant = 'full',
  color = COLORS.primary,
  style,
  showText = true,
}) => {
  const logoSize = size;
  const textSize = size * 0.3;

  if (variant === 'text') {
    return (
      <View style={[styles.container, style]}>
        <Text style={[styles.logoText, { fontSize: textSize, color }]}>
          FoodWay
        </Text>
      </View>
    );
  }

  if (variant === 'icon') {
    return (
      <View style={[styles.iconContainer, { width: logoSize, height: logoSize, backgroundColor: color }, style]}>
        <Image
          source={require('../assets/images/foodway logo.jpeg')}
          style={{ width: logoSize * 0.6, height: logoSize * 0.6 }}
          resizeMode="contain"
        />
        {showText && (
          <Text style={[styles.logoText, { fontSize: textSize, color, marginTop: 5 }]}>
            FoodWay
          </Text>
        )}
      </View>
    );
  }

  // Full variant
  return (
    <View style={[styles.container, style]}>
      <View style={[styles.iconContainer, { width: logoSize * 0.8, height: logoSize * 0.8, backgroundColor: color }]}>
        <Image
          source={require('../assets/images/foodway logo.jpeg')}
          style={{ width: logoSize * 0.4, height: logoSize * 0.4 }}
          resizeMode="contain"
        />
      </View>
      {showText && (
        <View style={styles.textContainer}>
          <Text style={[styles.logoText, { fontSize: textSize, color }]}>
            FoodWay
          </Text>
          <Text style={[styles.tagline, { fontSize: textSize * 0.5, color: '#666666' }]}>
            Delicious food, delivered fast
          </Text>
        </View>
      )}
    </View>
  );
};

// Simplified logo for headers and small spaces
export const FoodWayMiniLogo: React.FC<{ size?: number; color?: string }> = ({
  size = 40,
  color = COLORS.primary,
}) => (
  <View style={[styles.miniContainer, { width: size, height: size, backgroundColor: color }]}>
    <Image
      source={require('../assets/images/foodway logo.jpeg')}
      style={{ width: size * 0.6, height: size * 0.6 }}
      resizeMode="contain"
    />
  </View>
);

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textContainer: {
    alignItems: 'center',
    marginTop: 8,
  },
  miniContainer: {
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoText: {
    fontWeight: 'bold',
    textAlign: 'center',
    fontFamily: 'System',
  },
  tagline: {
    textAlign: 'center',
    fontFamily: 'System',
    marginTop: 4,
  },
});

export default FoodWayLogo;
