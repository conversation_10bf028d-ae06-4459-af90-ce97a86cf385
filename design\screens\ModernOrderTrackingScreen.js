// Modern Order Tracking Screen Design for FoodWay App
// Optimized for performance and visual appeal

import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useEffect, useState } from 'react';
import {
    Animated,
    Image,
    Linking,
    SafeAreaView,
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import DesignSystem from '../DesignSystem';
import { ModernButton } from '../ModernComponents';

const { Colors, Typography, Spacing, BorderRadius, Shadows, Layout } = DesignSystem;

const ModernOrderTrackingScreen = ({ navigation, route }) => {
  const { orderId, items, total } = route.params;
  const [currentStatus, setCurrentStatus] = useState('confirmed');
  const [estimatedTime, setEstimatedTime] = useState(35);
  const [progressAnim] = useState(new Animated.Value(0));

  // Sample order statuses
  const orderStatuses = [
    {
      id: 'confirmed',
      title: 'Order Confirmed',
      subtitle: 'Restaurant is preparing your order',
      icon: 'checkmark-circle',
      time: '2 min ago',
      completed: true,
    },
    {
      id: 'preparing',
      title: 'Preparing Food',
      subtitle: 'Your delicious meal is being prepared',
      icon: 'restaurant',
      time: 'In progress',
      completed: true,
    },
    {
      id: 'ready',
      title: 'Ready for Pickup',
      subtitle: 'Rider is on the way to restaurant',
      icon: 'bag-check',
      time: 'Current',
      completed: false,
    },
    {
      id: 'picked',
      title: 'Order Picked Up',
      subtitle: 'Rider has picked up your order',
      icon: 'bicycle',
      time: 'Pending',
      completed: false,
    },
    {
      id: 'delivered',
      title: 'Delivered',
      subtitle: 'Enjoy your meal!',
      icon: 'home',
      time: 'Pending',
      completed: false,
    },
  ];

  // Sample rider data
  const riderInfo = {
    name: 'Ahmed Khan',
    phone: '+92 300 1234567',
    rating: 4.8,
    image: 'https://via.placeholder.com/60x60',
    vehicle: 'Motorcycle',
    plateNumber: 'KHI-1234',
  };

  useEffect(() => {
    // Animate progress bar
    const currentIndex = orderStatuses.findIndex(status => status.id === currentStatus);
    const progress = (currentIndex + 1) / orderStatuses.length;
    
    Animated.timing(progressAnim, {
      toValue: progress,
      duration: 1000,
      useNativeDriver: false,
    }).start();

    // Simulate order status updates
    const timer = setTimeout(() => {
      if (currentStatus === 'confirmed') {
        setCurrentStatus('preparing');
      } else if (currentStatus === 'preparing') {
        setCurrentStatus('ready');
      }
    }, 10000);

    return () => clearTimeout(timer);
  }, [currentStatus]);

  const handleCallRider = () => {
    Linking.openURL(`tel:${riderInfo.phone}`);
  };

  const handleChatRider = () => {
    // Navigate to chat screen
    navigation.navigate('Chat', { rider: riderInfo });
  };

  const handleViewMap = () => {
    // Navigate to map view
    navigation.navigate('DeliveryMap', { orderId });
  };

  const getStatusColor = (status) => {
    if (status.completed) return Colors.secondary;
    if (status.id === currentStatus) return Colors.primary;
    return Colors.textLight;
  };

  const getStatusIcon = (status) => {
    if (status.completed) return 'checkmark-circle';
    if (status.id === currentStatus) return status.icon;
    return 'ellipse-outline';
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.textPrimary} />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Order Tracking</Text>
          <Text style={styles.orderNumber}>#{orderId}</Text>
        </View>
        <TouchableOpacity
          style={styles.helpButton}
          onPress={() => navigation.navigate('Help')}
        >
          <Ionicons name="help-circle-outline" size={24} color={Colors.textSecondary} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Estimated Time Card */}
        <View style={styles.timeCard}>
          <LinearGradient
            colors={[Colors.primary, Colors.primaryDark]}
            style={styles.timeCardGradient}
          >
            <View style={styles.timeCardContent}>
              <Text style={styles.timeCardTitle}>Estimated Delivery Time</Text>
              <Text style={styles.timeCardTime}>{estimatedTime} minutes</Text>
              <Text style={styles.timeCardSubtitle}>
                Your order will arrive soon!
              </Text>
            </View>
            <View style={styles.timeCardIcon}>
              <Ionicons name="time" size={40} color={Colors.textWhite} />
            </View>
          </LinearGradient>
        </View>

        {/* Progress Bar */}
        <View style={styles.progressSection}>
          <View style={styles.progressBarContainer}>
            <View style={styles.progressBarBackground} />
            <Animated.View
              style={[
                styles.progressBarFill,
                {
                  width: progressAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                },
              ]}
            />
          </View>
        </View>

        {/* Order Status Timeline */}
        <View style={styles.timelineSection}>
          <Text style={styles.sectionTitle}>Order Status</Text>
          {orderStatuses.map((status, index) => (
            <View key={status.id} style={styles.timelineItem}>
              <View style={styles.timelineLeft}>
                <View
                  style={[
                    styles.timelineIcon,
                    { backgroundColor: getStatusColor(status) },
                  ]}
                >
                  <Ionicons
                    name={getStatusIcon(status)}
                    size={20}
                    color={Colors.textWhite}
                  />
                </View>
                {index < orderStatuses.length - 1 && (
                  <View
                    style={[
                      styles.timelineLine,
                      {
                        backgroundColor: status.completed
                          ? Colors.secondary
                          : Colors.border,
                      },
                    ]}
                  />
                )}
              </View>
              <View style={styles.timelineContent}>
                <Text
                  style={[
                    styles.timelineTitle,
                    {
                      color: status.completed || status.id === currentStatus
                        ? Colors.textPrimary
                        : Colors.textSecondary,
                    },
                  ]}
                >
                  {status.title}
                </Text>
                <Text style={styles.timelineSubtitle}>{status.subtitle}</Text>
                <Text style={styles.timelineTime}>{status.time}</Text>
              </View>
            </View>
          ))}
        </View>

        {/* Rider Information */}
        {currentStatus !== 'confirmed' && (
          <View style={styles.riderSection}>
            <Text style={styles.sectionTitle}>Delivery Partner</Text>
            <View style={styles.riderCard}>
              <View style={styles.riderInfo}>
                <Image
                  source={{ uri: riderInfo.image }}
                  style={styles.riderImage}
                />
                <View style={styles.riderDetails}>
                  <Text style={styles.riderName}>{riderInfo.name}</Text>
                  <View style={styles.riderRating}>
                    <Ionicons name="star" size={14} color={Colors.rating} />
                    <Text style={styles.riderRatingText}>{riderInfo.rating}</Text>
                  </View>
                  <Text style={styles.riderVehicle}>
                    {riderInfo.vehicle} • {riderInfo.plateNumber}
                  </Text>
                </View>
              </View>
              
              <View style={styles.riderActions}>
                <TouchableOpacity
                  style={styles.riderActionButton}
                  onPress={handleCallRider}
                >
                  <Ionicons name="call" size={20} color={Colors.primary} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.riderActionButton}
                  onPress={handleChatRider}
                >
                  <Ionicons name="chatbubble" size={20} color={Colors.primary} />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}

        {/* Order Items */}
        <View style={styles.itemsSection}>
          <Text style={styles.sectionTitle}>Order Items</Text>
          <View style={styles.itemsCard}>
            {items.map((item) => (
              <View key={item.id} style={styles.orderItem}>
                <Image
                  source={{ uri: item.image }}
                  style={styles.itemImage}
                />
                <View style={styles.itemDetails}>
                  <Text style={styles.itemName}>{item.name}</Text>
                  <Text style={styles.itemRestaurant}>{item.restaurant}</Text>
                  <Text style={styles.itemPrice}>
                    {item.quantity}x ₨{item.price}
                  </Text>
                </View>
              </View>
            ))}
            
            <View style={styles.orderTotal}>
              <Text style={styles.totalLabel}>Total Amount</Text>
              <Text style={styles.totalValue}>₨{total}</Text>
            </View>
          </View>
        </View>

        {/* Map View Button */}
        <View style={styles.mapSection}>
          <ModernButton
            title="View Live Tracking"
            icon="map"
            onPress={handleViewMap}
            style={styles.mapButton}
          />
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Help Button */}
      <View style={styles.helpSection}>
        <TouchableOpacity
          style={styles.helpFloatingButton}
          onPress={() => navigation.navigate('Help')}
        >
          <Ionicons name="headset" size={24} color={Colors.textWhite} />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.surface,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.base,
    paddingVertical: Spacing.base,
    backgroundColor: Colors.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.textPrimary,
  },
  orderNumber: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  helpButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  timeCard: {
    margin: Spacing.base,
    borderRadius: BorderRadius.lg,
    overflow: 'hidden',
    ...Shadows.md,
  },
  timeCardGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.lg,
  },
  timeCardContent: {
    flex: 1,
  },
  timeCardTitle: {
    fontSize: Typography.fontSize.base,
    color: Colors.textWhite,
    marginBottom: 4,
  },
  timeCardTime: {
    fontSize: Typography.fontSize['3xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.textWhite,
    marginBottom: 4,
  },
  timeCardSubtitle: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textWhite,
    opacity: 0.9,
  },
  timeCardIcon: {
    marginLeft: Spacing.base,
  },
  progressSection: {
    paddingHorizontal: Spacing.base,
    marginBottom: Spacing.lg,
  },
  progressBarContainer: {
    height: 6,
    backgroundColor: Colors.border,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressBarBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.border,
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: Colors.secondary,
    borderRadius: 3,
  },
  timelineSection: {
    paddingHorizontal: Spacing.base,
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.textPrimary,
    marginBottom: Spacing.base,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: Spacing.base,
  },
  timelineLeft: {
    alignItems: 'center',
    marginRight: Spacing.base,
  },
  timelineIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  timelineLine: {
    width: 2,
    flex: 1,
    marginTop: Spacing.sm,
  },
  timelineContent: {
    flex: 1,
    paddingBottom: Spacing.base,
  },
  timelineTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: 4,
  },
  timelineSubtitle: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
    marginBottom: 4,
  },
  timelineTime: {
    fontSize: Typography.fontSize.xs,
    color: Colors.primary,
    fontWeight: Typography.fontWeight.medium,
  },
  riderSection: {
    paddingHorizontal: Spacing.base,
    marginBottom: Spacing.xl,
  },
  riderCard: {
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.md,
    padding: Spacing.base,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    ...Shadows.sm,
  },
  riderInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  riderImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: Spacing.base,
  },
  riderDetails: {
    flex: 1,
  },
  riderName: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  riderRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  riderRatingText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
    marginLeft: 4,
  },
  riderVehicle: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
  },
  riderActions: {
    flexDirection: 'row',
  },
  riderActionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.primaryAlpha,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: Spacing.sm,
  },
  itemsSection: {
    paddingHorizontal: Spacing.base,
    marginBottom: Spacing.xl,
  },
  itemsCard: {
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.md,
    padding: Spacing.base,
    ...Shadows.sm,
  },
  orderItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  itemImage: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.sm,
    marginRight: Spacing.base,
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.textPrimary,
    marginBottom: 2,
  },
  itemRestaurant: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
    marginBottom: 2,
  },
  itemPrice: {
    fontSize: Typography.fontSize.sm,
    color: Colors.primary,
    fontWeight: Typography.fontWeight.semibold,
  },
  orderTotal: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: Spacing.base,
    marginTop: Spacing.sm,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  totalLabel: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.textPrimary,
  },
  totalValue: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
  },
  mapSection: {
    paddingHorizontal: Spacing.base,
    marginBottom: Spacing.xl,
  },
  mapButton: {
    backgroundColor: Colors.secondary,
  },
  helpSection: {
    position: 'absolute',
    bottom: Spacing.xl,
    right: Spacing.base,
  },
  helpFloatingButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    ...Shadows.lg,
  },
  bottomSpacing: {
    height: 100,
  },
});
