import { 
  mockDelay, 
  validateMockCredentials, 
  findUserByEmail, 
  mockTokens, 
  defaultMockUser,
  getUserAddresses,
  getUserPaymentMethods,
  mockCategories
} from './mockData';
import { 
  mockRestaurants, 
  getRestaurantById, 
  getMenuItemsByRestaurant, 
  searchRestaurants,
  getRestaurantsByCategory,
  getFeaturedRestaurants,
  getPromotedRestaurants
} from './mockRestaurants';
import { 
  getUserOrders, 
  getOrderById, 
  getActiveOrders, 
  getOrderHistory, 
  createMockOrder,
  updateOrderStatus,
  simulateOrderProgress
} from './mockOrders';
import { User, Restaurant, Order, Category, MenuItem, Address, PaymentMethod, LoginForm, RegisterForm } from '../../types';

// Configuration
export const MOCK_API_CONFIG = {
  enabled: true, // Set to false to use real API
  simulateNetworkDelay: true,
  minDelay: 300,
  maxDelay: 1200,
  failureRate: 0.02, // 2% chance of simulated failures
};

// Simulate network delay
const delay = () => MOCK_API_CONFIG.simulateNetworkDelay 
  ? mockDelay(MOCK_API_CONFIG.minDelay, MOCK_API_CONFIG.maxDelay) 
  : Promise.resolve();

// Simulate random failures
const shouldSimulateFailure = () => Math.random() < MOCK_API_CONFIG.failureRate;

// Mock API Response wrapper
interface MockApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

const createSuccessResponse = <T>(data: T, message?: string): MockApiResponse<T> => ({
  success: true,
  data,
  message,
});

const createErrorResponse = (error: string): MockApiResponse<any> => ({
  success: false,
  error,
});

// Authentication API
export const mockAuthApi = {
  async login(credentials: LoginForm): Promise<MockApiResponse<{ user: User; tokens: typeof mockTokens }>> {
    await delay();
    
    if (shouldSimulateFailure()) {
      return createErrorResponse('Network error occurred');
    }

    const user = validateMockCredentials(credentials.email, credentials.password);
    
    if (!user) {
      return createErrorResponse('Invalid email or password');
    }

    return createSuccessResponse({
      user,
      tokens: mockTokens,
    }, 'Login successful');
  },

  async register(userData: RegisterForm): Promise<MockApiResponse<{ user: User; tokens: typeof mockTokens }>> {
    await delay();
    
    if (shouldSimulateFailure()) {
      return createErrorResponse('Network error occurred');
    }

    // Check if user already exists
    const existingUser = findUserByEmail(userData.email);
    if (existingUser) {
      return createErrorResponse('User with this email already exists');
    }

    // Create new user
    const newUser: User = {
      id: `user_${Date.now()}`,
      email: userData.email,
      firstName: userData.firstName,
      lastName: userData.lastName,
      phone: userData.phone || '',
      avatar: '',
      dateOfBirth: userData.dateOfBirth || '',
      isEmailVerified: false,
      isPhoneVerified: false,
      preferences: {
        notifications: {
          orderUpdates: true,
          promotions: true,
          newRestaurants: false,
        },
        dietary: [],
        cuisines: [],
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return createSuccessResponse({
      user: newUser,
      tokens: mockTokens,
    }, 'Registration successful');
  },

  async logout(): Promise<MockApiResponse<null>> {
    await delay();
    return createSuccessResponse(null, 'Logout successful');
  },

  async refreshToken(): Promise<MockApiResponse<typeof mockTokens>> {
    await delay();
    return createSuccessResponse(mockTokens, 'Token refreshed');
  },

  async getCurrentUser(): Promise<MockApiResponse<User>> {
    await delay();
    return createSuccessResponse(defaultMockUser, 'User data retrieved');
  },
};

// Restaurant API
export const mockRestaurantApi = {
  async getRestaurants(): Promise<MockApiResponse<Restaurant[]>> {
    await delay();
    
    if (shouldSimulateFailure()) {
      return createErrorResponse('Failed to load restaurants');
    }

    return createSuccessResponse(mockRestaurants, 'Restaurants loaded successfully');
  },

  async getRestaurantById(id: string): Promise<MockApiResponse<Restaurant>> {
    await delay();
    
    const restaurant = getRestaurantById(id);
    if (!restaurant) {
      return createErrorResponse('Restaurant not found');
    }

    return createSuccessResponse(restaurant, 'Restaurant details loaded');
  },

  async getMenuItems(restaurantId: string): Promise<MockApiResponse<MenuItem[]>> {
    await delay();
    
    const menuItems = getMenuItemsByRestaurant(restaurantId);
    return createSuccessResponse(menuItems, 'Menu items loaded');
  },

  async searchRestaurants(query: string): Promise<MockApiResponse<Restaurant[]>> {
    await delay();
    
    const results = searchRestaurants(query);
    return createSuccessResponse(results, `Found ${results.length} restaurants`);
  },

  async getRestaurantsByCategory(category: string): Promise<MockApiResponse<Restaurant[]>> {
    await delay();
    
    const results = getRestaurantsByCategory(category);
    return createSuccessResponse(results, `Found ${results.length} restaurants in ${category}`);
  },

  async getFeaturedRestaurants(): Promise<MockApiResponse<Restaurant[]>> {
    await delay();
    
    const featured = getFeaturedRestaurants();
    return createSuccessResponse(featured, 'Featured restaurants loaded');
  },

  async getPromotedRestaurants(): Promise<MockApiResponse<Restaurant[]>> {
    await delay();
    
    const promoted = getPromotedRestaurants();
    return createSuccessResponse(promoted, 'Promoted restaurants loaded');
  },

  async getCategories(): Promise<MockApiResponse<Category[]>> {
    await delay();
    
    return createSuccessResponse(mockCategories, 'Categories loaded');
  },
};

// Order API
export const mockOrderApi = {
  async getUserOrders(userId: string): Promise<MockApiResponse<Order[]>> {
    await delay();
    
    const orders = getUserOrders(userId);
    return createSuccessResponse(orders, 'Orders loaded successfully');
  },

  async getOrderById(orderId: string): Promise<MockApiResponse<Order>> {
    await delay();
    
    const order = getOrderById(orderId);
    if (!order) {
      return createErrorResponse('Order not found');
    }

    return createSuccessResponse(order, 'Order details loaded');
  },

  async getActiveOrders(userId: string): Promise<MockApiResponse<Order[]>> {
    await delay();
    
    const activeOrders = getActiveOrders(userId);
    return createSuccessResponse(activeOrders, 'Active orders loaded');
  },

  async getOrderHistory(userId: string): Promise<MockApiResponse<Order[]>> {
    await delay();
    
    const history = getOrderHistory(userId);
    return createSuccessResponse(history, 'Order history loaded');
  },

  async createOrder(orderData: Partial<Order>): Promise<MockApiResponse<Order>> {
    await delay();
    
    if (shouldSimulateFailure()) {
      return createErrorResponse('Failed to create order');
    }

    const newOrder = createMockOrder(orderData);
    
    // Start simulating order progress
    setTimeout(() => {
      simulateOrderProgress(newOrder.id);
    }, 2000);

    return createSuccessResponse(newOrder, 'Order created successfully');
  },

  async cancelOrder(orderId: string): Promise<MockApiResponse<Order>> {
    await delay();
    
    const updatedOrder = updateOrderStatus(orderId, 'cancelled', 'Order cancelled by customer');
    if (!updatedOrder) {
      return createErrorResponse('Order not found or cannot be cancelled');
    }

    return createSuccessResponse(updatedOrder, 'Order cancelled successfully');
  },

  async rateOrder(orderId: string, rating: number, review?: string): Promise<MockApiResponse<Order>> {
    await delay();
    
    const order = getOrderById(orderId);
    if (!order) {
      return createErrorResponse('Order not found');
    }

    order.rating = rating;
    order.review = review || '';

    return createSuccessResponse(order, 'Order rated successfully');
  },
};

// User API
export const mockUserApi = {
  async getUserAddresses(userId: string): Promise<MockApiResponse<Address[]>> {
    await delay();
    
    const addresses = getUserAddresses(userId);
    return createSuccessResponse(addresses, 'Addresses loaded');
  },

  async getUserPaymentMethods(userId: string): Promise<MockApiResponse<PaymentMethod[]>> {
    await delay();
    
    const paymentMethods = getUserPaymentMethods(userId);
    return createSuccessResponse(paymentMethods, 'Payment methods loaded');
  },

  async updateUserProfile(userId: string, updates: Partial<User>): Promise<MockApiResponse<User>> {
    await delay();
    
    // In a real app, this would update the user in the database
    const updatedUser = { ...defaultMockUser, ...updates };
    return createSuccessResponse(updatedUser, 'Profile updated successfully');
  },
};

// Main Mock API Service
export const mockApiService = {
  auth: mockAuthApi,
  restaurants: mockRestaurantApi,
  orders: mockOrderApi,
  user: mockUserApi,
  
  // Utility methods
  isEnabled: () => MOCK_API_CONFIG.enabled,
  
  setEnabled: (enabled: boolean) => {
    MOCK_API_CONFIG.enabled = enabled;
  },
  
  setFailureRate: (rate: number) => {
    MOCK_API_CONFIG.failureRate = Math.max(0, Math.min(1, rate));
  },
  
  setNetworkDelay: (enabled: boolean, minDelay = 300, maxDelay = 1200) => {
    MOCK_API_CONFIG.simulateNetworkDelay = enabled;
    MOCK_API_CONFIG.minDelay = minDelay;
    MOCK_API_CONFIG.maxDelay = maxDelay;
  },
};

export default mockApiService;
