// Integrated Design System for FoodWay App
// Merges modern design tokens with existing constants for seamless integration

import { Dimensions, Platform } from 'react-native';
import { 
  COLORS as EXISTING_COLORS, 
  TYPOGRAPHY as EXISTING_TYPOGRAPHY, 
  SPACING as EXISTING_SPACING,
  BORDER_RADIUS as EXISTING_BORDER_RADIUS,
  SHADOWS as EXISTING_SHADOWS,
  ANIMATION as EXISTING_ANIMATION
} from '../utils/constants';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// 🎨 ENHANCED COLOR PALETTE
// Extends existing colors with modern design tokens
export const Colors = {
  // Keep existing colors for compatibility
  ...EXISTING_COLORS,
  
  // Enhanced primary colors with alpha variants
  primaryAlpha: 'rgba(255, 107, 53, 0.1)',
  primaryDark: '#E55A2B',
  primaryLight: '#FF8A65',
  
  // Enhanced secondary colors
  secondaryAlpha: 'rgba(46, 204, 113, 0.1)',
  
  // Modern surface colors
  surface: '#F8F9FA',
  surfaceElevated: '#FFFFFF',
  
  // Enhanced text colors
  textWhite: '#FFFFFF',
  textLight: '#BDBDBD',
  
  // Status colors with alpha variants
  successAlpha: 'rgba(76, 175, 80, 0.1)',
  warningAlpha: 'rgba(255, 152, 0, 0.1)',
  errorAlpha: 'rgba(244, 67, 54, 0.1)',
  
  // Food-specific colors
  rating: '#FFB400',
  discount: '#E91E63',
  veg: '#4CAF50',
  nonVeg: '#F44336',
  
  // Gradient colors
  gradientStart: '#FF6B35',
  gradientEnd: '#E55A2B',
};

// 📝 ENHANCED TYPOGRAPHY
// Extends existing typography with modern font weights and responsive sizing
export const Typography = {
  // Keep existing typography for compatibility
  ...EXISTING_TYPOGRAPHY,
  
  // Enhanced font weights
  fontWeight: {
    light: '300',
    regular: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },
  
  // Responsive font sizes based on screen width
  responsiveFontSize: {
    xs: screenWidth < 375 ? 11 : 12,
    sm: screenWidth < 375 ? 13 : 14,
    base: screenWidth < 375 ? 15 : 16,
    lg: screenWidth < 375 ? 17 : 18,
    xl: screenWidth < 375 ? 19 : 20,
    '2xl': screenWidth < 375 ? 22 : 24,
    '3xl': screenWidth < 375 ? 28 : 30,
  },
  
  // Line heights for better readability
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },
};

// 📏 ENHANCED SPACING
// Extends existing spacing with additional values
export const Spacing = {
  // Keep existing spacing for compatibility
  ...EXISTING_SPACING,
  
  // Additional spacing values
  xxs: 2,
  '4xl': 80,
  '5xl': 96,
  
  // Component-specific spacing
  cardPadding: 16,
  sectionSpacing: 24,
  screenPadding: 16,
};

// 🔲 ENHANCED BORDER RADIUS
// Extends existing border radius with modern values
export const BorderRadius = {
  // Keep existing border radius for compatibility
  ...EXISTING_BORDER_RADIUS,
  
  // Additional modern radius values
  xs: 2,
  '3xl': 32,
  '4xl': 40,
};

// 🌟 ENHANCED SHADOWS
// Extends existing shadows with modern elevation system
export const Shadows = {
  // Keep existing shadows for compatibility
  ...EXISTING_SHADOWS,
  
  // Additional shadow levels
  xs: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.03,
    shadowRadius: 1,
    elevation: 1,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  '2xl': {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.25,
    shadowRadius: 24,
    elevation: 12,
  },
};

// 🎭 ENHANCED ANIMATIONS
// Extends existing animations with modern timing and easing
export const Animations = {
  // Keep existing animations for compatibility
  timing: {
    ...EXISTING_ANIMATION,
    extraFast: 100,
    extraSlow: 800,
  },
  
  // Modern easing curves
  easing: {
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },
  
  // Spring configurations
  spring: {
    tension: 100,
    friction: 8,
    useNativeDriver: true,
  },
  
  // Stagger timing
  stagger: {
    fast: 50,
    normal: 100,
    slow: 150,
  },
};

// 📱 LAYOUT SYSTEM
// Responsive layout utilities
export const Layout = {
  // Screen breakpoints
  breakpoints: {
    small: 375,
    medium: 414,
    large: 768,
  },
  
  // Container widths
  container: {
    small: screenWidth - 32,
    medium: Math.min(screenWidth - 32, 600),
    large: Math.min(screenWidth - 32, 800),
  },
  
  // Grid system
  grid: {
    columns: 12,
    gutter: 16,
  },
  
  // Common dimensions
  dimensions: {
    headerHeight: 60,
    tabBarHeight: 80,
    buttonHeight: 48,
    inputHeight: 48,
    cardMinHeight: 120,
  },
};

// 🎯 COMPONENT VARIANTS
// Pre-defined component styling variants
export const ComponentVariants = {
  button: {
    primary: {
      backgroundColor: Colors.primary,
      borderColor: Colors.primary,
    },
    secondary: {
      backgroundColor: Colors.secondary,
      borderColor: Colors.secondary,
    },
    outline: {
      backgroundColor: 'transparent',
      borderColor: Colors.primary,
      borderWidth: 1,
    },
    ghost: {
      backgroundColor: 'transparent',
      borderColor: 'transparent',
    },
  },
  
  card: {
    default: {
      backgroundColor: Colors.surface,
      borderRadius: BorderRadius.md,
      ...Shadows.sm,
    },
    elevated: {
      backgroundColor: Colors.surfaceElevated,
      borderRadius: BorderRadius.lg,
      ...Shadows.md,
    },
    outlined: {
      backgroundColor: Colors.surface,
      borderRadius: BorderRadius.md,
      borderWidth: 1,
      borderColor: Colors.border,
    },
  },
  
  input: {
    default: {
      backgroundColor: Colors.surface,
      borderRadius: BorderRadius.md,
      borderWidth: 1,
      borderColor: Colors.border,
    },
    focused: {
      borderColor: Colors.primary,
      borderWidth: 2,
    },
    error: {
      borderColor: Colors.error,
      borderWidth: 1,
    },
  },
};

// ⚡ PERFORMANCE OPTIMIZATIONS
// Configuration for optimal React Native performance
export const Performance = {
  // FlatList optimizations
  listProps: {
    removeClippedSubviews: true,
    maxToRenderPerBatch: 10,
    windowSize: 10,
    initialNumToRender: 8,
    updateCellsBatchingPeriod: 50,
    scrollEventThrottle: 16,
  },
  
  // Image optimizations
  imageProps: {
    resizeMode: 'cover',
    cache: 'force-cache',
    priority: 'normal',
  },
  
  // Animation optimizations
  animationProps: {
    useNativeDriver: true,
    isInteraction: false,
  },
  
  // Memory management
  memory: {
    imageCacheLimit: 50,
    imageCacheAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  },
};

// 🔧 UTILITY FUNCTIONS
// Helper functions for responsive design and calculations
export const Utils = {
  // Responsive value based on screen width
  responsive: (small, medium, large) => {
    if (screenWidth < Layout.breakpoints.small) return small;
    if (screenWidth < Layout.breakpoints.medium) return medium;
    return large || medium;
  },
  
  // Scale value based on screen width
  scale: (size) => {
    const scale = screenWidth / 375; // Base width
    return Math.round(size * scale);
  },
  
  // Get appropriate shadow for elevation
  getShadow: (elevation) => {
    const shadowMap = {
      1: Shadows.xs,
      2: Shadows.sm,
      3: Shadows.md,
      4: Shadows.lg,
      5: Shadows.xl,
      6: Shadows['2xl'],
    };
    return shadowMap[elevation] || Shadows.sm;
  },
  
  // Platform-specific values
  platform: (ios, android) => {
    return Platform.OS === 'ios' ? ios : android;
  },
  
  // Safe area calculations
  safeArea: {
    top: Platform.OS === 'ios' ? 44 : 0,
    bottom: Platform.OS === 'ios' ? 34 : 0,
  },
};

// Export the complete integrated design system
export default {
  Colors,
  Typography,
  Spacing,
  BorderRadius,
  Shadows,
  Animations,
  Layout,
  ComponentVariants,
  Performance,
  Utils,
};
