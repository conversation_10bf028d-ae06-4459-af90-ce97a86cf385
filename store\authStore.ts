import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { axiosInstance } from '../services/config/axios';
import { LoginForm, RegisterForm, User } from '../types';
import { STORAGE_KEYS } from '../utils/constants';

// Configuration for mock mode
const USE_MOCK_API = true; // Set to false to use real API

interface AuthState {
  // State
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  login: (credentials: LoginForm) => Promise<void>;
  register: (userData: RegisterForm) => Promise<void>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  updateProfile: (userData: Partial<User>) => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;

  // Token management
  setTokens: (token: string, refreshToken: string) => Promise<void>;
  getToken: () => Promise<string | null>;
  clearTokens: () => Promise<void>;
}

// Secure token storage functions
const storeToken = async (key: string, token: string) => {
  try {
    await SecureStore.setItemAsync(key, token);
  } catch (error) {
    console.error('Error storing token:', error);
  }
};

const getStoredToken = async (key: string): Promise<string | null> => {
  try {
    return await SecureStore.getItemAsync(key);
  } catch (error) {
    console.error('Error getting token:', error);
    return null;
  }
};

const deleteStoredToken = async (key: string) => {
  try {
    await SecureStore.deleteItemAsync(key);
  } catch (error) {
    console.error('Error deleting token:', error);
  }
};

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials: LoginForm) => {
        set({ isLoading: true, error: null });

        try {
          let response;

          if (USE_MOCK_API) {
            // Use mock API
            const { mockApiService } = await import('../services/mock/mockApiService');
            response = await mockApiService.auth.login(credentials);

            if (!response.success) {
              throw new Error(response.error || 'Login failed');
            }

            const { user, tokens } = response.data!;
            const { accessToken: token, refreshToken } = tokens;

            // Store tokens securely
            await storeToken(STORAGE_KEYS.AUTH_TOKEN, token);
            await storeToken(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);

            set({
              user,
              token,
              refreshToken,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
          } else {
            // Use real API
            const apiResponse = await axiosInstance.post('/auth/login', credentials, {
              headers: { skipAuth: true } as any
            });

            const { user, token, refreshToken } = apiResponse.data.data;

            // Store tokens securely
            await storeToken(STORAGE_KEYS.AUTH_TOKEN, token);
            await storeToken(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);

            set({
              user,
              token,
              refreshToken,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Login failed',
            isLoading: false,
          });
          throw error;
        }
      },

      register: async (userData: RegisterForm) => {
        set({ isLoading: true, error: null });

        try {
          if (USE_MOCK_API) {
            // Use mock API
            const { mockApiService } = await import('../services/mock/mockApiService');
            const response = await mockApiService.auth.register(userData);

            if (!response.success) {
              throw new Error(response.error || 'Registration failed');
            }

            const { user, tokens } = response.data!;
            const { accessToken: token, refreshToken } = tokens;

            // Store tokens securely
            await storeToken(STORAGE_KEYS.AUTH_TOKEN, token);
            await storeToken(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);

            set({
              user,
              token,
              refreshToken,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
          } else {
            // Use real API
            const response = await axiosInstance.post('/auth/register', userData, {
              headers: { skipAuth: true } as any
            });

            const { user, token, refreshToken } = response.data.data;

            // Store tokens securely
            await storeToken(STORAGE_KEYS.AUTH_TOKEN, token);
            await storeToken(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);

            set({
              user,
              token,
              refreshToken,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Registration failed',
            isLoading: false,
          });
          throw error;
        }
      },

      logout: async () => {
        set({ isLoading: true });

        try {
          if (USE_MOCK_API) {
            // Use mock API
            const { mockApiService } = await import('../services/mock/mockApiService');
            await mockApiService.auth.logout();
          } else {
            // Use real API
            const token = await getStoredToken(STORAGE_KEYS.AUTH_TOKEN);

            if (token) {
              // Call logout endpoint
              await fetch(`${process.env.EXPO_PUBLIC_API_URL}/auth/logout`, {
                method: 'POST',
                headers: {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json',
                },
              });
            }
          }
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          // Clear tokens and state regardless of API call success
          await deleteStoredToken(STORAGE_KEYS.AUTH_TOKEN);
          await deleteStoredToken(STORAGE_KEYS.REFRESH_TOKEN);

          set({
            user: null,
            token: null,
            refreshToken: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        }
      },

      refreshAuth: async () => {
        const refreshToken = await getStoredToken(STORAGE_KEYS.REFRESH_TOKEN);

        if (!refreshToken) {
          set({ isAuthenticated: false });
          return;
        }

        try {
          if (USE_MOCK_API) {
            // Use mock API
            const { mockApiService } = await import('../services/mock/mockApiService');
            const response = await mockApiService.auth.refreshToken();

            if (!response.success) {
              throw new Error(response.error || 'Token refresh failed');
            }

            const { accessToken: newToken, refreshToken: newRefreshToken } = response.data!;
            const { mockApiService: mockService } = await import('../services/mock/mockApiService');
            const userResponse = await mockService.auth.getCurrentUser();
            const user = userResponse.data!;

            await storeToken(STORAGE_KEYS.AUTH_TOKEN, newToken);
            await storeToken(STORAGE_KEYS.REFRESH_TOKEN, newRefreshToken);

            set({
              user,
              token: newToken,
              refreshToken: newRefreshToken,
              isAuthenticated: true,
            });
          } else {
            // Use real API
            const response = await axiosInstance.post('/auth/refresh', { refreshToken }, {
              headers: { skipAuth: true } as any
            });

            const { token: newToken, refreshToken: newRefreshToken, user } = response.data.data;

            await storeToken(STORAGE_KEYS.AUTH_TOKEN, newToken);
            await storeToken(STORAGE_KEYS.REFRESH_TOKEN, newRefreshToken);

            set({
              user,
              token: newToken,
              refreshToken: newRefreshToken,
              isAuthenticated: true,
            });
          }
        } catch (error) {
          console.error('Token refresh failed:', error);
          get().logout();
        }
      },

      updateProfile: async (userData: Partial<User>) => {
        set({ isLoading: true, error: null });

        try {
          if (USE_MOCK_API) {
            // Use mock API
            const { mockApiService } = await import('../services/mock/mockApiService');
            const currentUser = get().user;
            if (!currentUser) throw new Error('No user logged in');

            const response = await mockApiService.user.updateUserProfile(currentUser.id, userData);

            if (!response.success) {
              throw new Error(response.error || 'Profile update failed');
            }

            const updatedUser = response.data!;

            set({
              user: { ...get().user, ...updatedUser },
              isLoading: false,
              error: null,
            });
          } else {
            // Use real API
            const response = await axiosInstance.put('/user/profile', userData);

            const updatedUser = response.data.data;

            set({
              user: { ...get().user, ...updatedUser },
              isLoading: false,
              error: null,
            });
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Profile update failed',
            isLoading: false,
          });
          throw error;
        }
      },

      clearError: () => set({ error: null }),

      setLoading: (loading: boolean) => set({ isLoading: loading }),

      setTokens: async (token: string, refreshToken: string) => {
        await storeToken(STORAGE_KEYS.AUTH_TOKEN, token);
        await storeToken(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
        set({ token, refreshToken });
      },

      getToken: async () => {
        return await getStoredToken(STORAGE_KEYS.AUTH_TOKEN);
      },

      clearTokens: async () => {
        await deleteStoredToken(STORAGE_KEYS.AUTH_TOKEN);
        await deleteStoredToken(STORAGE_KEYS.REFRESH_TOKEN);
        set({ token: null, refreshToken: null });
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Initialize auth state on app start
export const initializeAuth = async () => {
  const token = await getStoredToken(STORAGE_KEYS.AUTH_TOKEN);
  const refreshToken = await getStoredToken(STORAGE_KEYS.REFRESH_TOKEN);

  if (token && refreshToken) {
    useAuthStore.setState({ token, refreshToken });
    // Attempt to refresh the token to validate it
    await useAuthStore.getState().refreshAuth();
  }
};
