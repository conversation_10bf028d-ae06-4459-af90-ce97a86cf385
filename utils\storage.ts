import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { STORAGE_KEYS, ERROR_TYPES } from './constants';
import { AppError, errorHandler } from './errorHandler';

// Storage error types
export class StorageError extends AppError {
  constructor(message: string, operation: string, key?: string) {
    super(
      ERROR_TYPES.STORAGE_ERROR,
      `Storage ${operation} failed: ${message}`,
      `Unable to ${operation} data. Please try again.`,
      'STORAGE_ERROR',
      undefined,
      true
    );
    this.operation = operation;
    this.key = key;
  }

  public readonly operation: string;
  public readonly key?: string;
}

export class SecureStorageError extends StorageError {
  constructor(message: string, operation: string, key?: string) {
    super(message, operation, key);
    this.name = 'SecureStorageError';
  }
}

// Storage interface for consistent error handling
interface StorageInterface {
  getItem(key: string): Promise<string | null>;
  setItem(key: string, value: string): Promise<void>;
  removeItem(key: string): Promise<void>;
  clear(): Promise<void>;
}

// Enhanced AsyncStorage wrapper
class EnhancedAsyncStorage implements StorageInterface {
  async getItem(key: string): Promise<string | null> {
    try {
      const value = await AsyncStorage.getItem(key);
      return value;
    } catch (error) {
      const storageError = new StorageError(
        error instanceof Error ? error.message : 'Unknown error',
        'read',
        key
      );
      errorHandler.handleError(storageError, `AsyncStorage.getItem.${key}`);
      throw storageError;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      await AsyncStorage.setItem(key, value);
    } catch (error) {
      const storageError = new StorageError(
        error instanceof Error ? error.message : 'Unknown error',
        'write',
        key
      );
      errorHandler.handleError(storageError, `AsyncStorage.setItem.${key}`);
      throw storageError;
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(key);
    } catch (error) {
      const storageError = new StorageError(
        error instanceof Error ? error.message : 'Unknown error',
        'delete',
        key
      );
      errorHandler.handleError(storageError, `AsyncStorage.removeItem.${key}`);
      throw storageError;
    }
  }

  async clear(): Promise<void> {
    try {
      await AsyncStorage.clear();
    } catch (error) {
      const storageError = new StorageError(
        error instanceof Error ? error.message : 'Unknown error',
        'clear'
      );
      errorHandler.handleError(storageError, 'AsyncStorage.clear');
      throw storageError;
    }
  }

  // Get multiple items with error handling
  async multiGet(keys: string[]): Promise<Record<string, string | null>> {
    try {
      const pairs = await AsyncStorage.multiGet(keys);
      return pairs.reduce((acc, [key, value]) => {
        acc[key] = value;
        return acc;
      }, {} as Record<string, string | null>);
    } catch (error) {
      const storageError = new StorageError(
        error instanceof Error ? error.message : 'Unknown error',
        'multiGet'
      );
      errorHandler.handleError(storageError, 'AsyncStorage.multiGet');
      throw storageError;
    }
  }

  // Set multiple items with error handling
  async multiSet(keyValuePairs: Array<[string, string]>): Promise<void> {
    try {
      await AsyncStorage.multiSet(keyValuePairs);
    } catch (error) {
      const storageError = new StorageError(
        error instanceof Error ? error.message : 'Unknown error',
        'multiSet'
      );
      errorHandler.handleError(storageError, 'AsyncStorage.multiSet');
      throw storageError;
    }
  }
}

// Enhanced SecureStore wrapper
class EnhancedSecureStore {
  async getItemAsync(key: string): Promise<string | null> {
    try {
      const value = await SecureStore.getItemAsync(key);
      return value;
    } catch (error) {
      const storageError = new SecureStorageError(
        error instanceof Error ? error.message : 'Unknown error',
        'read',
        key
      );
      errorHandler.handleError(storageError, `SecureStore.getItemAsync.${key}`);
      throw storageError;
    }
  }

  async setItemAsync(key: string, value: string): Promise<void> {
    try {
      await SecureStore.setItemAsync(key, value);
    } catch (error) {
      const storageError = new SecureStorageError(
        error instanceof Error ? error.message : 'Unknown error',
        'write',
        key
      );
      errorHandler.handleError(storageError, `SecureStore.setItemAsync.${key}`);
      throw storageError;
    }
  }

  async deleteItemAsync(key: string): Promise<void> {
    try {
      await SecureStore.deleteItemAsync(key);
    } catch (error) {
      const storageError = new SecureStorageError(
        error instanceof Error ? error.message : 'Unknown error',
        'delete',
        key
      );
      errorHandler.handleError(storageError, `SecureStore.deleteItemAsync.${key}`);
      throw storageError;
    }
  }

  // Check if item exists
  async hasItemAsync(key: string): Promise<boolean> {
    try {
      const value = await this.getItemAsync(key);
      return value !== null;
    } catch (error) {
      // If we can't read, assume it doesn't exist
      return false;
    }
  }
}

// Storage manager with fallback mechanisms
export class StorageManager {
  private asyncStorage = new EnhancedAsyncStorage();
  private secureStore = new EnhancedSecureStore();

  // Store data with automatic JSON serialization
  async storeData<T>(key: string, data: T, secure: boolean = false): Promise<void> {
    try {
      const serializedData = JSON.stringify(data);
      
      if (secure) {
        await this.secureStore.setItemAsync(key, serializedData);
      } else {
        await this.asyncStorage.setItem(key, serializedData);
      }
    } catch (error) {
      if (error instanceof StorageError) {
        throw error;
      }
      
      const storageError = new StorageError(
        error instanceof Error ? error.message : 'Serialization failed',
        'store',
        key
      );
      errorHandler.handleError(storageError, `StorageManager.storeData.${key}`);
      throw storageError;
    }
  }

  // Retrieve data with automatic JSON deserialization
  async retrieveData<T>(key: string, secure: boolean = false): Promise<T | null> {
    try {
      let serializedData: string | null;
      
      if (secure) {
        serializedData = await this.secureStore.getItemAsync(key);
      } else {
        serializedData = await this.asyncStorage.getItem(key);
      }

      if (serializedData === null) {
        return null;
      }

      return JSON.parse(serializedData) as T;
    } catch (error) {
      if (error instanceof StorageError) {
        throw error;
      }

      const storageError = new StorageError(
        error instanceof Error ? error.message : 'Deserialization failed',
        'retrieve',
        key
      );
      errorHandler.handleError(storageError, `StorageManager.retrieveData.${key}`);
      throw storageError;
    }
  }

  // Remove data
  async removeData(key: string, secure: boolean = false): Promise<void> {
    try {
      if (secure) {
        await this.secureStore.deleteItemAsync(key);
      } else {
        await this.asyncStorage.removeItem(key);
      }
    } catch (error) {
      if (error instanceof StorageError) {
        throw error;
      }

      const storageError = new StorageError(
        error instanceof Error ? error.message : 'Unknown error',
        'remove',
        key
      );
      errorHandler.handleError(storageError, `StorageManager.removeData.${key}`);
      throw storageError;
    }
  }

  // Clear all data with confirmation
  async clearAllData(includeSecure: boolean = false): Promise<void> {
    try {
      await this.asyncStorage.clear();
      
      if (includeSecure) {
        // Clear known secure keys
        const secureKeys = Object.values(STORAGE_KEYS).filter(key => 
          key.includes('TOKEN') || key.includes('SECURE')
        );
        
        for (const key of secureKeys) {
          try {
            await this.secureStore.deleteItemAsync(key);
          } catch (error) {
            // Continue clearing other keys even if one fails
            errorHandler.handleError(error, `StorageManager.clearAllData.${key}`);
          }
        }
      }
    } catch (error) {
      const storageError = new StorageError(
        error instanceof Error ? error.message : 'Unknown error',
        'clearAll'
      );
      errorHandler.handleError(storageError, 'StorageManager.clearAllData');
      throw storageError;
    }
  }

  // Get storage info
  async getStorageInfo(): Promise<{
    asyncStorageSize: number;
    secureStoreKeys: string[];
  }> {
    try {
      // Get AsyncStorage keys and estimate size
      const asyncKeys = await AsyncStorage.getAllKeys();
      let totalSize = 0;
      
      for (const key of asyncKeys) {
        try {
          const value = await AsyncStorage.getItem(key);
          if (value) {
            totalSize += value.length;
          }
        } catch (error) {
          // Skip failed keys
        }
      }

      // Get known secure store keys
      const secureKeys = Object.values(STORAGE_KEYS).filter(key => 
        key.includes('TOKEN') || key.includes('SECURE')
      );
      
      const existingSecureKeys: string[] = [];
      for (const key of secureKeys) {
        try {
          const exists = await this.secureStore.hasItemAsync(key);
          if (exists) {
            existingSecureKeys.push(key);
          }
        } catch (error) {
          // Skip failed keys
        }
      }

      return {
        asyncStorageSize: totalSize,
        secureStoreKeys: existingSecureKeys,
      };
    } catch (error) {
      const storageError = new StorageError(
        error instanceof Error ? error.message : 'Unknown error',
        'getInfo'
      );
      errorHandler.handleError(storageError, 'StorageManager.getStorageInfo');
      throw storageError;
    }
  }
}

// Create singleton instance
export const storageManager = new StorageManager();

// Convenience functions
export const storeData = <T>(key: string, data: T, secure?: boolean) => 
  storageManager.storeData(key, data, secure);

export const retrieveData = <T>(key: string, secure?: boolean) => 
  storageManager.retrieveData<T>(key, secure);

export const removeData = (key: string, secure?: boolean) => 
  storageManager.removeData(key, secure);

export const clearAllData = (includeSecure?: boolean) => 
  storageManager.clearAllData(includeSecure);

// Export enhanced storage instances
export const enhancedAsyncStorage = new EnhancedAsyncStorage();
export const enhancedSecureStore = new EnhancedSecureStore();
