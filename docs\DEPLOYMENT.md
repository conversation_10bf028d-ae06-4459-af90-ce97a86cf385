# FoodWay Customer App - Deployment Guide

This guide covers the complete deployment process for the FoodWay Customer App, from development to production.

## Prerequisites

### Development Environment
- Node.js (v18 or higher)
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)
- EAS CLI (`npm install -g @expo/eas-cli`)

### Accounts Required
- Expo Account (for EAS services)
- Apple Developer Account (for iOS deployment)
- Google Play Console Account (for Android deployment)
- Railway Account (for backend deployment)

## Environment Setup

### 1. Environment Variables

Create environment files for different stages:

#### `.env.development`
```env
EXPO_PUBLIC_API_URL=http://localhost:3000/api
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_test_key
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=your_development_maps_key
EXPO_PUBLIC_ENVIRONMENT=development
```

#### `.env.staging`
```env
EXPO_PUBLIC_API_URL=https://your-staging-api.railway.app/api
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_test_key
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=your_staging_maps_key
EXPO_PUBLIC_ENVIRONMENT=staging
```

#### `.env.production`
```env
EXPO_PUBLIC_API_URL=https://your-production-api.railway.app/api
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_live_key
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=your_production_maps_key
EXPO_PUBLIC_ENVIRONMENT=production
```

### 2. EAS Configuration

Initialize EAS in your project:
```bash
eas build:configure
```

This creates `eas.json`:
```json
{
  "cli": {
    "version": ">= 5.0.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal",
      "env": {
        "ENVIRONMENT": "development"
      }
    },
    "preview": {
      "distribution": "internal",
      "env": {
        "ENVIRONMENT": "staging"
      }
    },
    "production": {
      "env": {
        "ENVIRONMENT": "production"
      }
    }
  },
  "submit": {
    "production": {}
  }
}
```

## Backend Deployment (Railway)

### 1. Database Setup
1. Create a new Railway project
2. Add PostgreSQL database service
3. Configure database schema and migrations
4. Set up environment variables

### 2. API Deployment
1. Connect your backend repository to Railway
2. Configure build and start commands
3. Set environment variables:
   - `DATABASE_URL`
   - `JWT_SECRET`
   - `STRIPE_SECRET_KEY`
   - `NODE_ENV=production`

### 3. Domain Configuration
1. Set up custom domain (optional)
2. Configure SSL certificates
3. Update CORS settings for your domain

## Mobile App Deployment

### 1. Pre-deployment Checklist

#### Code Quality
- [ ] All tests passing (`npm test`)
- [ ] No TypeScript errors (`npm run type-check`)
- [ ] Code linted (`npm run lint`)
- [ ] Performance optimized
- [ ] Error boundaries implemented

#### App Configuration
- [ ] App version updated in `app.json`
- [ ] Bundle identifier configured
- [ ] App icons and splash screens ready
- [ ] Permissions properly configured
- [ ] Deep linking configured

#### API Integration
- [ ] Production API endpoints configured
- [ ] Authentication flow tested
- [ ] Payment integration tested
- [ ] Push notifications configured

### 2. Build Process

#### Development Build
```bash
# For development testing
eas build --profile development --platform ios
eas build --profile development --platform android
```

#### Staging Build
```bash
# For internal testing
eas build --profile preview --platform all
```

#### Production Build
```bash
# For app store submission
eas build --profile production --platform all
```

### 3. Testing Builds

#### Internal Testing
1. Download builds from EAS dashboard
2. Install on test devices
3. Test core functionality:
   - User registration/login
   - Restaurant browsing
   - Order placement
   - Payment processing
   - Order tracking

#### Beta Testing
1. Set up TestFlight (iOS) and Internal Testing (Android)
2. Invite beta testers
3. Collect feedback and crash reports
4. Fix critical issues

## App Store Submission

### iOS App Store

#### 1. App Store Connect Setup
1. Create app record in App Store Connect
2. Configure app information:
   - App name and description
   - Keywords and categories
   - Screenshots and app preview
   - App icon

#### 2. Submission Process
```bash
# Submit to App Store
eas submit --platform ios
```

#### 3. Review Process
- Respond to App Review feedback
- Address any rejection reasons
- Resubmit if necessary

### Google Play Store

#### 1. Play Console Setup
1. Create app in Google Play Console
2. Configure store listing:
   - App details and description
   - Graphics and screenshots
   - Content rating

#### 2. Submission Process
```bash
# Submit to Google Play
eas submit --platform android
```

#### 3. Review Process
- Monitor review status
- Address policy violations
- Update if required

## Post-Deployment

### 1. Monitoring and Analytics

#### Crash Reporting
- Set up Sentry or Bugsnag
- Monitor crash rates
- Fix critical issues quickly

#### Performance Monitoring
- Monitor app performance metrics
- Track user engagement
- Optimize based on data

#### User Feedback
- Monitor app store reviews
- Set up in-app feedback system
- Respond to user issues

### 2. Updates and Maintenance

#### Over-the-Air Updates
```bash
# For non-native changes
eas update --branch production --message "Bug fixes and improvements"
```

#### App Store Updates
```bash
# For native changes requiring new build
eas build --profile production --platform all
eas submit --platform all
```

### 3. Rollback Strategy

#### Emergency Rollback
1. Revert to previous EAS update
2. Submit hotfix build if needed
3. Communicate with users

## CI/CD Pipeline

### GitHub Actions Example

```yaml
name: EAS Build and Deploy

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - run: npm ci
      - run: npm test
      - run: npm run type-check
      - run: npm run lint

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - run: npm ci
      - uses: expo/expo-github-action@v8
        with:
          expo-version: latest
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
      - run: eas build --platform all --non-interactive
```

## Security Considerations

### 1. API Security
- Use HTTPS for all API communications
- Implement proper authentication
- Validate all inputs
- Use rate limiting

### 2. App Security
- Store sensitive data securely
- Implement certificate pinning
- Use code obfuscation for production
- Regular security audits

### 3. Data Privacy
- Implement GDPR compliance
- Provide privacy policy
- Allow data deletion
- Secure data transmission

## Performance Optimization

### 1. Bundle Size
- Use code splitting
- Remove unused dependencies
- Optimize images and assets
- Use tree shaking

### 2. Runtime Performance
- Implement lazy loading
- Use FlatList for large lists
- Optimize re-renders
- Use performance monitoring

### 3. Network Optimization
- Implement caching strategies
- Use compression
- Optimize API calls
- Handle offline scenarios

## Troubleshooting

### Common Issues

#### Build Failures
- Check EAS build logs
- Verify dependencies
- Update Expo SDK if needed
- Clear cache and retry

#### Submission Rejections
- Review app store guidelines
- Fix identified issues
- Update app metadata
- Resubmit with changes

#### Performance Issues
- Profile app performance
- Optimize heavy operations
- Reduce bundle size
- Implement lazy loading

## Support and Maintenance

### 1. User Support
- Set up support channels
- Create FAQ documentation
- Monitor user feedback
- Provide timely responses

### 2. Technical Maintenance
- Regular dependency updates
- Security patch management
- Performance optimization
- Bug fix releases

### 3. Feature Updates
- Plan feature roadmap
- Gather user feedback
- Implement new features
- Test thoroughly before release

---

For additional support, refer to:
- [Expo Documentation](https://docs.expo.dev/)
- [EAS Documentation](https://docs.expo.dev/eas/)
- [React Native Documentation](https://reactnative.dev/)
- [Railway Documentation](https://docs.railway.app/)
