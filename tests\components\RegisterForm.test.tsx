import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import RegisterScreen from '../../app/(auth)/register';

// Mock dependencies
jest.mock('expo-router', () => ({
  router: {
    replace: jest.fn(),
    push: jest.fn(),
  },
}));

jest.mock('../../store/authStore', () => ({
  useAuthStore: () => ({
    register: jest.fn(),
    isLoading: false,
    clearError: jest.fn(),
  }),
}));

jest.mock('@expo/vector-icons', () => ({
  Ionicons: 'Ionicons',
}));

jest.mock('react-native-safe-area-context', () => ({
  SafeAreaView: ({ children }: any) => children,
}));

describe('RegisterScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render all form fields', () => {
    const { getByPlaceholderText } = render(<RegisterScreen />);
    
    expect(getByPlaceholderText('First name')).toBeTruthy();
    expect(getByPlaceholderText('Last name')).toBeTruthy();
    expect(getByPlaceholderText('Enter your email')).toBeTruthy();
    expect(getByPlaceholderText('Enter your phone number')).toBeTruthy();
    expect(getByPlaceholderText('Create a password')).toBeTruthy();
    expect(getByPlaceholderText('Confirm your password')).toBeTruthy();
  });

  it('should show validation errors when form is submitted empty', async () => {
    const { getByText, queryByText } = render(<RegisterScreen />);
    
    const submitButton = getByText('Create Account');
    fireEvent.press(submitButton);

    await waitFor(() => {
      expect(queryByText('First name is required')).toBeTruthy();
      expect(queryByText('Last name is required')).toBeTruthy();
      expect(queryByText('Email is required')).toBeTruthy();
      expect(queryByText('Phone number is required')).toBeTruthy();
      expect(queryByText('Password is required')).toBeTruthy();
      expect(queryByText('Please confirm your password')).toBeTruthy();
    });
  });

  it('should clear error for a field when valid input is provided', async () => {
    const { getByText, getByPlaceholderText, queryByText } = render(<RegisterScreen />);
    
    // First, trigger validation errors
    const submitButton = getByText('Create Account');
    fireEvent.press(submitButton);

    await waitFor(() => {
      expect(queryByText('First name is required')).toBeTruthy();
    });

    // Now type a valid first name
    const firstNameInput = getByPlaceholderText('First name');
    fireEvent.changeText(firstNameInput, 'John');

    await waitFor(() => {
      expect(queryByText('First name is required')).toBeFalsy();
    });
  });

  it('should keep other field errors when one field is corrected', async () => {
    const { getByText, getByPlaceholderText, queryByText } = render(<RegisterScreen />);
    
    // First, trigger validation errors
    const submitButton = getByText('Create Account');
    fireEvent.press(submitButton);

    await waitFor(() => {
      expect(queryByText('First name is required')).toBeTruthy();
      expect(queryByText('Last name is required')).toBeTruthy();
      expect(queryByText('Email is required')).toBeTruthy();
    });

    // Now type a valid first name
    const firstNameInput = getByPlaceholderText('First name');
    fireEvent.changeText(firstNameInput, 'John');

    await waitFor(() => {
      // First name error should be cleared
      expect(queryByText('First name is required')).toBeFalsy();
      // But other errors should remain
      expect(queryByText('Last name is required')).toBeTruthy();
      expect(queryByText('Email is required')).toBeTruthy();
    });
  });

  it('should validate password confirmation correctly', async () => {
    const { getByText, getByPlaceholderText, queryByText } = render(<RegisterScreen />);
    
    const passwordInput = getByPlaceholderText('Create a password');
    const confirmPasswordInput = getByPlaceholderText('Confirm your password');
    
    // Set different passwords
    fireEvent.changeText(passwordInput, 'password123');
    fireEvent.changeText(confirmPasswordInput, 'different123');
    
    const submitButton = getByText('Create Account');
    fireEvent.press(submitButton);

    await waitFor(() => {
      expect(queryByText('Passwords do not match')).toBeTruthy();
    });

    // Now make them match
    fireEvent.changeText(confirmPasswordInput, 'password123');

    await waitFor(() => {
      expect(queryByText('Passwords do not match')).toBeFalsy();
    });
  });

  it('should remain responsive after multiple validation attempts', async () => {
    const { getByText } = render(<RegisterScreen />);
    
    const submitButton = getByText('Create Account');
    
    // Press the button multiple times
    fireEvent.press(submitButton);
    fireEvent.press(submitButton);
    fireEvent.press(submitButton);

    // Button should still be responsive
    expect(submitButton).toBeTruthy();
    expect(submitButton.props.accessibilityState?.disabled).toBeFalsy();
  });
});
