#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function execCommand(command, options = {}) {
  try {
    log(`Executing: ${command}`, colors.cyan);
    const result = execSync(command, { 
      stdio: 'inherit', 
      encoding: 'utf8',
      ...options 
    });
    return result;
  } catch (error) {
    log(`Error executing command: ${command}`, colors.red);
    log(error.message, colors.red);
    process.exit(1);
  }
}

function checkPrerequisites() {
  log('🔍 Checking prerequisites...', colors.blue);
  
  // Check if Railway CLI is installed
  try {
    execSync('railway --version', { stdio: 'pipe' });
    log('✅ Railway CLI is installed', colors.green);
  } catch (error) {
    log('❌ Railway CLI is not installed', colors.red);
    log('Please install it with: npm install -g @railway/cli', colors.yellow);
    process.exit(1);
  }

  // Check if logged in to Railway
  try {
    execSync('railway whoami', { stdio: 'pipe' });
    log('✅ Logged in to Railway', colors.green);
  } catch (error) {
    log('❌ Not logged in to Railway', colors.red);
    log('Please login with: railway login', colors.yellow);
    process.exit(1);
  }

  // Check if package.json exists
  if (!fs.existsSync('package.json')) {
    log('❌ package.json not found', colors.red);
    process.exit(1);
  }

  log('✅ All prerequisites met', colors.green);
}

function validateEnvironment() {
  log('🔧 Validating environment configuration...', colors.blue);
  
  const requiredEnvVars = [
    'DATABASE_URL',
    'REDIS_URL',
    'JWT_SECRET',
    'STRIPE_SECRET_KEY'
  ];

  const missingVars = [];
  
  requiredEnvVars.forEach(varName => {
    try {
      const result = execSync(`railway variables get ${varName}`, { stdio: 'pipe', encoding: 'utf8' });
      if (!result.trim()) {
        missingVars.push(varName);
      }
    } catch (error) {
      missingVars.push(varName);
    }
  });

  if (missingVars.length > 0) {
    log('❌ Missing required environment variables:', colors.red);
    missingVars.forEach(varName => {
      log(`  - ${varName}`, colors.red);
    });
    log('Please set them using: railway variables set KEY=value', colors.yellow);
    process.exit(1);
  }

  log('✅ Environment variables validated', colors.green);
}

function buildApplication() {
  log('🏗️  Building application...', colors.blue);
  
  // Install dependencies
  log('Installing dependencies...', colors.cyan);
  execCommand('npm ci');
  
  // Run type checking
  log('Running type check...', colors.cyan);
  execCommand('npm run type-check');
  
  // Run tests if available
  if (fs.existsSync('jest.config.js') || fs.existsSync('jest.config.ts')) {
    log('Running tests...', colors.cyan);
    execCommand('npm test');
  }
  
  log('✅ Application built successfully', colors.green);
}

function deployToRailway() {
  log('🚀 Deploying to Railway...', colors.blue);
  
  // Deploy the application
  execCommand('railway up --detach');
  
  log('✅ Deployment initiated', colors.green);
  log('🔗 Check deployment status with: railway status', colors.cyan);
}

function runMigrations() {
  log('🗄️  Running database migrations...', colors.blue);
  
  try {
    // Run migrations using Railway CLI
    execCommand('railway run node -e "require(\'./services/database/index.js\').DatabaseMigrations.runAllMigrations()"');
    log('✅ Database migrations completed', colors.green);
  } catch (error) {
    log('⚠️  Migration failed, but deployment will continue', colors.yellow);
    log('You may need to run migrations manually', colors.yellow);
  }
}

function showDeploymentInfo() {
  log('📋 Deployment Information', colors.bright);
  
  try {
    const status = execSync('railway status', { encoding: 'utf8', stdio: 'pipe' });
    console.log(status);
  } catch (error) {
    log('Could not fetch deployment status', colors.yellow);
  }
  
  log('🔗 Useful commands:', colors.cyan);
  log('  railway logs     - View application logs', colors.reset);
  log('  railway status   - Check deployment status', colors.reset);
  log('  railway open     - Open application in browser', colors.reset);
  log('  railway shell    - Connect to application shell', colors.reset);
}

function main() {
  const args = process.argv.slice(2);
  const skipMigrations = args.includes('--skip-migrations');
  const skipBuild = args.includes('--skip-build');
  const skipValidation = args.includes('--skip-validation');

  log('🚂 Railway Deployment Script for FoodWay Customer App', colors.bright);
  log('=' .repeat(60), colors.cyan);

  try {
    checkPrerequisites();
    
    if (!skipValidation) {
      validateEnvironment();
    }
    
    if (!skipBuild) {
      buildApplication();
    }
    
    deployToRailway();
    
    if (!skipMigrations) {
      // Wait a bit for deployment to be ready
      log('⏳ Waiting for deployment to be ready...', colors.yellow);
      setTimeout(() => {
        runMigrations();
      }, 30000); // Wait 30 seconds
    }
    
    showDeploymentInfo();
    
    log('🎉 Deployment completed successfully!', colors.green);
    
  } catch (error) {
    log('❌ Deployment failed:', colors.red);
    log(error.message, colors.red);
    process.exit(1);
  }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
Railway Deployment Script for FoodWay Customer App

Usage: node scripts/railway-deploy.js [options]

Options:
  --skip-migrations    Skip database migrations
  --skip-build        Skip application build
  --skip-validation   Skip environment validation
  --help, -h          Show this help message

Examples:
  node scripts/railway-deploy.js                    # Full deployment
  node scripts/railway-deploy.js --skip-migrations  # Deploy without migrations
  node scripts/railway-deploy.js --skip-build       # Deploy without building
  `);
  process.exit(0);
}

if (require.main === module) {
  main();
}

module.exports = {
  checkPrerequisites,
  validateEnvironment,
  buildApplication,
  deployToRailway,
  runMigrations,
  showDeploymentInfo,
};
