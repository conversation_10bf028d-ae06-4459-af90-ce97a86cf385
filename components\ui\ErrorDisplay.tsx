import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { AppError } from '../../utils/errorHandler';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../utils/constants';

interface ErrorDisplayProps {
  error: AppError | string | null;
  onRetry?: () => void;
  onDismiss?: () => void;
  style?: ViewStyle;
  showIcon?: boolean;
  variant?: 'inline' | 'banner' | 'modal';
}

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  onRetry,
  onDismiss,
  style,
  showIcon = true,
  variant = 'inline',
}) => {
  if (!error) return null;

  const errorMessage = typeof error === 'string' ? error : error.userMessage;
  const isRetryable = typeof error === 'object' && error.isRetryable;

  const getVariantStyles = () => {
    switch (variant) {
      case 'banner':
        return styles.bannerContainer;
      case 'modal':
        return styles.modalContainer;
      default:
        return styles.inlineContainer;
    }
  };

  return (
    <View style={[getVariantStyles(), style]}>
      <View style={styles.content}>
        {showIcon && (
          <Ionicons
            name="alert-circle"
            size={20}
            color={COLORS.error}
            style={styles.icon}
          />
        )}
        <Text style={styles.message}>{errorMessage}</Text>
        {onDismiss && (
          <TouchableOpacity onPress={onDismiss} style={styles.dismissButton}>
            <Ionicons name="close" size={16} color={COLORS.gray[500]} />
          </TouchableOpacity>
        )}
      </View>
      {onRetry && isRetryable && (
        <TouchableOpacity onPress={onRetry} style={styles.retryButton}>
          <Text style={styles.retryText}>Retry</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

interface ErrorBoundaryFallbackProps {
  error?: AppError;
  onRetry?: () => void;
}

export const ErrorBoundaryFallback: React.FC<ErrorBoundaryFallbackProps> = ({
  error,
  onRetry,
}) => {
  return (
    <View style={styles.fallbackContainer}>
      <Ionicons name="warning" size={64} color={COLORS.error} />
      <Text style={styles.fallbackTitle}>Something went wrong</Text>
      <Text style={styles.fallbackMessage}>
        {error?.userMessage || 'An unexpected error occurred'}
      </Text>
      {onRetry && (
        <TouchableOpacity onPress={onRetry} style={styles.fallbackButton}>
          <Text style={styles.fallbackButtonText}>Try Again</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

interface FormErrorProps {
  errors: Record<string, string>;
  field: string;
  style?: TextStyle;
}

export const FormError: React.FC<FormErrorProps> = ({ errors, field, style }) => {
  if (!errors[field]) return null;

  return (
    <Text style={[styles.formError, style]}>
      {errors[field]}
    </Text>
  );
};

interface NetworkErrorProps {
  onRetry: () => void;
  message?: string;
}

export const NetworkError: React.FC<NetworkErrorProps> = ({
  onRetry,
  message = 'No internet connection',
}) => {
  return (
    <View style={styles.networkErrorContainer}>
      <Ionicons name="wifi-outline" size={48} color={COLORS.gray[400]} />
      <Text style={styles.networkErrorTitle}>Connection Problem</Text>
      <Text style={styles.networkErrorMessage}>{message}</Text>
      <TouchableOpacity onPress={onRetry} style={styles.networkErrorButton}>
        <Text style={styles.networkErrorButtonText}>Try Again</Text>
      </TouchableOpacity>
    </View>
  );
};

interface LoadingErrorProps {
  onRetry: () => void;
  message?: string;
}

export const LoadingError: React.FC<LoadingErrorProps> = ({
  onRetry,
  message = 'Failed to load data',
}) => {
  return (
    <View style={styles.loadingErrorContainer}>
      <Ionicons name="refresh-circle-outline" size={48} color={COLORS.gray[400]} />
      <Text style={styles.loadingErrorMessage}>{message}</Text>
      <TouchableOpacity onPress={onRetry} style={styles.loadingErrorButton}>
        <Ionicons name="refresh" size={16} color={COLORS.primary} />
        <Text style={styles.loadingErrorButtonText}>Retry</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  // Error Display Variants
  inlineContainer: {
    backgroundColor: COLORS.error + '10',
    borderColor: COLORS.error + '30',
    borderWidth: 1,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    marginVertical: SPACING.sm,
  },
  bannerContainer: {
    backgroundColor: COLORS.error,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
  },
  modalContainer: {
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
    margin: SPACING.lg,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },

  // Content
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: SPACING.sm,
  },
  message: {
    flex: 1,
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.error,
    lineHeight: 20,
  },
  dismissButton: {
    padding: SPACING.xs,
    marginLeft: SPACING.sm,
  },

  // Retry Button
  retryButton: {
    backgroundColor: COLORS.error,
    borderRadius: BORDER_RADIUS.sm,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    marginTop: SPACING.sm,
    alignSelf: 'flex-start',
  },
  retryText: {
    color: COLORS.white,
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '600',
  },

  // Error Boundary Fallback
  fallbackContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
    backgroundColor: COLORS.background,
  },
  fallbackTitle: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
  },
  fallbackMessage: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
    lineHeight: 22,
  },
  fallbackButton: {
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.lg,
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.md,
  },
  fallbackButtonText: {
    color: COLORS.white,
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
  },

  // Form Error
  formError: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.error,
    marginTop: SPACING.xs,
  },

  // Network Error
  networkErrorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
    backgroundColor: COLORS.background,
  },
  networkErrorTitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  networkErrorMessage: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },
  networkErrorButton: {
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.lg,
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.md,
  },
  networkErrorButtonText: {
    color: COLORS.white,
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
  },

  // Loading Error
  loadingErrorContainer: {
    alignItems: 'center',
    padding: SPACING.lg,
  },
  loadingErrorMessage: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
    marginTop: SPACING.sm,
    marginBottom: SPACING.md,
    textAlign: 'center',
  },
  loadingErrorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary + '10',
    borderColor: COLORS.primary,
    borderWidth: 1,
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
  },
  loadingErrorButtonText: {
    color: COLORS.primary,
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '600',
    marginLeft: SPACING.xs,
  },
});

export default ErrorDisplay;
