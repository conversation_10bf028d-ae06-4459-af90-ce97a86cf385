import { Order, OrderStatus, OrderItem } from '../../types';
import { mockRestaurants, mockMenuItems } from './mockRestaurants';
import { mockAddresses, mockPaymentMethods } from './mockData';

// Mock Order Items
export const mockOrderItems: OrderItem[] = [
  {
    id: 'oi1',
    menuItemId: 'item1',
    quantity: 2,
    price: 16.99,
    customizations: [
      { id: 'size', name: '<PERSON><PERSON>', selectedOption: { id: 'large', name: 'Large (14")', price: 6 } },
      { id: 'crust', name: 'Crust Type', selectedOption: { id: 'thin', name: 'Thin Crust', price: 0 } },
    ],
    addOns: [
      { id: 'extra-cheese', name: 'Extra Cheese', price: 2.50 },
    ],
    specialInstructions: 'Extra crispy please',
    totalPrice: 52.48, // (16.99 + 6 + 2.50) * 2
  },
  {
    id: 'oi2',
    menuItemId: 'item2',
    quantity: 1,
    price: 6.99,
    customizations: [],
    addOns: [
      { id: 'marinara', name: '<PERSON><PERSON> Sauce', price: 1.00 },
    ],
    specialInstructions: '',
    totalPrice: 7.99,
  },
];

// Mock Orders
export const mockOrders: Order[] = [
  {
    id: 'order1',
    userId: 'user1',
    restaurantId: '1',
    restaurant: mockRestaurants[0],
    items: mockOrderItems,
    status: 'delivered' as OrderStatus,
    subtotal: 60.47,
    deliveryFee: 2.99,
    serviceFee: 1.50,
    tax: 5.44,
    tip: 8.00,
    total: 78.40,
    deliveryAddress: mockAddresses[0],
    paymentMethod: mockPaymentMethods[0],
    estimatedDeliveryTime: '2024-01-15T19:30:00Z',
    actualDeliveryTime: '2024-01-15T19:25:00Z',
    orderTime: '2024-01-15T18:45:00Z',
    specialInstructions: 'Ring doorbell twice',
    trackingUpdates: [
      {
        status: 'confirmed',
        timestamp: '2024-01-15T18:46:00Z',
        message: 'Order confirmed by restaurant',
      },
      {
        status: 'preparing',
        timestamp: '2024-01-15T18:50:00Z',
        message: 'Restaurant is preparing your order',
      },
      {
        status: 'ready',
        timestamp: '2024-01-15T19:05:00Z',
        message: 'Order is ready for pickup',
      },
      {
        status: 'picked_up',
        timestamp: '2024-01-15T19:10:00Z',
        message: 'Driver has picked up your order',
      },
      {
        status: 'out_for_delivery',
        timestamp: '2024-01-15T19:12:00Z',
        message: 'Order is out for delivery',
      },
      {
        status: 'delivered',
        timestamp: '2024-01-15T19:25:00Z',
        message: 'Order delivered successfully',
      },
    ],
    driverInfo: {
      name: 'Mike Johnson',
      phone: '******-0199',
      rating: 4.8,
      vehicle: 'Honda Civic - ABC123',
      photo: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    },
    rating: 5,
    review: 'Great food, delivered on time!',
  },
  {
    id: 'order2',
    userId: 'user1',
    restaurantId: '2',
    restaurant: mockRestaurants[1],
    items: [
      {
        id: 'oi3',
        menuItemId: 'item3',
        quantity: 1,
        price: 12.99,
        customizations: [
          { id: 'patty', name: 'Patty', selectedOption: { id: 'beef', name: 'Beef Patty', price: 0 } },
        ],
        addOns: [
          { id: 'bacon', name: 'Bacon', price: 2.50 },
          { id: 'avocado', name: 'Avocado', price: 2.00 },
        ],
        specialInstructions: 'Medium rare',
        totalPrice: 17.49,
      },
    ],
    status: 'out_for_delivery' as OrderStatus,
    subtotal: 17.49,
    deliveryFee: 1.99,
    serviceFee: 1.00,
    tax: 1.84,
    tip: 3.50,
    total: 25.82,
    deliveryAddress: mockAddresses[0],
    paymentMethod: mockPaymentMethods[0],
    estimatedDeliveryTime: '2024-01-16T20:15:00Z',
    actualDeliveryTime: null,
    orderTime: '2024-01-16T19:30:00Z',
    specialInstructions: 'Call when you arrive',
    trackingUpdates: [
      {
        status: 'confirmed',
        timestamp: '2024-01-16T19:31:00Z',
        message: 'Order confirmed by restaurant',
      },
      {
        status: 'preparing',
        timestamp: '2024-01-16T19:35:00Z',
        message: 'Restaurant is preparing your order',
      },
      {
        status: 'ready',
        timestamp: '2024-01-16T19:50:00Z',
        message: 'Order is ready for pickup',
      },
      {
        status: 'picked_up',
        timestamp: '2024-01-16T19:55:00Z',
        message: 'Driver has picked up your order',
      },
      {
        status: 'out_for_delivery',
        timestamp: '2024-01-16T19:58:00Z',
        message: 'Order is out for delivery - ETA 15 minutes',
      },
    ],
    driverInfo: {
      name: 'Sarah Chen',
      phone: '******-0188',
      rating: 4.9,
      vehicle: 'Toyota Prius - XYZ789',
      photo: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    },
    rating: null,
    review: null,
  },
  {
    id: 'order3',
    userId: 'user1',
    restaurantId: '3',
    restaurant: mockRestaurants[2],
    items: [
      {
        id: 'oi4',
        menuItemId: 'item4',
        quantity: 3,
        price: 8.99,
        customizations: [],
        addOns: [
          { id: 'wasabi', name: 'Extra Wasabi', price: 0.50 },
          { id: 'ginger', name: 'Pickled Ginger', price: 0.50 },
        ],
        specialInstructions: '',
        totalPrice: 29.97, // (8.99 + 0.50 + 0.50) * 3
      },
    ],
    status: 'preparing' as OrderStatus,
    subtotal: 29.97,
    deliveryFee: 3.99,
    serviceFee: 1.50,
    tax: 3.15,
    tip: 6.00,
    total: 44.61,
    deliveryAddress: mockAddresses[1],
    paymentMethod: mockPaymentMethods[1],
    estimatedDeliveryTime: '2024-01-16T21:00:00Z',
    actualDeliveryTime: null,
    orderTime: '2024-01-16T20:15:00Z',
    specialInstructions: 'Leave at reception',
    trackingUpdates: [
      {
        status: 'confirmed',
        timestamp: '2024-01-16T20:16:00Z',
        message: 'Order confirmed by restaurant',
      },
      {
        status: 'preparing',
        timestamp: '2024-01-16T20:20:00Z',
        message: 'Restaurant is preparing your order',
      },
    ],
    driverInfo: null,
    rating: null,
    review: null,
  },
];

// Utility functions
export const getUserOrders = (userId: string): Order[] => {
  return mockOrders.filter(order => order.userId === userId);
};

export const getOrderById = (orderId: string): Order | undefined => {
  return mockOrders.find(order => order.id === orderId);
};

export const getActiveOrders = (userId: string): Order[] => {
  const activeStatuses: OrderStatus[] = ['confirmed', 'preparing', 'ready', 'picked_up', 'out_for_delivery'];
  return mockOrders.filter(order => 
    order.userId === userId && activeStatuses.includes(order.status)
  );
};

export const getOrderHistory = (userId: string): Order[] => {
  const completedStatuses: OrderStatus[] = ['delivered', 'cancelled'];
  return mockOrders.filter(order => 
    order.userId === userId && completedStatuses.includes(order.status)
  );
};

export const createMockOrder = (orderData: Partial<Order>): Order => {
  const newOrder: Order = {
    id: `order_${Date.now()}`,
    userId: orderData.userId || 'user1',
    restaurantId: orderData.restaurantId || '1',
    restaurant: orderData.restaurant || mockRestaurants[0],
    items: orderData.items || [],
    status: 'confirmed',
    subtotal: orderData.subtotal || 0,
    deliveryFee: orderData.deliveryFee || 2.99,
    serviceFee: orderData.serviceFee || 1.50,
    tax: orderData.tax || 0,
    tip: orderData.tip || 0,
    total: orderData.total || 0,
    deliveryAddress: orderData.deliveryAddress || mockAddresses[0],
    paymentMethod: orderData.paymentMethod || mockPaymentMethods[0],
    estimatedDeliveryTime: orderData.estimatedDeliveryTime || new Date(Date.now() + 30 * 60 * 1000).toISOString(),
    actualDeliveryTime: null,
    orderTime: new Date().toISOString(),
    specialInstructions: orderData.specialInstructions || '',
    trackingUpdates: [
      {
        status: 'confirmed',
        timestamp: new Date().toISOString(),
        message: 'Order confirmed by restaurant',
      },
    ],
    driverInfo: null,
    rating: null,
    review: null,
    ...orderData,
  };

  // Add to mock orders array
  mockOrders.unshift(newOrder);
  return newOrder;
};

export const updateOrderStatus = (orderId: string, status: OrderStatus, message?: string): Order | null => {
  const order = mockOrders.find(o => o.id === orderId);
  if (!order) return null;

  order.status = status;
  order.trackingUpdates.push({
    status,
    timestamp: new Date().toISOString(),
    message: message || `Order status updated to ${status}`,
  });

  if (status === 'delivered') {
    order.actualDeliveryTime = new Date().toISOString();
  }

  return order;
};

// Simulate real-time order tracking
export const simulateOrderProgress = (orderId: string): void => {
  const order = mockOrders.find(o => o.id === orderId);
  if (!order) return;

  const statusProgression: { status: OrderStatus; delay: number; message: string }[] = [
    { status: 'confirmed', delay: 0, message: 'Order confirmed by restaurant' },
    { status: 'preparing', delay: 5000, message: 'Restaurant is preparing your order' },
    { status: 'ready', delay: 15000, message: 'Order is ready for pickup' },
    { status: 'picked_up', delay: 5000, message: 'Driver has picked up your order' },
    { status: 'out_for_delivery', delay: 3000, message: 'Order is out for delivery' },
    { status: 'delivered', delay: 20000, message: 'Order delivered successfully' },
  ];

  let currentIndex = statusProgression.findIndex(s => s.status === order.status);
  if (currentIndex === -1) currentIndex = 0;

  const progressOrder = () => {
    currentIndex++;
    if (currentIndex < statusProgression.length) {
      const nextStatus = statusProgression[currentIndex];
      setTimeout(() => {
        updateOrderStatus(orderId, nextStatus.status, nextStatus.message);
        progressOrder();
      }, nextStatus.delay);
    }
  };

  progressOrder();
};
