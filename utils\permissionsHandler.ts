import * as Location from 'expo-location';
import * as Camera from 'expo-camera';
import * as Notifications from 'expo-notifications';
import * as MediaLibrary from 'expo-media-library';
import { Alert, Linking, Platform } from 'react-native';
import { AppError, errorHandler } from './errorHandler';
import { addBreadcrumb } from './errorLogger';
import { ERROR_TYPES } from './constants';

// Permission error types
export class PermissionError extends AppError {
  constructor(
    permission: string,
    message: string,
    userMessage?: string,
    canOpenSettings: boolean = true
  ) {
    super(
      ERROR_TYPES.PERMISSION_DENIED || 'PERMISSION_DENIED',
      message,
      userMessage || `${permission} permission is required for this feature.`,
      'PERMISSION_ERROR',
      403,
      false
    );
    this.permission = permission;
    this.canOpenSettings = canOpenSettings;
  }

  public readonly permission: string;
  public readonly canOpenSettings: boolean;
}

export class LocationError extends PermissionError {
  constructor(message: string, userMessage?: string) {
    super(
      'Location',
      message,
      userMessage || 'Location access is required for delivery services.',
      true
    );
  }
}

export class CameraError extends PermissionError {
  constructor(message: string, userMessage?: string) {
    super(
      'Camera',
      message,
      userMessage || 'Camera access is required to take photos.',
      true
    );
  }
}

export class NotificationError extends PermissionError {
  constructor(message: string, userMessage?: string) {
    super(
      'Notifications',
      message,
      userMessage || 'Notification permission is required for order updates.',
      true
    );
  }
}

// Permission status types
export type PermissionStatus = 'granted' | 'denied' | 'undetermined' | 'restricted';

export interface PermissionResult {
  status: PermissionStatus;
  canAskAgain: boolean;
  granted: boolean;
}

export interface LocationResult extends PermissionResult {
  location?: Location.LocationObject;
  accuracy?: Location.LocationAccuracy;
}

class PermissionsHandler {
  private static instance: PermissionsHandler;
  private permissionCache: Map<string, { status: PermissionStatus; timestamp: number }> = new Map();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  private constructor() {}

  public static getInstance(): PermissionsHandler {
    if (!PermissionsHandler.instance) {
      PermissionsHandler.instance = new PermissionsHandler();
    }
    return PermissionsHandler.instance;
  }

  // Location permissions and services
  public async requestLocationPermission(
    accuracy: Location.LocationAccuracy = Location.LocationAccuracy.Balanced
  ): Promise<LocationResult> {
    try {
      addBreadcrumb('permissions', 'Requesting location permission', 'info');

      // Check if location services are enabled
      const serviceEnabled = await Location.hasServicesEnabledAsync();
      if (!serviceEnabled) {
        throw new LocationError(
          'Location services are disabled',
          'Please enable location services in your device settings to use delivery features.'
        );
      }

      // Request foreground permission
      const { status, canAskAgain } = await Location.requestForegroundPermissionsAsync();
      
      const result: LocationResult = {
        status: status as PermissionStatus,
        canAskAgain,
        granted: status === 'granted',
      };

      if (status === 'granted') {
        // Get current location
        try {
          const location = await Location.getCurrentPositionAsync({
            accuracy,
            timeInterval: 10000, // 10 seconds timeout
          });
          result.location = location;
          result.accuracy = accuracy;
          
          addBreadcrumb('permissions', 'Location permission granted and location obtained', 'info');
        } catch (locationError) {
          addBreadcrumb('permissions', 'Location permission granted but failed to get location', 'warning');
          // Permission granted but couldn't get location
        }
      } else {
        const error = new LocationError(
          `Location permission ${status}`,
          status === 'denied' 
            ? 'Location access was denied. Please enable it in settings to use delivery features.'
            : 'Location permission is required for delivery services.'
        );
        
        this.handlePermissionError(error);
        throw error;
      }

      this.cachePermissionStatus('location', status as PermissionStatus);
      return result;
    } catch (error) {
      if (error instanceof PermissionError) {
        throw error;
      }
      
      const locationError = new LocationError(
        error instanceof Error ? error.message : 'Failed to request location permission'
      );
      errorHandler.handleError(locationError, 'PermissionsHandler.requestLocationPermission');
      throw locationError;
    }
  }

  // Camera permissions
  public async requestCameraPermission(): Promise<PermissionResult> {
    try {
      addBreadcrumb('permissions', 'Requesting camera permission', 'info');

      const { status, canAskAgain } = await Camera.requestCameraPermissionsAsync();
      
      const result: PermissionResult = {
        status: status as PermissionStatus,
        canAskAgain,
        granted: status === 'granted',
      };

      if (status !== 'granted') {
        const error = new CameraError(
          `Camera permission ${status}`,
          status === 'denied'
            ? 'Camera access was denied. Please enable it in settings to take photos.'
            : 'Camera permission is required to take photos.'
        );
        
        this.handlePermissionError(error);
        throw error;
      }

      addBreadcrumb('permissions', 'Camera permission granted', 'info');
      this.cachePermissionStatus('camera', status as PermissionStatus);
      return result;
    } catch (error) {
      if (error instanceof PermissionError) {
        throw error;
      }
      
      const cameraError = new CameraError(
        error instanceof Error ? error.message : 'Failed to request camera permission'
      );
      errorHandler.handleError(cameraError, 'PermissionsHandler.requestCameraPermission');
      throw cameraError;
    }
  }

  // Notification permissions
  public async requestNotificationPermission(): Promise<PermissionResult> {
    try {
      addBreadcrumb('permissions', 'Requesting notification permission', 'info');

      const { status, canAskAgain } = await Notifications.requestPermissionsAsync({
        ios: {
          allowAlert: true,
          allowBadge: true,
          allowSound: true,
          allowAnnouncements: false,
        },
      });

      const result: PermissionResult = {
        status: status as PermissionStatus,
        canAskAgain,
        granted: status === 'granted',
      };

      if (status !== 'granted') {
        const error = new NotificationError(
          `Notification permission ${status}`,
          status === 'denied'
            ? 'Notification access was denied. You may miss important order updates.'
            : 'Notification permission helps you stay updated on your orders.'
        );
        
        // Don't throw for notifications as they're not critical
        this.handlePermissionError(error, false);
      } else {
        addBreadcrumb('permissions', 'Notification permission granted', 'info');
      }

      this.cachePermissionStatus('notifications', status as PermissionStatus);
      return result;
    } catch (error) {
      const notificationError = new NotificationError(
        error instanceof Error ? error.message : 'Failed to request notification permission'
      );
      errorHandler.handleError(notificationError, 'PermissionsHandler.requestNotificationPermission');
      
      // Return denied status instead of throwing for notifications
      return {
        status: 'denied',
        canAskAgain: false,
        granted: false,
      };
    }
  }

  // Media library permissions (for saving photos)
  public async requestMediaLibraryPermission(): Promise<PermissionResult> {
    try {
      addBreadcrumb('permissions', 'Requesting media library permission', 'info');

      const { status, canAskAgain } = await MediaLibrary.requestPermissionsAsync();
      
      const result: PermissionResult = {
        status: status as PermissionStatus,
        canAskAgain,
        granted: status === 'granted',
      };

      if (status !== 'granted') {
        const error = new PermissionError(
          'Media Library',
          `Media library permission ${status}`,
          'Media library access is required to save photos.',
          true
        );
        
        this.handlePermissionError(error);
        throw error;
      }

      addBreadcrumb('permissions', 'Media library permission granted', 'info');
      this.cachePermissionStatus('mediaLibrary', status as PermissionStatus);
      return result;
    } catch (error) {
      if (error instanceof PermissionError) {
        throw error;
      }
      
      const mediaError = new PermissionError(
        'Media Library',
        error instanceof Error ? error.message : 'Failed to request media library permission'
      );
      errorHandler.handleError(mediaError, 'PermissionsHandler.requestMediaLibraryPermission');
      throw mediaError;
    }
  }

  // Check permission status without requesting
  public async checkLocationPermission(): Promise<PermissionStatus> {
    try {
      const cached = this.getCachedPermissionStatus('location');
      if (cached) return cached;

      const { status } = await Location.getForegroundPermissionsAsync();
      this.cachePermissionStatus('location', status as PermissionStatus);
      return status as PermissionStatus;
    } catch (error) {
      return 'undetermined';
    }
  }

  public async checkCameraPermission(): Promise<PermissionStatus> {
    try {
      const cached = this.getCachedPermissionStatus('camera');
      if (cached) return cached;

      const { status } = await Camera.getCameraPermissionsAsync();
      this.cachePermissionStatus('camera', status as PermissionStatus);
      return status as PermissionStatus;
    } catch (error) {
      return 'undetermined';
    }
  }

  public async checkNotificationPermission(): Promise<PermissionStatus> {
    try {
      const cached = this.getCachedPermissionStatus('notifications');
      if (cached) return cached;

      const { status } = await Notifications.getPermissionsAsync();
      this.cachePermissionStatus('notifications', status as PermissionStatus);
      return status as PermissionStatus;
    } catch (error) {
      return 'undetermined';
    }
  }

  // Handle permission errors with user-friendly dialogs
  private handlePermissionError(error: PermissionError, shouldThrow: boolean = true): void {
    const buttons = [
      { text: 'Cancel', style: 'cancel' as const },
    ];

    if (error.canOpenSettings) {
      buttons.push({
        text: 'Open Settings',
        onPress: () => this.openAppSettings(),
      });
    }

    Alert.alert(
      `${error.permission} Permission Required`,
      error.userMessage,
      buttons
    );

    addBreadcrumb('permissions', `Permission error: ${error.permission}`, 'error', {
      status: error.message,
      canOpenSettings: error.canOpenSettings,
    });
  }

  // Open app settings
  private async openAppSettings(): Promise<void> {
    try {
      await Linking.openSettings();
      addBreadcrumb('permissions', 'Opened app settings', 'info');
    } catch (error) {
      Alert.alert(
        'Settings Unavailable',
        'Unable to open settings. Please manually enable permissions in your device settings.'
      );
    }
  }

  // Cache management
  private cachePermissionStatus(permission: string, status: PermissionStatus): void {
    this.permissionCache.set(permission, {
      status,
      timestamp: Date.now(),
    });
  }

  private getCachedPermissionStatus(permission: string): PermissionStatus | null {
    const cached = this.permissionCache.get(permission);
    if (!cached) return null;

    const isExpired = Date.now() - cached.timestamp > this.cacheTimeout;
    if (isExpired) {
      this.permissionCache.delete(permission);
      return null;
    }

    return cached.status;
  }

  // Clear permission cache
  public clearPermissionCache(): void {
    this.permissionCache.clear();
  }

  // Get all permission statuses
  public async getAllPermissionStatuses(): Promise<Record<string, PermissionStatus>> {
    const [location, camera, notifications] = await Promise.allSettled([
      this.checkLocationPermission(),
      this.checkCameraPermission(),
      this.checkNotificationPermission(),
    ]);

    return {
      location: location.status === 'fulfilled' ? location.value : 'undetermined',
      camera: camera.status === 'fulfilled' ? camera.value : 'undetermined',
      notifications: notifications.status === 'fulfilled' ? notifications.value : 'undetermined',
    };
  }

  // Request multiple permissions
  public async requestAllPermissions(): Promise<Record<string, PermissionResult>> {
    const results: Record<string, PermissionResult> = {};

    // Request location (critical)
    try {
      results.location = await this.requestLocationPermission();
    } catch (error) {
      results.location = { status: 'denied', canAskAgain: false, granted: false };
    }

    // Request notifications (non-critical)
    try {
      results.notifications = await this.requestNotificationPermission();
    } catch (error) {
      results.notifications = { status: 'denied', canAskAgain: false, granted: false };
    }

    return results;
  }
}

// Create singleton instance
export const permissionsHandler = PermissionsHandler.getInstance();

// Convenience functions
export const requestLocationPermission = (accuracy?: Location.LocationAccuracy) =>
  permissionsHandler.requestLocationPermission(accuracy);

export const requestCameraPermission = () =>
  permissionsHandler.requestCameraPermission();

export const requestNotificationPermission = () =>
  permissionsHandler.requestNotificationPermission();

export const checkLocationPermission = () =>
  permissionsHandler.checkLocationPermission();

export const checkCameraPermission = () =>
  permissionsHandler.checkCameraPermission();

export const checkNotificationPermission = () =>
  permissionsHandler.checkNotificationPermission();

export const getAllPermissionStatuses = () =>
  permissionsHandler.getAllPermissionStatuses();

export default permissionsHandler;
