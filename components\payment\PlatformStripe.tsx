import { Platform } from 'react-native';
import React from 'react';

// Conditional imports based on platform
let StripeProvider: any;
let CardField: any;
let useStripe: any;
let useConfirmPayment: any;

if (Platform.OS === 'web') {
  // Web-specific mock components
  const { 
    StripeProvider: WebStripeProvider, 
    CardField: WebCardField, 
    useStripe: webUseStripe,
    useConfirmPayment: webUseConfirmPayment 
  } = require('./StripeProvider.web');
  
  StripeProvider = WebStripeProvider;
  CardField = WebCardField;
  useStripe = webUseStripe;
  useConfirmPayment = webUseConfirmPayment;
} else {
  // Native components for iOS/Android
  try {
    const stripe = require('@stripe/stripe-react-native');
    StripeProvider = stripe.StripeProvider;
    CardField = stripe.CardField;
    useStripe = stripe.useStripe;
    useConfirmPayment = stripe.useConfirmPayment;
  } catch (error) {
    console.warn('Stripe React Native not available, using mock components');
    const { 
      StripeProvider: WebStripeProvider, 
      CardField: WebCardField, 
      useStripe: webUseStripe,
      useConfirmPayment: webUseConfirmPayment 
    } = require('./StripeProvider.web');
    
    StripeProvider = WebStripeProvider;
    CardField = WebCardField;
    useStripe = webUseStripe;
    useConfirmPayment = webUseConfirmPayment;
  }
}

export { StripeProvider, CardField, useStripe, useConfirmPayment };
