import { InteractionManager, Platform } from 'react-native';

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private isEnabled: boolean = __DEV__;

  constructor() {
    this.setupGlobalErrorHandler();
  }

  /**
   * Start timing a performance metric
   */
  startTimer(name: string, metadata?: Record<string, any>): void {
    if (!this.isEnabled) return;

    this.metrics.set(name, {
      name,
      startTime: Date.now(),
      metadata,
    });
  }

  /**
   * End timing a performance metric
   */
  endTimer(name: string): number | null {
    if (!this.isEnabled) return null;

    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance timer '${name}' was not started`);
      return null;
    }

    const endTime = Date.now();
    const duration = endTime - metric.startTime;

    metric.endTime = endTime;
    metric.duration = duration;

    this.logMetric(metric);
    this.metrics.delete(name);

    return duration;
  }

  /**
   * Measure the execution time of a function
   */
  async measureAsync<T>(name: string, fn: () => Promise<T>, metadata?: Record<string, any>): Promise<T> {
    this.startTimer(name, metadata);
    try {
      const result = await fn();
      this.endTimer(name);
      return result;
    } catch (error) {
      this.endTimer(name);
      throw error;
    }
  }

  /**
   * Measure the execution time of a synchronous function
   */
  measure<T>(name: string, fn: () => T, metadata?: Record<string, any>): T {
    this.startTimer(name, metadata);
    try {
      const result = fn();
      this.endTimer(name);
      return result;
    } catch (error) {
      this.endTimer(name);
      throw error;
    }
  }

  /**
   * Measure component render time
   */
  measureRender(componentName: string, metadata?: Record<string, any>) {
    const timerName = `render_${componentName}`;
    
    return {
      start: () => this.startTimer(timerName, metadata),
      end: () => this.endTimer(timerName),
    };
  }

  /**
   * Measure navigation performance
   */
  measureNavigation(screenName: string, metadata?: Record<string, any>) {
    const timerName = `navigation_${screenName}`;
    
    return {
      start: () => this.startTimer(timerName, metadata),
      end: () => {
        // Wait for interactions to complete before measuring
        InteractionManager.runAfterInteractions(() => {
          this.endTimer(timerName);
        });
      },
    };
  }

  /**
   * Measure API call performance
   */
  measureApiCall(endpoint: string, method: string = 'GET', metadata?: Record<string, any>) {
    const timerName = `api_${method}_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`;
    
    return {
      start: () => this.startTimer(timerName, { endpoint, method, ...metadata }),
      end: () => this.endTimer(timerName),
    };
  }

  /**
   * Log performance metric
   */
  private logMetric(metric: PerformanceMetric): void {
    const { name, duration, metadata } = metric;
    
    if (duration === undefined) return;

    // Color-coded console output
    const color = this.getColorForDuration(duration);
    const metadataStr = metadata ? ` | ${JSON.stringify(metadata)}` : '';
    
    console.log(
      `%c[PERF] ${name}: ${duration}ms${metadataStr}`,
      `color: ${color}; font-weight: bold;`
    );

    // Send to analytics in production (if enabled)
    if (!__DEV__ && this.isEnabled) {
      this.sendToAnalytics(metric);
    }

    // Alert for slow operations
    if (duration > 1000) {
      console.warn(`⚠️ Slow operation detected: ${name} took ${duration}ms`);
    }
  }

  /**
   * Get color based on duration for console output
   */
  private getColorForDuration(duration: number): string {
    if (duration < 100) return '#4CAF50'; // Green
    if (duration < 500) return '#FF9800'; // Orange
    return '#F44336'; // Red
  }

  /**
   * Send metrics to analytics service
   */
  private sendToAnalytics(metric: PerformanceMetric): void {
    // Implement your analytics service here
    // Example: Analytics.track('performance', metric);
  }

  /**
   * Setup global error handler for performance monitoring
   */
  private setupGlobalErrorHandler(): void {
    if (__DEV__) {
      const originalConsoleError = console.error;
      console.error = (...args) => {
        // Log performance-related errors
        const message = args.join(' ');
        if (message.includes('performance') || message.includes('slow')) {
          console.log('%c[PERF ERROR]', 'color: red; font-weight: bold;', ...args);
        }
        originalConsoleError.apply(console, args);
      };
    }
  }

  /**
   * Get current metrics snapshot
   */
  getMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values());
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics.clear();
  }

  /**
   * Enable/disable performance monitoring
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * Check if monitoring is enabled
   */
  isMonitoringEnabled(): boolean {
    return this.isEnabled;
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor();

// React hook for component performance monitoring
export const usePerformanceMonitor = (componentName: string, metadata?: Record<string, any>) => {
  const renderTimer = performanceMonitor.measureRender(componentName, metadata);
  
  return {
    startRender: renderTimer.start,
    endRender: renderTimer.end,
    measureAsync: (name: string, fn: () => Promise<any>) => 
      performanceMonitor.measureAsync(`${componentName}_${name}`, fn),
    measure: (name: string, fn: () => any) => 
      performanceMonitor.measure(`${componentName}_${name}`, fn),
  };
};

// Decorator for measuring function performance
export const measurePerformance = (name?: string) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;
    const timerName = name || `${target.constructor.name}_${propertyKey}`;

    descriptor.value = function (...args: any[]) {
      return performanceMonitor.measure(timerName, () => originalMethod.apply(this, args));
    };

    return descriptor;
  };
};

export default performanceMonitor;
