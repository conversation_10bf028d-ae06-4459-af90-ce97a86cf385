# FoodWay Customer App - Backend Implementation Guide

## Overview
This guide provides step-by-step instructions for implementing the backend API for the FoodWay customer application using Node.js, Express, PostgreSQL, and Redis on Railway.

## Technology Stack
- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Database**: PostgreSQL 14+
- **Cache**: Redis 7+
- **Authentication**: JWT + Refresh Tokens
- **Payment**: Stripe
- **File Upload**: Cloudinary
- **Deployment**: Railway
- **Monitoring**: Winston + Morgan

## Project Structure
```
backend/
├── src/
│   ├── controllers/          # Route handlers
│   │   ├── auth.controller.js
│   │   ├── user.controller.js
│   │   ├── restaurant.controller.js
│   │   ├── order.controller.js
│   │   └── payment.controller.js
│   ├── middleware/           # Custom middleware
│   │   ├── auth.middleware.js
│   │   ├── validation.middleware.js
│   │   ├── rateLimit.middleware.js
│   │   └── error.middleware.js
│   ├── models/              # Database models
│   │   ├── User.js
│   │   ├── Restaurant.js
│   │   ├── Order.js
│   │   └── Review.js
│   ├── routes/              # Route definitions
│   │   ├── auth.routes.js
│   │   ├── user.routes.js
│   │   ├── restaurant.routes.js
│   │   └── order.routes.js
│   ├── services/            # Business logic
│   │   ├── auth.service.js
│   │   ├── email.service.js
│   │   ├── payment.service.js
│   │   └── notification.service.js
│   ├── utils/               # Utilities
│   │   ├── database.js
│   │   ├── redis.js
│   │   ├── logger.js
│   │   └── helpers.js
│   ├── config/              # Configuration
│   │   ├── database.config.js
│   │   ├── redis.config.js
│   │   └── app.config.js
│   └── app.js               # Express app setup
├── migrations/              # Database migrations
├── seeds/                   # Sample data
├── tests/                   # Test files
├── package.json
└── railway.json
```

## 1. Initial Setup

### Package.json Dependencies
```json
{
  "name": "foodway-backend",
  "version": "1.0.0",
  "main": "src/app.js",
  "scripts": {
    "start": "node src/app.js",
    "dev": "nodemon src/app.js",
    "migrate": "node migrations/run.js",
    "seed": "node seeds/run.js",
    "test": "jest"
  },
  "dependencies": {
    "express": "^4.18.2",
    "pg": "^8.11.0",
    "redis": "^4.6.7",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.0",
    "joi": "^17.9.2",
    "cors": "^2.8.5",
    "helmet": "^7.0.0",
    "express-rate-limit": "^6.7.0",
    "multer": "^1.4.5",
    "cloudinary": "^1.37.3",
    "stripe": "^12.9.0",
    "nodemailer": "^6.9.3",
    "winston": "^3.9.0",
    "morgan": "^1.10.0",
    "dotenv": "^16.1.4",
    "uuid": "^9.0.0"
  },
  "devDependencies": {
    "nodemon": "^2.0.22",
    "jest": "^29.5.0",
    "supertest": "^6.3.3"
  }
}
```

### Environment Variables (.env)
```bash
# App Configuration
NODE_ENV=development
PORT=3000
API_VERSION=v1

# Database
DATABASE_URL=postgresql://username:password@host:port/database

# Redis
REDIS_URL=redis://username:password@host:port

# JWT
JWT_SECRET=your_super_secure_jwt_secret
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_SECRET=your_refresh_token_secret
REFRESH_TOKEN_EXPIRES_IN=7d

# Stripe
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Email
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password

# Cloudinary
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# External APIs
GOOGLE_MAPS_API_KEY=your_google_maps_key

# Security
BCRYPT_ROUNDS=12
CORS_ORIGIN=http://localhost:3000,http://localhost:8081

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## 2. Database Configuration

### Database Connection (src/utils/database.js)
```javascript
const { Pool } = require('pg');
const config = require('../config/database.config');

class Database {
  constructor() {
    this.pool = new Pool(config.database);
    this.pool.on('error', (err) => {
      console.error('Database pool error:', err);
    });
  }

  async query(text, params) {
    const client = await this.pool.connect();
    try {
      const result = await client.query(text, params);
      return result;
    } finally {
      client.release();
    }
  }

  async transaction(callback) {
    const client = await this.pool.connect();
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  async close() {
    await this.pool.end();
  }
}

module.exports = new Database();
```

### Redis Configuration (src/utils/redis.js)
```javascript
const redis = require('redis');
const config = require('../config/redis.config');

class RedisClient {
  constructor() {
    this.client = redis.createClient(config.redis);
    this.client.on('error', (err) => {
      console.error('Redis error:', err);
    });
    this.client.connect();
  }

  async get(key) {
    return await this.client.get(key);
  }

  async set(key, value, ttl = 3600) {
    return await this.client.setEx(key, ttl, JSON.stringify(value));
  }

  async del(key) {
    return await this.client.del(key);
  }

  async exists(key) {
    return await this.client.exists(key);
  }

  async close() {
    await this.client.quit();
  }
}

module.exports = new RedisClient();
```

## 3. Authentication Implementation

### Auth Middleware (src/middleware/auth.middleware.js)
```javascript
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const redis = require('../utils/redis');

const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Access token required' }
      });
    }

    // Check if token is blacklisted
    const isBlacklisted = await redis.exists(`blacklist:${token}`);
    if (isBlacklisted) {
      return res.status(401).json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Token has been revoked' }
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId);

    if (!user || !user.is_active) {
      return res.status(401).json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'User not found or inactive' }
      });
    }

    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: { code: 'TOKEN_EXPIRED', message: 'Access token expired' }
      });
    }

    return res.status(401).json({
      success: false,
      error: { code: 'UNAUTHORIZED', message: 'Invalid token' }
    });
  }
};

module.exports = { authenticateToken };
```

### Auth Controller (src/controllers/auth.controller.js)
```javascript
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const authService = require('../services/auth.service');
const emailService = require('../services/email.service');
const redis = require('../utils/redis');

class AuthController {
  async register(req, res) {
    try {
      const { email, password, firstName, lastName, phone } = req.body;

      // Check if user exists
      const existingUser = await User.findByEmail(email);
      if (existingUser) {
        return res.status(409).json({
          success: false,
          error: { code: 'CONFLICT', message: 'User already exists' }
        });
      }

      // Hash password
      const passwordHash = await bcrypt.hash(password, parseInt(process.env.BCRYPT_ROUNDS));

      // Create user
      const user = await User.create({
        email,
        password_hash: passwordHash,
        first_name: firstName,
        last_name: lastName,
        phone
      });

      // Generate tokens
      const { accessToken, refreshToken } = authService.generateTokens(user.id);

      // Save refresh token
      await authService.saveRefreshToken(user.id, refreshToken);

      // Send verification email
      await emailService.sendVerificationEmail(user.email, user.id);

      res.status(201).json({
        success: true,
        data: {
          user: User.sanitize(user),
          token: accessToken,
          refreshToken
        },
        message: 'User registered successfully'
      });
    } catch (error) {
      console.error('Registration error:', error);
      res.status(500).json({
        success: false,
        error: { code: 'SERVER_ERROR', message: 'Registration failed' }
      });
    }
  }

  async login(req, res) {
    try {
      const { email, password } = req.body;

      // Find user
      const user = await User.findByEmail(email);
      if (!user) {
        return res.status(401).json({
          success: false,
          error: { code: 'UNAUTHORIZED', message: 'Invalid credentials' }
        });
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.password_hash);
      if (!isValidPassword) {
        return res.status(401).json({
          success: false,
          error: { code: 'UNAUTHORIZED', message: 'Invalid credentials' }
        });
      }

      // Check if user is active
      if (!user.is_active) {
        return res.status(401).json({
          success: false,
          error: { code: 'UNAUTHORIZED', message: 'Account is deactivated' }
        });
      }

      // Generate tokens
      const { accessToken, refreshToken } = authService.generateTokens(user.id);

      // Save refresh token
      await authService.saveRefreshToken(user.id, refreshToken);

      // Update last login
      await User.updateLastLogin(user.id);

      res.json({
        success: true,
        data: {
          user: User.sanitize(user),
          token: accessToken,
          refreshToken
        },
        message: 'Login successful'
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({
        success: false,
        error: { code: 'SERVER_ERROR', message: 'Login failed' }
      });
    }
  }

  async refreshToken(req, res) {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        return res.status(401).json({
          success: false,
          error: { code: 'UNAUTHORIZED', message: 'Refresh token required' }
        });
      }

      const result = await authService.refreshAccessToken(refreshToken);
      
      res.json({
        success: true,
        data: result,
        message: 'Token refreshed successfully'
      });
    } catch (error) {
      console.error('Token refresh error:', error);
      res.status(401).json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Invalid refresh token' }
      });
    }
  }

  async logout(req, res) {
    try {
      const token = req.headers.authorization?.split(' ')[1];
      const { refreshToken } = req.body;

      // Blacklist access token
      if (token) {
        const decoded = jwt.decode(token);
        const ttl = decoded.exp - Math.floor(Date.now() / 1000);
        if (ttl > 0) {
          await redis.set(`blacklist:${token}`, true, ttl);
        }
      }

      // Remove refresh token
      if (refreshToken) {
        await authService.revokeRefreshToken(refreshToken);
      }

      res.json({
        success: true,
        message: 'Logout successful'
      });
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({
        success: false,
        error: { code: 'SERVER_ERROR', message: 'Logout failed' }
      });
    }
  }
}

module.exports = new AuthController();
```

## 4. User Model Implementation

### User Model (src/models/User.js)
```javascript
const db = require('../utils/database');

class User {
  static async create(userData) {
    const query = `
      INSERT INTO users (email, password_hash, first_name, last_name, phone)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `;
    const values = [
      userData.email,
      userData.password_hash,
      userData.first_name,
      userData.last_name,
      userData.phone
    ];
    
    const result = await db.query(query, values);
    return result.rows[0];
  }

  static async findById(id) {
    const query = 'SELECT * FROM users WHERE id = $1';
    const result = await db.query(query, [id]);
    return result.rows[0];
  }

  static async findByEmail(email) {
    const query = 'SELECT * FROM users WHERE email = $1';
    const result = await db.query(query, [email]);
    return result.rows[0];
  }

  static async update(id, userData) {
    const fields = Object.keys(userData);
    const values = Object.values(userData);
    const setClause = fields.map((field, index) => `${field} = $${index + 1}`).join(', ');
    
    const query = `
      UPDATE users 
      SET ${setClause}, updated_at = NOW()
      WHERE id = $${fields.length + 1}
      RETURNING *
    `;
    
    const result = await db.query(query, [...values, id]);
    return result.rows[0];
  }

  static async updateLastLogin(id) {
    const query = `
      UPDATE users 
      SET last_login_at = NOW(), updated_at = NOW()
      WHERE id = $1
    `;
    await db.query(query, [id]);
  }

  static sanitize(user) {
    const { password_hash, ...sanitizedUser } = user;
    return sanitizedUser;
  }
}

module.exports = User;
```

This implementation guide provides the foundation for building a robust backend API. The next sections would cover restaurant management, order processing, payment integration, and deployment to Railway.
