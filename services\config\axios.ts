import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { API_CONFIG, ERROR_MESSAGES } from '../../utils/constants';
import {
    AppError,
    AuthenticationError,
    NetworkError,
    ValidationError
} from '../../utils/errorHandler';

// Create axios instance with base configuration
const createAxiosInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: process.env.EXPO_PUBLIC_API_URL || 'https://backend-production-f106.up.railway.app/api/v1',
    timeout: API_CONFIG.TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  });

  // Request interceptor for authentication
  instance.interceptors.request.use(
    async (config) => {
      // Get auth token from store (avoiding circular dependency)
      try {
        const authStore = await import('../../store/authStore');
        const { token } = authStore.useAuthStore.getState();

        if (token && !config.headers.skipAuth) {
          config.headers.Authorization = `Bearer ${token}`;
        }
      } catch (error) {
        console.warn('Could not access auth store:', error);
      }

      // Remove skipAuth header before sending request
      delete config.headers.skipAuth;

      console.log(`🚀 ${config.method?.toUpperCase()} ${config.url}`, {
        headers: config.headers,
        data: config.data,
      });

      return config;
    },
    (error) => {
      console.error('Request interceptor error:', error);
      return Promise.reject(error);
    }
  );

  // Response interceptor for error handling
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      console.log(`✅ ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data,
      });
      return response;
    },
    async (error: AxiosError) => {
      const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

      console.error(`❌ ${originalRequest?.method?.toUpperCase()} ${originalRequest?.url}`, {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message,
      });

      // Handle token refresh for 401 errors
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;

        try {
          const authStore = await import('../../store/authStore');
          const { refreshToken, refreshAuth } = authStore.useAuthStore.getState();

          if (refreshToken) {
            await refreshAuth();

            // Retry original request with new token
            const { token } = authStore.useAuthStore.getState();
            if (token && originalRequest.headers) {
              originalRequest.headers.Authorization = `Bearer ${token}`;
            }

            return instance(originalRequest);
          }
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError);
          const authStore = await import('../../store/authStore');
          authStore.useAuthStore.getState().logout();
          throw new AuthenticationError('Session expired. Please login again.');
        }
      }

      // Transform axios errors to custom errors
      const transformedError = transformAxiosError(error);
      return Promise.reject(transformedError);
    }
  );

  return instance;
};

// Transform axios errors to custom error types
const transformAxiosError = (error: AxiosError): AppError => {
  if (!error.response) {
    // Network error
    if (error.code === 'ECONNABORTED') {
      return new NetworkError('Request timeout');
    }
    return new NetworkError(error.message || ERROR_MESSAGES.NETWORK_ERROR);
  }

  const { status, data } = error.response;
  const message = (data as any)?.message || (data as any)?.error?.message || error.message;

  switch (status) {
    case 400:
      return new ValidationError(message || 'Invalid request data');
    case 401:
      return new AuthenticationError(message || 'Authentication required');
    case 403:
      return new AuthenticationError(message || 'Access denied');
    case 404:
      return new AppError(message || 'Resource not found', 'NOT_FOUND');
    case 409:
      return new ValidationError(message || 'Conflict with existing data');
    case 422:
      return new ValidationError(message || 'Validation failed');
    case 429:
      return new AppError(message || 'Too many requests', 'RATE_LIMIT');
    case 500:
    case 502:
    case 503:
    case 504:
      return new AppError(message || 'Server error', 'SERVER_ERROR');
    default:
      return new AppError(message || 'An unexpected error occurred', 'UNKNOWN');
  }
};

// Create and export the configured axios instance
export const axiosInstance = createAxiosInstance();

// Export axios types for convenience
export type { AxiosError, AxiosRequestConfig, AxiosResponse };

// Convenience methods that match the existing API client interface
export const axiosClient = {
  get: <T = any>(url: string, config?: AxiosRequestConfig) => 
    axiosInstance.get<T>(url, config).then(response => response.data),
  
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => 
    axiosInstance.post<T>(url, data, config).then(response => response.data),
  
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => 
    axiosInstance.put<T>(url, data, config).then(response => response.data),
  
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => 
    axiosInstance.patch<T>(url, data, config).then(response => response.data),
  
  delete: <T = any>(url: string, config?: AxiosRequestConfig) => 
    axiosInstance.delete<T>(url, config).then(response => response.data),
};

export default axiosInstance;
