import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { AppError } from '../../utils/errorHandler';
import { errorLogger, UserFeedback } from '../../utils/errorLogger';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../utils/constants';

interface ErrorReportModalProps {
  visible: boolean;
  error: AppError | null;
  onClose: () => void;
  onSubmit?: (feedbackId: string) => void;
}

export const ErrorReportModal: React.FC<ErrorReportModalProps> = ({
  visible,
  error,
  onClose,
  onSubmit,
}) => {
  const [email, setEmail] = useState('');
  const [description, setDescription] = useState('');
  const [rating, setRating] = useState<1 | 2 | 3 | 4 | 5>(3);
  const [reproduced, setReproduced] = useState(false);
  const [additionalInfo, setAdditionalInfo] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!error) return;

    if (!description.trim()) {
      Alert.alert('Error', 'Please provide a description of the issue.');
      return;
    }

    setIsSubmitting(true);

    try {
      const feedback: Omit<UserFeedback, 'id' | 'errorId' | 'timestamp'> = {
        email: email.trim() || undefined,
        description: description.trim(),
        rating,
        reproduced,
        additionalInfo: additionalInfo.trim() || undefined,
      };

      const feedbackId = await errorLogger.submitUserFeedback(error.code || 'unknown', feedback);
      
      Alert.alert(
        'Thank You!',
        'Your feedback has been submitted. We appreciate your help in improving the app.',
        [{ text: 'OK', onPress: onClose }]
      );

      if (onSubmit) {
        onSubmit(feedbackId);
      }

      // Reset form
      setEmail('');
      setDescription('');
      setRating(3);
      setReproduced(false);
      setAdditionalInfo('');
    } catch (submitError) {
      Alert.alert(
        'Submission Failed',
        'Unable to submit your feedback. Please try again later.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStarRating = () => {
    return (
      <View style={styles.ratingContainer}>
        <Text style={styles.ratingLabel}>How would you rate this issue?</Text>
        <View style={styles.starsContainer}>
          {[1, 2, 3, 4, 5].map((star) => (
            <TouchableOpacity
              key={star}
              onPress={() => setRating(star as 1 | 2 | 3 | 4 | 5)}
              style={styles.starButton}
            >
              <Ionicons
                name={star <= rating ? 'star' : 'star-outline'}
                size={24}
                color={star <= rating ? COLORS.warning : COLORS.gray[400]}
              />
            </TouchableOpacity>
          ))}
        </View>
        <Text style={styles.ratingDescription}>
          {rating === 1 && 'Minor issue'}
          {rating === 2 && 'Noticeable issue'}
          {rating === 3 && 'Moderate issue'}
          {rating === 4 && 'Significant issue'}
          {rating === 5 && 'Critical issue'}
        </Text>
      </View>
    );
  };

  if (!visible || !error) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.header}>
          <Text style={styles.title}>Report Issue</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={COLORS.textPrimary} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Error Information */}
          <View style={styles.errorInfo}>
            <Text style={styles.sectionTitle}>Error Details</Text>
            <View style={styles.errorCard}>
              <Text style={styles.errorType}>{error.type}</Text>
              <Text style={styles.errorMessage}>{error.userMessage}</Text>
              {error.code && (
                <Text style={styles.errorCode}>Code: {error.code}</Text>
              )}
            </View>
          </View>

          {/* Rating */}
          {renderStarRating()}

          {/* Email (Optional) */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>
              Email (Optional)
              <Text style={styles.optional}> - for follow-up</Text>
            </Text>
            <TextInput
              style={styles.input}
              placeholder="<EMAIL>"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          {/* Description */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>
              Description <Text style={styles.required}>*</Text>
            </Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Please describe what you were doing when this error occurred..."
              value={description}
              onChangeText={setDescription}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          {/* Reproduction */}
          <View style={styles.checkboxContainer}>
            <TouchableOpacity
              style={styles.checkbox}
              onPress={() => setReproduced(!reproduced)}
            >
              <Ionicons
                name={reproduced ? 'checkbox' : 'square-outline'}
                size={20}
                color={reproduced ? COLORS.primary : COLORS.gray[400]}
              />
              <Text style={styles.checkboxLabel}>
                I can reproduce this issue consistently
              </Text>
            </TouchableOpacity>
          </View>

          {/* Additional Information */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Additional Information (Optional)</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Any additional details that might help us understand the issue..."
              value={additionalInfo}
              onChangeText={setAdditionalInfo}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>

          {/* Submit Button */}
          <TouchableOpacity
            style={[styles.submitButton, isSubmitting && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={isSubmitting}
          >
            <Text style={styles.submitButtonText}>
              {isSubmitting ? 'Submitting...' : 'Submit Report'}
            </Text>
          </TouchableOpacity>

          {/* Privacy Note */}
          <Text style={styles.privacyNote}>
            Your feedback helps us improve the app. We may collect technical information 
            about the error to help with debugging. No personal data will be shared.
          </Text>
        </ScrollView>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  title: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
  },
  closeButton: {
    padding: SPACING.xs,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  errorInfo: {
    marginTop: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  errorCard: {
    backgroundColor: COLORS.error + '10',
    borderColor: COLORS.error + '30',
    borderWidth: 1,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
  },
  errorType: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '600',
    color: COLORS.error,
    marginBottom: SPACING.xs,
  },
  errorMessage: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  errorCode: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    fontFamily: 'monospace',
  },
  ratingContainer: {
    marginBottom: SPACING.lg,
  },
  ratingLabel: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  starsContainer: {
    flexDirection: 'row',
    marginBottom: SPACING.xs,
  },
  starButton: {
    padding: SPACING.xs,
    marginRight: SPACING.xs,
  },
  ratingDescription: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
  },
  inputContainer: {
    marginBottom: SPACING.lg,
  },
  label: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  optional: {
    fontWeight: 'normal',
    color: COLORS.textSecondary,
  },
  required: {
    color: COLORS.error,
  },
  input: {
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderWidth: 1,
    borderColor: COLORS.border,
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textPrimary,
  },
  textArea: {
    minHeight: 80,
    paddingTop: SPACING.sm,
  },
  checkboxContainer: {
    marginBottom: SPACING.lg,
  },
  checkbox: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkboxLabel: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textPrimary,
    marginLeft: SPACING.sm,
    flex: 1,
  },
  submitButton: {
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.lg,
    paddingVertical: SPACING.md,
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  submitButtonDisabled: {
    opacity: 0.6,
  },
  submitButtonText: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  privacyNote: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: SPACING.xl,
  },
});

export default ErrorReportModal;
