<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- PRODUCTION: HTTPS-ONLY Configuration -->
    <!-- Only allow HTTPS connections to production backend -->
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">backend-production-f106.up.railway.app</domain>
        <domain includeSubdomains="true">railway.app</domain>
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </domain-config>

    <!-- Local development only (remove in production builds) -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">*************</domain>
    </domain-config>

    <!-- Default: HTTPS-ONLY for all other domains -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
