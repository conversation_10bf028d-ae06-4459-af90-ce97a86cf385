import { performanceMonitor } from './performanceMonitor';
import { memoryManager } from './memoryManager';
import { optimizedApiService } from '../services/optimizedApiService';

interface PerformanceTestResult {
  testName: string;
  duration: number;
  success: boolean;
  details?: any;
}

class PerformanceTestSuite {
  private results: PerformanceTestResult[] = [];

  /**
   * Run all performance tests
   */
  async runAllTests(): Promise<PerformanceTestResult[]> {
    console.log('🚀 Starting Performance Test Suite...');
    
    this.results = [];
    
    await this.testApiPerformance();
    await this.testMemoryManagement();
    await this.testCachePerformance();
    await this.testRenderPerformance();
    
    this.printResults();
    return this.results;
  }

  /**
   * Test API performance
   */
  private async testApiPerformance(): Promise<void> {
    console.log('📡 Testing API Performance...');
    
    try {
      const startTime = Date.now();
      
      // Test multiple concurrent requests
      const requests = [
        optimizedApiService.request('/api/restaurants'),
        optimizedApiService.request('/api/categories'),
        optimizedApiService.request('/api/featured'),
      ];
      
      await Promise.all(requests);
      const duration = Date.now() - startTime;
      
      this.results.push({
        testName: 'API Concurrent Requests',
        duration,
        success: duration < 2000, // Should complete in under 2 seconds
        details: { requestCount: requests.length }
      });
      
      // Test cache performance
      const cacheStartTime = Date.now();
      await optimizedApiService.request('/api/restaurants'); // Should be cached
      const cacheDuration = Date.now() - cacheStartTime;
      
      this.results.push({
        testName: 'API Cache Performance',
        duration: cacheDuration,
        success: cacheDuration < 100, // Cached requests should be very fast
        details: { cached: true }
      });
      
    } catch (error) {
      this.results.push({
        testName: 'API Performance',
        duration: -1,
        success: false,
        details: { error: error.message }
      });
    }
  }

  /**
   * Test memory management
   */
  private async testMemoryManagement(): Promise<void> {
    console.log('🧠 Testing Memory Management...');
    
    try {
      const initialStats = memoryManager.getMemoryStats();
      
      // Create some test data
      const testCache = memoryManager.createCache('test');
      for (let i = 0; i < 1000; i++) {
        testCache.set(`key_${i}`, { data: `value_${i}`, timestamp: Date.now() });
      }
      
      const afterCreationStats = memoryManager.getMemoryStats();
      
      // Cleanup
      memoryManager.clearCache('test');
      const afterCleanupStats = memoryManager.getMemoryStats();
      
      this.results.push({
        testName: 'Memory Management',
        duration: 0,
        success: afterCleanupStats.caches.test === 0,
        details: {
          initial: initialStats,
          afterCreation: afterCreationStats,
          afterCleanup: afterCleanupStats
        }
      });
      
    } catch (error) {
      this.results.push({
        testName: 'Memory Management',
        duration: -1,
        success: false,
        details: { error: error.message }
      });
    }
  }

  /**
   * Test cache performance
   */
  private async testCachePerformance(): Promise<void> {
    console.log('💾 Testing Cache Performance...');
    
    try {
      const cacheStats = optimizedApiService.getCacheStats();
      
      // Test cache hit rate
      const testEndpoint = '/api/test-cache';
      
      // First request (cache miss)
      const missStartTime = Date.now();
      try {
        await optimizedApiService.request(testEndpoint);
      } catch (error) {
        // Expected to fail for test endpoint
      }
      const missDuration = Date.now() - missStartTime;
      
      // Second request (cache hit)
      const hitStartTime = Date.now();
      try {
        await optimizedApiService.request(testEndpoint);
      } catch (error) {
        // Expected to fail for test endpoint
      }
      const hitDuration = Date.now() - hitStartTime;
      
      this.results.push({
        testName: 'Cache Performance',
        duration: hitDuration,
        success: hitDuration < missDuration, // Cache hit should be faster
        details: {
          cacheStats,
          missDuration,
          hitDuration,
          improvement: missDuration - hitDuration
        }
      });
      
    } catch (error) {
      this.results.push({
        testName: 'Cache Performance',
        duration: -1,
        success: false,
        details: { error: error.message }
      });
    }
  }

  /**
   * Test render performance
   */
  private async testRenderPerformance(): Promise<void> {
    console.log('🎨 Testing Render Performance...');
    
    try {
      // Simulate component render timing
      performanceMonitor.startTimer('test_render');
      
      // Simulate some work
      await new Promise(resolve => setTimeout(resolve, 50));
      
      const renderDuration = performanceMonitor.endTimer('test_render');
      
      this.results.push({
        testName: 'Render Performance',
        duration: renderDuration || 0,
        success: (renderDuration || 0) < 100, // Should render in under 100ms
        details: { renderDuration }
      });
      
    } catch (error) {
      this.results.push({
        testName: 'Render Performance',
        duration: -1,
        success: false,
        details: { error: error.message }
      });
    }
  }

  /**
   * Print test results
   */
  private printResults(): void {
    console.log('\n📊 Performance Test Results:');
    console.log('================================');
    
    let passedTests = 0;
    let totalTests = this.results.length;
    
    this.results.forEach(result => {
      const status = result.success ? '✅ PASS' : '❌ FAIL';
      const duration = result.duration >= 0 ? `${result.duration}ms` : 'N/A';
      
      console.log(`${status} ${result.testName}: ${duration}`);
      
      if (result.details && !result.success) {
        console.log(`   Details: ${JSON.stringify(result.details, null, 2)}`);
      }
      
      if (result.success) passedTests++;
    });
    
    console.log('================================');
    console.log(`📈 Summary: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('🎉 All performance tests passed!');
    } else {
      console.log('⚠️ Some performance tests failed. Check the details above.');
    }
  }

  /**
   * Get test results
   */
  getResults(): PerformanceTestResult[] {
    return this.results;
  }

  /**
   * Run a specific test
   */
  async runTest(testName: string): Promise<PerformanceTestResult | null> {
    switch (testName) {
      case 'api':
        await this.testApiPerformance();
        break;
      case 'memory':
        await this.testMemoryManagement();
        break;
      case 'cache':
        await this.testCachePerformance();
        break;
      case 'render':
        await this.testRenderPerformance();
        break;
      default:
        console.warn(`Unknown test: ${testName}`);
        return null;
    }
    
    return this.results[this.results.length - 1] || null;
  }
}

// Create singleton instance
export const performanceTestSuite = new PerformanceTestSuite();

// Utility function to run quick performance check
export const runQuickPerformanceCheck = async (): Promise<void> => {
  console.log('⚡ Running Quick Performance Check...');
  
  const results = await performanceTestSuite.runAllTests();
  
  // Send results to analytics in production
  if (!__DEV__) {
    // Analytics.track('performance_test', { results });
  }
};

// Development helper
export const runPerformanceTest = (testName?: string): Promise<any> => {
  if (testName) {
    return performanceTestSuite.runTest(testName);
  }
  return performanceTestSuite.runAllTests();
};

export default performanceTestSuite;
