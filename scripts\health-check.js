#!/usr/bin/env node

const http = require('http');
const { healthCheck } = require('../services/database/index');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

async function checkDatabaseHealth() {
  try {
    log('🔍 Checking database connections...', colors.blue);
    const health = await healthCheck();

    log('📊 Health Check Results:', colors.bright);
    log(`Overall Status: ${health.overall}`,
      health.overall === 'healthy' ? colors.green : colors.red);

    log(`Database: ${health.database.status}`,
      health.database.status === 'connected' ? colors.green : colors.red);
    if (health.database.latency) {
      log(`  Latency: ${health.database.latency}ms`, colors.cyan);
    }

    log(`Redis: ${health.redis.status}`,
      health.redis.status === 'connected' ? colors.green : colors.red);
    if (health.redis.latency) {
      log(`  Latency: ${health.redis.latency}ms`, colors.cyan);
    }

    log(`Timestamp: ${health.timestamp}`, colors.cyan);

    return health.overall === 'healthy';
  } catch (error) {
    log('❌ Health check failed:', colors.red);
    log(error.message, colors.red);
    return false;
  }
}

async function checkApiHealth(url = 'https://backend-production-f106.up.railway.app') {
  return new Promise((resolve) => {
    const healthUrl = `${url}/health`;
    log(`🌐 Checking API health: ${healthUrl}`, colors.blue);

    const request = http.get(healthUrl, (response) => {
      let data = '';

      response.on('data', (chunk) => {
        data += chunk;
      });

      response.on('end', () => {
        try {
          const healthData = JSON.parse(data);
          log('📊 API Health Check Results:', colors.bright);
          log(`Status: ${response.statusCode}`,
            response.statusCode === 200 ? colors.green : colors.red);
          log(`Response: ${JSON.stringify(healthData, null, 2)}`, colors.cyan);
          resolve(response.statusCode === 200);
        } catch (error) {
          log('❌ Invalid JSON response from API', colors.red);
          resolve(false);
        }
      });
    });

    request.on('error', (error) => {
      log(`❌ API health check failed: ${error.message}`, colors.red);
      resolve(false);
    });

    request.setTimeout(5000, () => {
      log('❌ API health check timed out', colors.red);
      request.destroy();
      resolve(false);
    });
  });
}

async function runFullHealthCheck() {
  log('🏥 FoodWay Customer App Health Check', colors.bright);
  log('='.repeat(50), colors.cyan);

  const results = {
    database: false,
    api: false,
    overall: false
  };

  // Check database health
  results.database = await checkDatabaseHealth();

  // Check API health if URL is provided
  const apiUrl = process.env.EXPO_PUBLIC_API_URL || process.argv[2];
  if (apiUrl) {
    results.api = await checkApiHealth(apiUrl);
  } else {
    log('⚠️  No API URL provided, skipping API health check', colors.yellow);
    log('Usage: node scripts/health-check.js [API_URL]', colors.cyan);
    results.api = true; // Don't fail overall check if API URL not provided
  }

  results.overall = results.database && results.api;

  log('', colors.reset);
  log('📋 Summary:', colors.bright);
  log(`Database: ${results.database ? '✅ Healthy' : '❌ Unhealthy'}`,
    results.database ? colors.green : colors.red);
  log(`API: ${results.api ? '✅ Healthy' : '❌ Unhealthy'}`,
    results.api ? colors.green : colors.red);
  log(`Overall: ${results.overall ? '✅ Healthy' : '❌ Unhealthy'}`,
    results.overall ? colors.green : colors.red);

  if (results.overall) {
    log('🎉 All systems operational!', colors.green);
    process.exit(0);
  } else {
    log('⚠️  Some systems are not healthy', colors.yellow);
    process.exit(1);
  }
}

// Create a simple HTTP health check server
function createHealthCheckServer(port = 3001) {
  const server = http.createServer(async (req, res) => {
    if (req.url === '/health' && req.method === 'GET') {
      try {
        const health = await healthCheck();
        const statusCode = health.overall === 'healthy' ? 200 : 503;

        res.writeHead(statusCode, {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        });

        res.end(JSON.stringify({
          status: health.overall,
          timestamp: health.timestamp,
          services: {
            database: health.database,
            redis: health.redis,
          },
          version: process.env.APP_VERSION || '1.0.0',
          environment: process.env.NODE_ENV || 'development',
        }, null, 2));
      } catch (error) {
        res.writeHead(503, {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        });

        res.end(JSON.stringify({
          status: 'unhealthy',
          error: error.message,
          timestamp: new Date().toISOString(),
        }, null, 2));
      }
    } else {
      res.writeHead(404, { 'Content-Type': 'text/plain' });
      res.end('Not Found');
    }
  });

  server.listen(port, () => {
    log(`🏥 Health check server running on port ${port}`, colors.green);
    log(`Health endpoint: http://localhost:${port}/health`, colors.cyan);
  });

  return server;
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
Health Check Script for FoodWay Customer App

Usage: 
  node scripts/health-check.js [API_URL]           # Run health check
  node scripts/health-check.js --server [PORT]    # Start health check server

Options:
  --server [PORT]   Start a health check server (default port: 3001)
  --help, -h        Show this help message

Examples:
  node scripts/health-check.js                                    # Check database only
  node scripts/health-check.js http://localhost:3000              # Check database and API
  node scripts/health-check.js https://your-app.railway.app       # Check production API
  node scripts/health-check.js --server                           # Start health server on port 3001
  node scripts/health-check.js --server 8080                      # Start health server on port 8080
  `);
  process.exit(0);
}

if (process.argv.includes('--server')) {
  const portIndex = process.argv.indexOf('--server') + 1;
  const port = process.argv[portIndex] && !isNaN(process.argv[portIndex])
    ? parseInt(process.argv[portIndex], 10)
    : 3001;

  createHealthCheckServer(port);
} else if (require.main === module) {
  runFullHealthCheck();
}

module.exports = {
  checkDatabaseHealth,
  checkApiHealth,
  runFullHealthCheck,
  createHealthCheckServer,
};
