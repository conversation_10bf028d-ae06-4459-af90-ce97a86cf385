# FoodWay Customer App - API Specification

## Overview
This document outlines the complete API specification and database structure required for the FoodWay customer mobile application. The backend should be built as a RESTful API with JWT authentication.

## Base URL
- **Development**: `http://localhost:3000/api`
- **Production**: `https://your-railway-app.railway.app/api`

## Authentication
All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Response Format
All API responses follow this standard format:
```json
{
  "success": true,
  "data": {},
  "message": "Success message",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

Error responses:
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "details": {}
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

## 1. Authentication Endpoints

### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "phone": "+**********"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "phone": "+**********",
      "emailVerified": false,
      "phoneVerified": false,
      "avatarUrl": null,
      "createdAt": "2024-01-01T00:00:00.000Z"
    },
    "token": "jwt_token",
    "refreshToken": "refresh_token"
  }
}
```

### POST /auth/login
Authenticate user and get access token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response:** Same as register response.

### POST /auth/refresh
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refreshToken": "refresh_token"
}
```

### POST /auth/logout
Logout user and invalidate tokens.

**Headers:** `Authorization: Bearer <token>`

### POST /auth/forgot-password
Request password reset email.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

### POST /auth/reset-password
Reset password using reset token.

**Request Body:**
```json
{
  "token": "reset_token",
  "newPassword": "newSecurePassword123"
}
```

### POST /auth/verify-email
Verify email address using verification token.

**Request Body:**
```json
{
  "token": "verification_token"
}
```

---

## 2. User Profile Endpoints

### GET /users/profile
Get current user profile.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "phone": "+**********",
    "avatarUrl": "https://example.com/avatar.jpg",
    "emailVerified": true,
    "phoneVerified": false,
    "addresses": [],
    "paymentMethods": [],
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### PUT /users/profile
Update user profile.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "phone": "+**********"
}
```

### POST /users/upload-avatar
Upload user avatar image.

**Headers:** `Authorization: Bearer <token>`

**Request:** Multipart form data with image file.

---

## 3. Address Management Endpoints

### GET /users/addresses
Get user's saved addresses.

**Headers:** `Authorization: Bearer <token>`

### POST /users/addresses
Add new address.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "type": "home",
  "label": "Home",
  "streetAddress": "123 Main St",
  "city": "New York",
  "state": "NY",
  "postalCode": "10001",
  "country": "US",
  "latitude": 40.7128,
  "longitude": -74.0060,
  "isDefault": true
}
```

### PUT /users/addresses/:id
Update address.

### DELETE /users/addresses/:id
Delete address.

---

## 4. Payment Methods Endpoints

### GET /users/payment-methods
Get user's saved payment methods.

**Headers:** `Authorization: Bearer <token>`

### POST /users/payment-methods
Add new payment method (Stripe integration).

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "stripePaymentMethodId": "pm_**********",
  "isDefault": true
}
```

### DELETE /users/payment-methods/:id
Delete payment method.

---

## 5. Restaurant Endpoints

### GET /restaurants
Get list of restaurants with filtering and pagination.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `latitude`: User latitude for distance calculation
- `longitude`: User longitude for distance calculation
- `radius`: Search radius in meters (default: 10000)
- `cuisine`: Filter by cuisine type
- `rating`: Minimum rating filter
- `priceRange`: Price range filter (1-4)
- `deliveryFee`: Maximum delivery fee
- `search`: Search query for restaurant name
- `sortBy`: Sort by (distance, rating, deliveryTime, popularity)
- `featured`: Filter featured restaurants only

**Response:**
```json
{
  "success": true,
  "data": {
    "restaurants": [
      {
        "id": "uuid",
        "name": "Mario's Pizza Palace",
        "description": "Authentic Italian pizza",
        "cuisineType": "Italian",
        "logoUrl": "https://example.com/logo.jpg",
        "coverImageUrl": "https://example.com/cover.jpg",
        "rating": 4.5,
        "reviewCount": 324,
        "priceRange": 2,
        "deliveryFee": 2.99,
        "minimumOrder": 15.00,
        "deliveryTimeMin": 25,
        "deliveryTimeMax": 35,
        "distance": 1.2,
        "isOpen": true,
        "isFeatured": true,
        "address": {
          "streetAddress": "123 Main St",
          "city": "New York",
          "state": "NY"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "totalPages": 8
    }
  }
}
```

### GET /restaurants/:id
Get restaurant details with menu.

**Response:**
```json
{
  "success": true,
  "data": {
    "restaurant": {
      "id": "uuid",
      "name": "Mario's Pizza Palace",
      "description": "Authentic Italian pizza made with fresh ingredients",
      "cuisineType": "Italian",
      "phone": "+**********",
      "email": "<EMAIL>",
      "websiteUrl": "https://mariospizza.com",
      "logoUrl": "https://example.com/logo.jpg",
      "coverImageUrl": "https://example.com/cover.jpg",
      "address": {
        "streetAddress": "123 Main St",
        "city": "New York",
        "state": "NY",
        "postalCode": "10001",
        "latitude": 40.7128,
        "longitude": -74.0060
      },
      "rating": 4.5,
      "reviewCount": 324,
      "priceRange": 2,
      "deliveryFee": 2.99,
      "minimumOrder": 15.00,
      "deliveryTimeMin": 25,
      "deliveryTimeMax": 35,
      "isOpen": true,
      "isFeatured": true,
      "menu": {
        "categories": [
          {
            "id": "uuid",
            "name": "Pizzas",
            "description": "Our signature pizzas",
            "sortOrder": 1,
            "items": [
              {
                "id": "uuid",
                "name": "Margherita Pizza",
                "description": "Fresh mozzarella, tomato sauce, basil",
                "price": 18.99,
                "imageUrl": "https://example.com/pizza.jpg",
                "isVegetarian": true,
                "isVegan": false,
                "isGlutenFree": false,
                "allergens": ["dairy", "gluten"],
                "calories": 320,
                "prepTime": 15,
                "isAvailable": true,
                "customizations": [
                  {
                    "name": "Size",
                    "required": true,
                    "options": [
                      {"name": "Small", "price": 0},
                      {"name": "Medium", "price": 3},
                      {"name": "Large", "price": 6}
                    ]
                  }
                ],
                "addOns": [
                  {"name": "Extra Cheese", "price": 2.50},
                  {"name": "Pepperoni", "price": 3.00}
                ]
              }
            ]
          }
        ]
      }
    }
  }
}
```

### GET /restaurants/featured
Get featured restaurants.

### GET /restaurants/nearby
Get nearby restaurants based on location.

**Query Parameters:**
- `latitude`: Required
- `longitude`: Required
- `radius`: Optional (default: 10000 meters)

---

## 6. Search Endpoints

### GET /search/restaurants
Search restaurants by name, cuisine, or menu items.

**Query Parameters:**
- `q`: Search query
- `latitude`: User latitude
- `longitude`: User longitude
- `filters`: JSON string with filters

### GET /search/suggestions
Get search suggestions and autocomplete.

**Query Parameters:**
- `q`: Partial search query

---

## 7. Cart & Orders Endpoints

### POST /orders
Create new order.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "restaurantId": "uuid",
  "items": [
    {
      "menuItemId": "uuid",
      "quantity": 2,
      "customizations": [
        {"name": "Size", "value": "Large", "price": 6}
      ],
      "addOns": [
        {"name": "Extra Cheese", "price": 2.50}
      ],
      "specialInstructions": "Extra crispy"
    }
  ],
  "deliveryAddress": {
    "streetAddress": "456 Oak Ave",
    "city": "New York",
    "state": "NY",
    "postalCode": "10002",
    "latitude": 40.7589,
    "longitude": -73.9851
  },
  "paymentMethodId": "uuid",
  "tipAmount": 5.00,
  "specialInstructions": "Ring doorbell twice"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "order": {
      "id": "uuid",
      "orderNumber": "FW-2024-001234",
      "status": "pending",
      "restaurant": {
        "id": "uuid",
        "name": "Mario's Pizza Palace",
        "phone": "+**********"
      },
      "items": [...],
      "subtotal": 45.98,
      "taxAmount": 4.14,
      "deliveryFee": 2.99,
      "tipAmount": 5.00,
      "totalAmount": 58.11,
      "estimatedDeliveryTime": "2024-01-01T19:30:00.000Z",
      "createdAt": "2024-01-01T18:00:00.000Z"
    }
  }
}
```

### GET /orders
Get user's order history.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `page`: Page number
- `limit`: Items per page
- `status`: Filter by order status

### GET /orders/:id
Get specific order details.

**Headers:** `Authorization: Bearer <token>`

### GET /orders/:id/tracking
Get real-time order tracking information.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "order": {
      "id": "uuid",
      "status": "on_the_way",
      "estimatedDeliveryTime": "2024-01-01T19:30:00.000Z"
    },
    "tracking": [
      {
        "status": "confirmed",
        "message": "Order confirmed by restaurant",
        "timestamp": "2024-01-01T18:05:00.000Z"
      },
      {
        "status": "preparing",
        "message": "Your order is being prepared",
        "timestamp": "2024-01-01T18:10:00.000Z"
      },
      {
        "status": "on_the_way",
        "message": "Driver is on the way",
        "latitude": 40.7589,
        "longitude": -73.9851,
        "timestamp": "2024-01-01T18:45:00.000Z"
      }
    ],
    "driver": {
      "name": "John Driver",
      "phone": "+**********",
      "currentLocation": {
        "latitude": 40.7589,
        "longitude": -73.9851
      }
    }
  }
}
```

---

## 8. Reviews Endpoints

### GET /restaurants/:id/reviews
Get restaurant reviews.

**Query Parameters:**
- `page`: Page number
- `limit`: Items per page
- `rating`: Filter by rating

### POST /reviews
Submit restaurant review.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "restaurantId": "uuid",
  "orderId": "uuid",
  "rating": 5,
  "comment": "Amazing food and fast delivery!",
  "images": ["https://example.com/review1.jpg"]
}
```

### PUT /reviews/:id
Update review.

### DELETE /reviews/:id
Delete review.

---

## 9. Notifications Endpoints

### GET /notifications
Get user notifications.

**Headers:** `Authorization: Bearer <token>`

### PUT /notifications/:id/read
Mark notification as read.

### POST /notifications/register-token
Register push notification token.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "token": "expo_push_token",
  "platform": "ios"
}
```

---

## 10. Payment Endpoints

### POST /payments/create-intent
Create Stripe payment intent.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "amount": 5811,
  "currency": "usd",
  "paymentMethodId": "pm_**********"
}
```

### POST /payments/confirm
Confirm payment.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "paymentIntentId": "pi_**********"
}
```

---

## 11. Utility Endpoints

### GET /health
Health check endpoint.

### GET /config
Get app configuration (public settings only).

**Response:**
```json
{
  "success": true,
  "data": {
    "stripePublishableKey": "pk_test_...",
    "googleMapsApiKey": "AIza...",
    "supportEmail": "<EMAIL>",
    "supportPhone": "******-FOODWAY",
    "appVersion": "1.0.0",
    "features": {
      "socialLogin": true,
      "applePay": true,
      "googlePay": true
    }
  }
}
```

---

## Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Request validation failed |
| `UNAUTHORIZED` | Authentication required |
| `FORBIDDEN` | Insufficient permissions |
| `NOT_FOUND` | Resource not found |
| `CONFLICT` | Resource already exists |
| `RATE_LIMITED` | Too many requests |
| `PAYMENT_FAILED` | Payment processing failed |
| `RESTAURANT_CLOSED` | Restaurant is closed |
| `OUT_OF_DELIVERY_AREA` | Address outside delivery area |
| `MINIMUM_ORDER_NOT_MET` | Order below minimum amount |
| `ITEM_UNAVAILABLE` | Menu item not available |
| `SERVER_ERROR` | Internal server error |

---

## Rate Limiting

- **Authentication endpoints**: 5 requests per minute per IP
- **General API**: 100 requests per minute per user
- **Search endpoints**: 30 requests per minute per user
- **Order creation**: 10 requests per minute per user

---

## Webhooks

### Stripe Webhooks
Handle payment events at `/webhooks/stripe`:
- `payment_intent.succeeded`
- `payment_intent.payment_failed`
- `payment_method.attached`

### Order Status Updates
Real-time order updates should be pushed via:
- WebSocket connections
- Push notifications
- Webhook callbacks (if applicable)
