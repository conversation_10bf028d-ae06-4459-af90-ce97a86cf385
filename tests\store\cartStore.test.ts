import { renderHook, act } from '@testing-library/react-native';
import { useCartStore } from '../../store/cartStore';
import { MenuItem, Restaurant, SelectedCustomization, SelectedAddOn } from '../../types';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

describe('CartStore', () => {
  const mockRestaurant: Restaurant = {
    id: 'rest1',
    name: 'Test Restaurant',
    description: 'Test Description',
    image: 'test-image.jpg',
    cuisine: ['Italian'],
    rating: 4.5,
    reviewCount: 100,
    deliveryTime: '30 min',
    deliveryFee: 2.99,
    minimumOrder: 15,
    isOpen: true,
    address: 'Test Address',
    latitude: 0,
    longitude: 0,
    phone: '+1234567890',
    categories: [],
    featured: false,
    promoted: false,
    tags: [],
  };

  const mockMenuItem: MenuItem = {
    id: 'item1',
    restaurantId: 'rest1',
    categoryId: 'cat1',
    name: 'Test Pizza',
    description: 'Test Description',
    image: 'test-pizza.jpg',
    price: 15.99,
    isAvailable: true,
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: false,
    isSpicy: false,
    customizations: [
      {
        id: 'size',
        name: 'Size',
        type: 'single',
        required: true,
        options: [
          { id: 'small', name: 'Small', price: 0, isDefault: true },
          { id: 'large', name: 'Large', price: 3 },
        ],
      },
    ],
    addOns: [
      { id: 'cheese', name: 'Extra Cheese', price: 2 },
    ],
    tags: [],
  };

  beforeEach(() => {
    // Reset store state
    useCartStore.setState({
      items: [],
      restaurant: null,
      subtotal: 0,
      deliveryFee: 0,
      tax: 0,
      tip: 0,
      total: 0,
      isLoading: false,
      error: null,
    });
  });

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      const { result } = renderHook(() => useCartStore());
      
      expect(result.current.items).toEqual([]);
      expect(result.current.restaurant).toBeNull();
      expect(result.current.subtotal).toBe(0);
      expect(result.current.deliveryFee).toBe(0);
      expect(result.current.tax).toBe(0);
      expect(result.current.tip).toBe(0);
      expect(result.current.total).toBe(0);
    });
  });

  describe('Set Restaurant', () => {
    it('should set restaurant and delivery fee', () => {
      const { result } = renderHook(() => useCartStore());

      act(() => {
        result.current.setRestaurant(mockRestaurant);
      });

      expect(result.current.restaurant).toEqual(mockRestaurant);
      expect(result.current.deliveryFee).toBe(mockRestaurant.deliveryFee);
    });

    it('should clear cart when switching restaurants', () => {
      const { result } = renderHook(() => useCartStore());

      // Add item to cart
      act(() => {
        result.current.setRestaurant(mockRestaurant);
        result.current.addItem(mockMenuItem, [], []);
      });

      expect(result.current.items).toHaveLength(1);

      // Switch to different restaurant
      const differentRestaurant = { ...mockRestaurant, id: 'rest2', name: 'Different Restaurant' };
      
      act(() => {
        result.current.setRestaurant(differentRestaurant);
      });

      expect(result.current.items).toHaveLength(0);
      expect(result.current.restaurant).toEqual(differentRestaurant);
    });
  });

  describe('Add Item', () => {
    beforeEach(() => {
      const { result } = renderHook(() => useCartStore());
      act(() => {
        result.current.setRestaurant(mockRestaurant);
      });
    });

    it('should add item to cart', () => {
      const { result } = renderHook(() => useCartStore());

      act(() => {
        result.current.addItem(mockMenuItem, [], []);
      });

      expect(result.current.items).toHaveLength(1);
      expect(result.current.items[0].menuItem).toEqual(mockMenuItem);
      expect(result.current.items[0].quantity).toBe(1);
      expect(result.current.items[0].totalPrice).toBe(mockMenuItem.price);
    });

    it('should add item with customizations', () => {
      const { result } = renderHook(() => useCartStore());
      
      const customizations: SelectedCustomization[] = [
        { customizationId: 'size', optionIds: ['large'] },
      ];

      act(() => {
        result.current.addItem(mockMenuItem, customizations, []);
      });

      expect(result.current.items[0].customizations).toEqual(customizations);
      expect(result.current.items[0].totalPrice).toBe(mockMenuItem.price + 3); // Large size adds $3
    });

    it('should add item with add-ons', () => {
      const { result } = renderHook(() => useCartStore());
      
      const addOns: SelectedAddOn[] = [
        { addOnId: 'cheese', quantity: 1 },
      ];

      act(() => {
        result.current.addItem(mockMenuItem, [], addOns);
      });

      expect(result.current.items[0].addOns).toEqual(addOns);
      expect(result.current.items[0].totalPrice).toBe(mockMenuItem.price + 2); // Extra cheese adds $2
    });

    it('should increase quantity for identical items', () => {
      const { result } = renderHook(() => useCartStore());

      act(() => {
        result.current.addItem(mockMenuItem, [], []);
        result.current.addItem(mockMenuItem, [], []);
      });

      expect(result.current.items).toHaveLength(1);
      expect(result.current.items[0].quantity).toBe(2);
      expect(result.current.items[0].totalPrice).toBe(mockMenuItem.price * 2);
    });

    it('should create separate items for different customizations', () => {
      const { result } = renderHook(() => useCartStore());
      
      const customizations1: SelectedCustomization[] = [
        { customizationId: 'size', optionIds: ['small'] },
      ];
      
      const customizations2: SelectedCustomization[] = [
        { customizationId: 'size', optionIds: ['large'] },
      ];

      act(() => {
        result.current.addItem(mockMenuItem, customizations1, []);
        result.current.addItem(mockMenuItem, customizations2, []);
      });

      expect(result.current.items).toHaveLength(2);
    });
  });

  describe('Update Item Quantity', () => {
    it('should update item quantity', () => {
      const { result } = renderHook(() => useCartStore());

      act(() => {
        result.current.setRestaurant(mockRestaurant);
        result.current.addItem(mockMenuItem, [], []);
      });

      const itemId = result.current.items[0].id;

      act(() => {
        result.current.updateItemQuantity(itemId, 3);
      });

      expect(result.current.items[0].quantity).toBe(3);
      expect(result.current.items[0].totalPrice).toBe(mockMenuItem.price * 3);
    });

    it('should remove item when quantity is 0', () => {
      const { result } = renderHook(() => useCartStore());

      act(() => {
        result.current.setRestaurant(mockRestaurant);
        result.current.addItem(mockMenuItem, [], []);
      });

      const itemId = result.current.items[0].id;

      act(() => {
        result.current.updateItemQuantity(itemId, 0);
      });

      expect(result.current.items).toHaveLength(0);
    });
  });

  describe('Remove Item', () => {
    it('should remove item from cart', () => {
      const { result } = renderHook(() => useCartStore());

      act(() => {
        result.current.setRestaurant(mockRestaurant);
        result.current.addItem(mockMenuItem, [], []);
      });

      const itemId = result.current.items[0].id;

      act(() => {
        result.current.removeItem(itemId);
      });

      expect(result.current.items).toHaveLength(0);
    });
  });

  describe('Clear Cart', () => {
    it('should clear all items and reset totals', () => {
      const { result } = renderHook(() => useCartStore());

      act(() => {
        result.current.setRestaurant(mockRestaurant);
        result.current.addItem(mockMenuItem, [], []);
        result.current.setTip(5);
      });

      act(() => {
        result.current.clearCart();
      });

      expect(result.current.items).toHaveLength(0);
      expect(result.current.restaurant).toBeNull();
      expect(result.current.subtotal).toBe(0);
      expect(result.current.total).toBe(0);
      expect(result.current.tip).toBe(0);
    });
  });

  describe('Calculate Totals', () => {
    it('should calculate correct totals', () => {
      const { result } = renderHook(() => useCartStore());

      act(() => {
        result.current.setRestaurant(mockRestaurant);
        result.current.addItem(mockMenuItem, [], []); // $15.99
        result.current.setTip(3); // $3.00
      });

      const expectedSubtotal = 15.99;
      const expectedTax = expectedSubtotal * 0.08; // 8% tax
      const expectedTotal = expectedSubtotal + mockRestaurant.deliveryFee + expectedTax + 3;

      expect(result.current.subtotal).toBeCloseTo(expectedSubtotal, 2);
      expect(result.current.tax).toBeCloseTo(expectedTax, 2);
      expect(result.current.total).toBeCloseTo(expectedTotal, 2);
    });
  });

  describe('Utility Functions', () => {
    beforeEach(() => {
      const { result } = renderHook(() => useCartStore());
      act(() => {
        result.current.setRestaurant(mockRestaurant);
        result.current.addItem(mockMenuItem, [], []);
        result.current.addItem(mockMenuItem, [], []); // Same item, quantity becomes 2
      });
    });

    it('should return correct item count', () => {
      const { result } = renderHook(() => useCartStore());
      expect(result.current.getItemCount()).toBe(2);
    });

    it('should return correct unique item count', () => {
      const { result } = renderHook(() => useCartStore());
      expect(result.current.getUniqueItemCount()).toBe(1);
    });

    it('should check if item is in cart', () => {
      const { result } = renderHook(() => useCartStore());
      expect(result.current.isItemInCart(mockMenuItem.id)).toBe(true);
      expect(result.current.isItemInCart('non-existent')).toBe(false);
    });

    it('should get cart item', () => {
      const { result } = renderHook(() => useCartStore());
      const cartItem = result.current.getCartItem(mockMenuItem.id);
      expect(cartItem).toBeDefined();
      expect(cartItem?.menuItem.id).toBe(mockMenuItem.id);
    });
  });
});

export {};
