// Modern UI Components for FoodWay App
// Optimized for performance and visual appeal

import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import {
    ActivityIndicator,
    Animated,
    Image,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import IntegratedDesignSystem from './IntegratedDesignSystem';

const { Colors, Typography, Spacing, BorderRadius, Shadows, Layout } = IntegratedDesignSystem;

// 🎨 Modern Button Component
export const ModernButton = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  icon,
  loading = false,
  disabled = false,
  style,
  textStyle,
  ...props
}) => {
  const buttonStyles = [
    styles.button,
    styles[`button${variant.charAt(0).toUpperCase() + variant.slice(1)}`],
    styles[`button${size.charAt(0).toUpperCase() + size.slice(1)}`],
    disabled && styles.buttonDisabled,
    style,
  ];

  const textStyles = [
    styles.buttonText,
    styles[`buttonText${variant.charAt(0).toUpperCase() + variant.slice(1)}`],
    disabled && styles.buttonTextDisabled,
    textStyle,
  ];

  return (
    <TouchableOpacity
      style={buttonStyles}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
      {...props}
    >
      {loading ? (
        <ActivityIndicator color={variant === 'outline' ? Colors.primary : Colors.textWhite} />
      ) : (
        <View style={styles.buttonContent}>
          {icon && (
            <Ionicons
              name={icon}
              size={20}
              color={variant === 'outline' ? Colors.primary : Colors.textWhite}
              style={styles.buttonIcon}
            />
          )}
          <Text style={textStyles}>{title}</Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

// 🍽️ Food Card Component
export const FoodCard = ({
  item,
  onPress,
  onAddToCart,
  style,
  showAddButton = true,
}) => {
  const scaleAnim = new Animated.Value(1);

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  return (
    <Animated.View style={[{ transform: [{ scale: scaleAnim }] }, style]}>
      <TouchableOpacity
        style={styles.foodCard}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
      >
        <View style={styles.foodImageContainer}>
          <Image
            source={{ uri: item.image }}
            style={styles.foodImage}
            resizeMode="cover"
          />
          {item.discount && (
            <View style={styles.discountBadge}>
              <Text style={styles.discountText}>{item.discount}% OFF</Text>
            </View>
          )}
          {item.isVeg !== undefined && (
            <View style={[styles.vegBadge, item.isVeg ? styles.vegBadgeVeg : styles.vegBadgeNonVeg]}>
              <View style={[styles.vegDot, item.isVeg ? styles.vegDotVeg : styles.vegDotNonVeg]} />
            </View>
          )}
        </View>
        
        <View style={styles.foodCardContent}>
          <Text style={styles.foodName} numberOfLines={2}>
            {item.name}
          </Text>
          <Text style={styles.foodDescription} numberOfLines={2}>
            {item.description}
          </Text>
          
          <View style={styles.foodCardFooter}>
            <View style={styles.priceContainer}>
              {item.originalPrice && (
                <Text style={styles.originalPrice}>₨{item.originalPrice}</Text>
              )}
              <Text style={styles.currentPrice}>₨{item.price}</Text>
            </View>
            
            {item.rating && (
              <View style={styles.ratingContainer}>
                <Ionicons name="star" size={14} color={Colors.rating} />
                <Text style={styles.ratingText}>{item.rating}</Text>
              </View>
            )}
          </View>
          
          {showAddButton && (
            <TouchableOpacity
              style={styles.addButton}
              onPress={() => onAddToCart(item)}
            >
              <Ionicons name="add" size={20} color={Colors.textWhite} />
            </TouchableOpacity>
          )}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

// 🏪 Restaurant Card Component
export const RestaurantCard = ({
  restaurant,
  onPress,
  style,
  horizontal = false,
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.restaurantCard,
        horizontal && styles.restaurantCardHorizontal,
        style,
      ]}
      onPress={onPress}
      activeOpacity={0.9}
    >
      <View style={styles.restaurantImageContainer}>
        <Image
          source={{ uri: restaurant.image }}
          style={[
            styles.restaurantImage,
            horizontal && styles.restaurantImageHorizontal,
          ]}
          resizeMode="cover"
        />
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.3)']}
          style={styles.restaurantImageOverlay}
        />
        
        <View style={styles.restaurantBadges}>
          {restaurant.isPromoted && (
            <View style={styles.promotedBadge}>
              <Text style={styles.promotedText}>PROMOTED</Text>
            </View>
          )}
          {restaurant.deliveryTime && (
            <View style={styles.deliveryTimeBadge}>
              <Ionicons name="time-outline" size={12} color={Colors.textWhite} />
              <Text style={styles.deliveryTimeText}>{restaurant.deliveryTime}</Text>
            </View>
          )}
        </View>
      </View>
      
      <View style={styles.restaurantCardContent}>
        <Text style={styles.restaurantName} numberOfLines={1}>
          {restaurant.name}
        </Text>
        <Text style={styles.restaurantCuisine} numberOfLines={1}>
          {restaurant.cuisine}
        </Text>
        
        <View style={styles.restaurantInfo}>
          <View style={styles.restaurantRating}>
            <Ionicons name="star" size={14} color={Colors.rating} />
            <Text style={styles.restaurantRatingText}>{restaurant.rating}</Text>
            <Text style={styles.restaurantReviews}>({restaurant.reviews})</Text>
          </View>
          
          <View style={styles.restaurantDelivery}>
            <Text style={styles.deliveryFee}>
              {restaurant.deliveryFee === 0 ? 'Free Delivery' : `₨${restaurant.deliveryFee}`}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

// 🏷️ Category Chip Component
export const CategoryChip = ({
  category,
  isSelected = false,
  onPress,
  style,
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.categoryChip,
        isSelected && styles.categoryChipSelected,
        style,
      ]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      {category.icon && (
        <Image
          source={{ uri: category.icon }}
          style={styles.categoryIcon}
          resizeMode="contain"
        />
      )}
      <Text
        style={[
          styles.categoryText,
          isSelected && styles.categoryTextSelected,
        ]}
      >
        {category.name}
      </Text>
    </TouchableOpacity>
  );
};

// 🛒 Cart Item Component
export const CartItem = ({
  item,
  onIncrease,
  onDecrease,
  onRemove,
  style,
}) => {
  return (
    <View style={[styles.cartItem, style]}>
      <Image
        source={{ uri: item.image }}
        style={styles.cartItemImage}
        resizeMode="cover"
      />
      
      <View style={styles.cartItemContent}>
        <Text style={styles.cartItemName} numberOfLines={2}>
          {item.name}
        </Text>
        <Text style={styles.cartItemPrice}>₨{item.price}</Text>
        
        <View style={styles.cartItemActions}>
          <View style={styles.quantityContainer}>
            <TouchableOpacity
              style={styles.quantityButton}
              onPress={() => onDecrease(item)}
            >
              <Ionicons name="remove" size={16} color={Colors.primary} />
            </TouchableOpacity>
            
            <Text style={styles.quantityText}>{item.quantity}</Text>
            
            <TouchableOpacity
              style={styles.quantityButton}
              onPress={() => onIncrease(item)}
            >
              <Ionicons name="add" size={16} color={Colors.primary} />
            </TouchableOpacity>
          </View>
          
          <TouchableOpacity
            style={styles.removeButton}
            onPress={() => onRemove(item)}
          >
            <Ionicons name="trash-outline" size={16} color={Colors.error} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

// 📍 Address Card Component
export const AddressCard = ({
  address,
  isSelected = false,
  onPress,
  onEdit,
  style,
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.addressCard,
        isSelected && styles.addressCardSelected,
        style,
      ]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={styles.addressHeader}>
        <View style={styles.addressTypeContainer}>
          <Ionicons
            name={address.type === 'home' ? 'home' : address.type === 'work' ? 'business' : 'location'}
            size={20}
            color={isSelected ? Colors.primary : Colors.textSecondary}
          />
          <Text style={[
            styles.addressType,
            isSelected && styles.addressTypeSelected,
          ]}>
            {address.type.toUpperCase()}
          </Text>
        </View>
        
        <TouchableOpacity onPress={() => onEdit(address)}>
          <Ionicons name="pencil" size={16} color={Colors.textSecondary} />
        </TouchableOpacity>
      </View>
      
      <Text style={styles.addressText} numberOfLines={3}>
        {address.fullAddress}
      </Text>
      
      {isSelected && (
        <View style={styles.selectedIndicator}>
          <Ionicons name="checkmark-circle" size={20} color={Colors.primary} />
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  // Button Styles
  button: {
    height: Layout.buttonHeight,
    borderRadius: BorderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    ...Shadows.sm,
  },
  buttonPrimary: {
    backgroundColor: Colors.primary,
  },
  buttonSecondary: {
    backgroundColor: Colors.secondary,
  },
  buttonOutline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  buttonSmall: {
    height: 36,
    paddingHorizontal: Spacing.md,
  },
  buttonLarge: {
    height: 56,
    paddingHorizontal: Spacing.xl,
  },
  buttonDisabled: {
    backgroundColor: Colors.textLight,
    ...Shadows.none,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonIcon: {
    marginRight: Spacing.sm,
  },
  buttonText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.textWhite,
  },
  buttonTextOutline: {
    color: Colors.primary,
  },
  buttonTextDisabled: {
    color: Colors.textSecondary,
  },
  
  // Food Card Styles
  foodCard: {
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.lg,
    overflow: 'hidden',
    ...Shadows.md,
    marginBottom: Spacing.md,
  },
  foodImageContainer: {
    position: 'relative',
    height: 160,
  },
  foodImage: {
    width: '100%',
    height: '100%',
  },
  discountBadge: {
    position: 'absolute',
    top: Spacing.sm,
    left: Spacing.sm,
    backgroundColor: Colors.error,
    paddingHorizontal: Spacing.sm,
    paddingVertical: 4,
    borderRadius: BorderRadius.sm,
  },
  discountText: {
    color: Colors.textWhite,
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.bold,
  },
  vegBadge: {
    position: 'absolute',
    top: Spacing.sm,
    right: Spacing.sm,
    width: 20,
    height: 20,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
  },
  vegBadgeVeg: {
    backgroundColor: Colors.background,
    borderColor: Colors.secondary,
  },
  vegBadgeNonVeg: {
    backgroundColor: Colors.background,
    borderColor: Colors.error,
  },
  vegDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  vegDotVeg: {
    backgroundColor: Colors.secondary,
  },
  vegDotNonVeg: {
    backgroundColor: Colors.error,
  },
  foodCardContent: {
    padding: Spacing.base,
    position: 'relative',
  },
  foodName: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  foodDescription: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.sm,
    lineHeight: Typography.lineHeight.normal * Typography.fontSize.sm,
  },
  foodCardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  originalPrice: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textLight,
    textDecorationLine: 'line-through',
    marginRight: Spacing.sm,
  },
  currentPrice: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
    marginLeft: 4,
  },
  addButton: {
    position: 'absolute',
    bottom: Spacing.base,
    right: Spacing.base,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    ...Shadows.md,
  },

  // Restaurant Card Styles
  restaurantCard: {
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.lg,
    overflow: 'hidden',
    ...Shadows.md,
    marginBottom: Spacing.md,
  },
  restaurantCardHorizontal: {
    flexDirection: 'row',
    height: 120,
  },
  restaurantImageContainer: {
    position: 'relative',
    height: 180,
  },
  restaurantImage: {
    width: '100%',
    height: '100%',
  },
  restaurantImageHorizontal: {
    width: 120,
    height: '100%',
  },
  restaurantImageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
  },
  restaurantBadges: {
    position: 'absolute',
    top: Spacing.sm,
    left: Spacing.sm,
    right: Spacing.sm,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  promotedBadge: {
    backgroundColor: Colors.accent,
    paddingHorizontal: Spacing.sm,
    paddingVertical: 4,
    borderRadius: BorderRadius.sm,
  },
  promotedText: {
    color: Colors.textWhite,
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.bold,
  },
  deliveryTimeBadge: {
    backgroundColor: 'rgba(0,0,0,0.6)',
    paddingHorizontal: Spacing.sm,
    paddingVertical: 4,
    borderRadius: BorderRadius.sm,
    flexDirection: 'row',
    alignItems: 'center',
  },
  deliveryTimeText: {
    color: Colors.textWhite,
    fontSize: Typography.fontSize.xs,
    marginLeft: 4,
  },
  restaurantCardContent: {
    padding: Spacing.base,
    flex: 1,
  },
  restaurantName: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  restaurantCuisine: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.sm,
  },
  restaurantInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  restaurantRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  restaurantRatingText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textPrimary,
    marginLeft: 4,
    fontWeight: Typography.fontWeight.medium,
  },
  restaurantReviews: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
    marginLeft: 4,
  },
  restaurantDelivery: {
    alignItems: 'flex-end',
  },
  deliveryFee: {
    fontSize: Typography.fontSize.sm,
    color: Colors.secondary,
    fontWeight: Typography.fontWeight.medium,
  },

  // Category Chip Styles
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.base,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.border,
    marginRight: Spacing.sm,
  },
  categoryChipSelected: {
    backgroundColor: Colors.primaryAlpha,
    borderColor: Colors.primary,
  },
  categoryIcon: {
    width: 20,
    height: 20,
    marginRight: Spacing.sm,
  },
  categoryText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
    fontWeight: Typography.fontWeight.medium,
  },
  categoryTextSelected: {
    color: Colors.primary,
    fontWeight: Typography.fontWeight.semibold,
  },

  // Cart Item Styles
  cartItem: {
    flexDirection: 'row',
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.md,
    padding: Spacing.base,
    marginBottom: Spacing.sm,
    ...Shadows.sm,
  },
  cartItemImage: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.sm,
  },
  cartItemContent: {
    flex: 1,
    marginLeft: Spacing.base,
  },
  cartItemName: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  cartItemPrice: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.sm,
  },
  cartItemActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.md,
    paddingHorizontal: 4,
  },
  quantityButton: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.textPrimary,
    marginHorizontal: Spacing.sm,
    minWidth: 20,
    textAlign: 'center',
  },
  removeButton: {
    padding: Spacing.sm,
  },

  // Address Card Styles
  addressCard: {
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.md,
    padding: Spacing.base,
    marginBottom: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.border,
    position: 'relative',
  },
  addressCardSelected: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primaryAlpha,
  },
  addressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  addressTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addressType: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.textSecondary,
    marginLeft: Spacing.sm,
  },
  addressTypeSelected: {
    color: Colors.primary,
  },
  addressText: {
    fontSize: Typography.fontSize.base,
    color: Colors.textPrimary,
    lineHeight: Typography.lineHeight.relaxed * Typography.fontSize.base,
  },
  selectedIndicator: {
    position: 'absolute',
    top: Spacing.sm,
    right: Spacing.sm,
  },
});
