// Integration Utilities for FoodWay App
// Helper functions to seamlessly integrate modern design with existing components

import { StyleSheet } from 'react-native';
import { Colors, Typography, Spacing, BorderRadius, Shadows, Utils } from './IntegratedDesignSystem';

// 🔄 STYLE MIGRATION HELPERS
// Functions to convert existing styles to modern design system

/**
 * Migrates existing color references to modern design system
 * @param {Object} styles - Existing styles object
 * @returns {Object} - Updated styles with modern colors
 */
export const migrateColors = (styles) => {
  const colorMappings = {
    '#FF6B35': Colors.primary,
    '#E55A2B': Colors.primaryDark,
    '#FF8A65': Colors.primaryLight,
    '#2E7D32': Colors.secondary,
    '#FFFFFF': Colors.white,
    '#212121': Colors.textPrimary,
    '#757575': Colors.textSecondary,
    '#FAFAFA': Colors.background,
    '#F5F5F5': Colors.surface,
  };

  const migratedStyles = { ...styles };
  
  Object.keys(migratedStyles).forEach(key => {
    const style = migratedStyles[key];
    if (typeof style === 'object') {
      Object.keys(style).forEach(prop => {
        if (colorMappings[style[prop]]) {
          style[prop] = colorMappings[style[prop]];
        }
      });
    }
  });

  return migratedStyles;
};

/**
 * Adds modern shadows to existing card components
 * @param {Object} cardStyle - Existing card style
 * @param {number} elevation - Shadow elevation level (1-6)
 * @returns {Object} - Card style with modern shadow
 */
export const addModernShadow = (cardStyle, elevation = 2) => {
  return {
    ...cardStyle,
    ...Utils.getShadow(elevation),
  };
};

/**
 * Converts existing spacing values to modern design system
 * @param {Object} styles - Existing styles object
 * @returns {Object} - Updated styles with modern spacing
 */
export const migrateSpacing = (styles) => {
  const spacingMappings = {
    4: Spacing.xs,
    8: Spacing.sm,
    16: Spacing.md,
    24: Spacing.lg,
    32: Spacing.xl,
    48: Spacing['2xl'],
    64: Spacing['3xl'],
  };

  const migratedStyles = { ...styles };
  
  Object.keys(migratedStyles).forEach(key => {
    const style = migratedStyles[key];
    if (typeof style === 'object') {
      ['margin', 'marginTop', 'marginBottom', 'marginLeft', 'marginRight',
       'marginHorizontal', 'marginVertical', 'padding', 'paddingTop',
       'paddingBottom', 'paddingLeft', 'paddingRight', 'paddingHorizontal',
       'paddingVertical'].forEach(prop => {
        if (spacingMappings[style[prop]]) {
          style[prop] = spacingMappings[style[prop]];
        }
      });
    }
  });

  return migratedStyles;
};

// 🎨 MODERN COMPONENT STYLES
// Pre-built modern styles for common components

export const ModernStyles = {
  // Modern button styles
  button: {
    primary: {
      backgroundColor: Colors.primary,
      paddingHorizontal: Spacing.lg,
      paddingVertical: Spacing.md,
      borderRadius: BorderRadius.lg,
      alignItems: 'center',
      justifyContent: 'center',
      ...Shadows.sm,
    },
    secondary: {
      backgroundColor: Colors.secondary,
      paddingHorizontal: Spacing.lg,
      paddingVertical: Spacing.md,
      borderRadius: BorderRadius.lg,
      alignItems: 'center',
      justifyContent: 'center',
      ...Shadows.sm,
    },
    outline: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: Colors.primary,
      paddingHorizontal: Spacing.lg,
      paddingVertical: Spacing.md,
      borderRadius: BorderRadius.lg,
      alignItems: 'center',
      justifyContent: 'center',
    },
  },

  // Modern card styles
  card: {
    default: {
      backgroundColor: Colors.white,
      borderRadius: BorderRadius.lg,
      padding: Spacing.md,
      ...Shadows.sm,
    },
    elevated: {
      backgroundColor: Colors.white,
      borderRadius: BorderRadius.lg,
      padding: Spacing.md,
      ...Shadows.md,
    },
    restaurant: {
      backgroundColor: Colors.white,
      borderRadius: BorderRadius.lg,
      overflow: 'hidden',
      ...Shadows.sm,
    },
  },

  // Modern text styles
  text: {
    heading1: {
      fontSize: Typography.fontSize['3xl'],
      fontWeight: Typography.fontWeight.bold,
      color: Colors.textPrimary,
      lineHeight: Typography.lineHeight.tight * Typography.fontSize['3xl'],
    },
    heading2: {
      fontSize: Typography.fontSize['2xl'],
      fontWeight: Typography.fontWeight.bold,
      color: Colors.textPrimary,
      lineHeight: Typography.lineHeight.tight * Typography.fontSize['2xl'],
    },
    heading3: {
      fontSize: Typography.fontSize.xl,
      fontWeight: Typography.fontWeight.semibold,
      color: Colors.textPrimary,
      lineHeight: Typography.lineHeight.normal * Typography.fontSize.xl,
    },
    body: {
      fontSize: Typography.fontSize.md,
      fontWeight: Typography.fontWeight.regular,
      color: Colors.textPrimary,
      lineHeight: Typography.lineHeight.normal * Typography.fontSize.md,
    },
    caption: {
      fontSize: Typography.fontSize.sm,
      fontWeight: Typography.fontWeight.regular,
      color: Colors.textSecondary,
      lineHeight: Typography.lineHeight.normal * Typography.fontSize.sm,
    },
  },

  // Modern input styles
  input: {
    default: {
      backgroundColor: Colors.surface,
      borderWidth: 1,
      borderColor: Colors.border,
      borderRadius: BorderRadius.md,
      paddingHorizontal: Spacing.md,
      paddingVertical: Spacing.sm,
      fontSize: Typography.fontSize.md,
      color: Colors.textPrimary,
    },
    focused: {
      borderColor: Colors.primary,
      borderWidth: 2,
    },
    error: {
      borderColor: Colors.error,
    },
  },

  // Modern header styles
  header: {
    default: {
      backgroundColor: Colors.white,
      paddingHorizontal: Spacing.md,
      paddingVertical: Spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: Colors.border,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    gradient: {
      paddingHorizontal: Spacing.md,
      paddingVertical: Spacing.lg,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
  },

  // Modern list styles
  list: {
    container: {
      backgroundColor: Colors.background,
    },
    item: {
      backgroundColor: Colors.white,
      marginHorizontal: Spacing.md,
      marginVertical: Spacing.xs,
      borderRadius: BorderRadius.md,
      padding: Spacing.md,
      ...Shadows.sm,
    },
  },
};

// 🔧 COMPONENT ENHANCEMENT FUNCTIONS
// Functions to enhance existing components with modern features

/**
 * Adds modern ripple effect to touchable components
 * @param {Object} baseStyle - Base component style
 * @returns {Object} - Enhanced style with ripple effect
 */
export const addRippleEffect = (baseStyle) => {
  return {
    ...baseStyle,
    overflow: 'hidden',
    // Android ripple effect will be handled in component
  };
};

/**
 * Creates modern gradient background style
 * @param {Array} colors - Gradient colors array
 * @param {Object} options - Gradient options
 * @returns {Object} - Gradient style configuration
 */
export const createGradientStyle = (colors = [Colors.gradientStart, Colors.gradientEnd], options = {}) => {
  return {
    colors,
    start: options.start || { x: 0, y: 0 },
    end: options.end || { x: 1, y: 0 },
    locations: options.locations || [0, 1],
  };
};

/**
 * Creates modern loading shimmer effect style
 * @param {number} width - Shimmer width
 * @param {number} height - Shimmer height
 * @returns {Object} - Shimmer style configuration
 */
export const createShimmerStyle = (width, height) => {
  return {
    width,
    height,
    backgroundColor: Colors.gray[200],
    borderRadius: BorderRadius.sm,
  };
};

// 📱 RESPONSIVE HELPERS
// Functions to create responsive styles

/**
 * Creates responsive padding based on screen size
 * @param {number} base - Base padding value
 * @returns {number} - Responsive padding value
 */
export const responsivePadding = (base) => {
  return Utils.responsive(base * 0.8, base, base * 1.2);
};

/**
 * Creates responsive font size based on screen size
 * @param {number} base - Base font size
 * @returns {number} - Responsive font size
 */
export const responsiveFontSize = (base) => {
  return Utils.responsive(base * 0.9, base, base * 1.1);
};

/**
 * Creates responsive margin based on screen size
 * @param {number} base - Base margin value
 * @returns {number} - Responsive margin value
 */
export const responsiveMargin = (base) => {
  return Utils.responsive(base * 0.8, base, base * 1.2);
};

// 🎯 THEME UTILITIES
// Functions to work with theme and styling

/**
 * Merges multiple style objects with proper precedence
 * @param {...Object} styles - Style objects to merge
 * @returns {Object} - Merged style object
 */
export const mergeStyles = (...styles) => {
  return StyleSheet.flatten(styles);
};

/**
 * Creates conditional styles based on state
 * @param {Object} baseStyle - Base style object
 * @param {Object} conditions - Conditions and their styles
 * @param {Object} state - Current state object
 * @returns {Object} - Conditional style object
 */
export const conditionalStyle = (baseStyle, conditions, state) => {
  let appliedStyles = [baseStyle];
  
  Object.keys(conditions).forEach(condition => {
    if (state[condition]) {
      appliedStyles.push(conditions[condition]);
    }
  });
  
  return StyleSheet.flatten(appliedStyles);
};

// Export all utilities
export default {
  migrateColors,
  addModernShadow,
  migrateSpacing,
  ModernStyles,
  addRippleEffect,
  createGradientStyle,
  createShimmerStyle,
  responsivePadding,
  responsiveFontSize,
  responsiveMargin,
  mergeStyles,
  conditionalStyle,
};
