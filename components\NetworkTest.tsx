import React, { useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { axiosInstance } from '../services/config/axios';

export const NetworkTest = () => {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<string[]>([]);

  const addResult = (message: string) => {
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testNetwork = async () => {
    setTesting(true);
    setResults([]);

    addResult('Starting network tests...');

    // Test 1: Basic connectivity
    try {
      addResult('Testing basic connectivity...');
      const response = await fetch('https://httpbin.org/get', {
        method: 'GET',
        headers: { 'Accept': 'application/json' }
      });
      addResult(`✅ Basic connectivity: ${response.status}`);
    } catch (error) {
      addResult(`❌ Basic connectivity failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Test 2: Backend health check
    try {
      addResult('Testing backend health...');
      const response = await fetch('https://backend-production-f106.up.railway.app/health', {
        method: 'GET',
        headers: { 'Accept': 'application/json' }
      });
      const data = await response.json();
      addResult(`✅ Backend health: ${response.status} - ${JSON.stringify(data)}`);
    } catch (error) {
      addResult(`❌ Backend health failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Test 3: Backend API endpoint
    try {
      addResult('Testing backend API...');
      const response = await axiosInstance.get('/restaurants', {
        headers: { skipAuth: true } as any
      });
      addResult(`✅ Backend API: 200 - Found ${response.data.data?.restaurants?.length || 0} restaurants`);
    } catch (error) {
      addResult(`❌ Backend API failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Test 4: Registration endpoint (without actual registration)
    try {
      addResult('Testing registration endpoint...');
      const response = await axiosInstance.post('/auth/register', {
        email: '<EMAIL>',
        password: 'test123',
        firstName: 'Test',
        lastName: 'User',
        phone: '**********'
      }, {
        headers: { skipAuth: true } as any
      });
      addResult(`✅ Registration endpoint: 200 - ${response.data.message || response.data.error?.message || 'Response received'}`);
    } catch (error) {
      addResult(`❌ Registration endpoint failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    setTesting(false);
    addResult('Network tests completed!');
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[styles.button, testing && styles.buttonDisabled]}
        onPress={testNetwork}
        disabled={testing}
      >
        <Text style={styles.buttonText}>
          {testing ? 'Testing...' : 'Test Network Connectivity'}
        </Text>
      </TouchableOpacity>

      <View style={styles.results}>
        {results.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f5f5f5',
    margin: 10,
    borderRadius: 10,
  },
  button: {
    backgroundColor: '#FF6B35',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  results: {
    maxHeight: 300,
  },
  resultText: {
    fontSize: 12,
    marginBottom: 5,
    fontFamily: 'monospace',
  },
});
