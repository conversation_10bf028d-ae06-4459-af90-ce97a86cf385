import { useEffect, useRef, useCallback } from 'react';
import { AppState, AppStateStatus } from 'react-native';

interface CleanupFunction {
  (): void;
}

interface MemoryManagerConfig {
  maxCacheSize: number;
  cleanupInterval: number;
  enableAutoCleanup: boolean;
}

class MemoryManager {
  private cleanupFunctions: Set<CleanupFunction> = new Set();
  private timers: Set<NodeJS.Timeout> = new Set();
  private intervals: Set<NodeJS.Timeout> = new Set();
  private caches: Map<string, Map<string, any>> = new Map();
  private config: MemoryManagerConfig;

  constructor(config: Partial<MemoryManagerConfig> = {}) {
    this.config = {
      maxCacheSize: 100,
      cleanupInterval: 5 * 60 * 1000, // 5 minutes
      enableAutoCleanup: true,
      ...config,
    };

    this.setupAppStateListener();
    this.setupAutoCleanup();
  }

  /**
   * Register a cleanup function
   */
  addCleanup(cleanup: CleanupFunction): void {
    this.cleanupFunctions.add(cleanup);
  }

  /**
   * Remove a cleanup function
   */
  removeCleanup(cleanup: CleanupFunction): void {
    this.cleanupFunctions.delete(cleanup);
  }

  /**
   * Execute all cleanup functions
   */
  cleanup(): void {
    console.log(`[MemoryManager] Running cleanup for ${this.cleanupFunctions.size} functions`);
    
    this.cleanupFunctions.forEach(cleanup => {
      try {
        cleanup();
      } catch (error) {
        console.warn('[MemoryManager] Cleanup function failed:', error);
      }
    });

    this.clearTimers();
    this.clearIntervals();
    this.clearCaches();
  }

  /**
   * Register a timer for automatic cleanup
   */
  addTimer(timer: NodeJS.Timeout): void {
    this.timers.add(timer);
  }

  /**
   * Register an interval for automatic cleanup
   */
  addInterval(interval: NodeJS.Timeout): void {
    this.intervals.add(interval);
  }

  /**
   * Clear all timers
   */
  private clearTimers(): void {
    this.timers.forEach(timer => clearTimeout(timer));
    this.timers.clear();
  }

  /**
   * Clear all intervals
   */
  private clearIntervals(): void {
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals.clear();
  }

  /**
   * Create a managed cache
   */
  createCache<T>(name: string): Map<string, T> {
    if (!this.caches.has(name)) {
      this.caches.set(name, new Map());
    }
    return this.caches.get(name)!;
  }

  /**
   * Clear all caches
   */
  private clearCaches(): void {
    this.caches.forEach(cache => cache.clear());
  }

  /**
   * Clear specific cache
   */
  clearCache(name: string): void {
    const cache = this.caches.get(name);
    if (cache) {
      cache.clear();
    }
  }

  /**
   * Get memory usage statistics
   */
  getMemoryStats(): {
    cleanupFunctions: number;
    timers: number;
    intervals: number;
    caches: Record<string, number>;
  } {
    const cacheStats: Record<string, number> = {};
    this.caches.forEach((cache, name) => {
      cacheStats[name] = cache.size;
    });

    return {
      cleanupFunctions: this.cleanupFunctions.size,
      timers: this.timers.size,
      intervals: this.intervals.size,
      caches: cacheStats,
    };
  }

  /**
   * Setup app state listener for cleanup
   */
  private setupAppStateListener(): void {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'background') {
        console.log('[MemoryManager] App backgrounded, running cleanup');
        this.cleanup();
      }
    };

    AppState.addEventListener('change', handleAppStateChange);
    
    this.addCleanup(() => {
      AppState.removeEventListener('change', handleAppStateChange);
    });
  }

  /**
   * Setup automatic cleanup interval
   */
  private setupAutoCleanup(): void {
    if (!this.config.enableAutoCleanup) return;

    const cleanupInterval = setInterval(() => {
      this.performMaintenanceCleanup();
    }, this.config.cleanupInterval);

    this.addInterval(cleanupInterval);
  }

  /**
   * Perform maintenance cleanup
   */
  private performMaintenanceCleanup(): void {
    console.log('[MemoryManager] Performing maintenance cleanup');
    
    // Clean up oversized caches
    this.caches.forEach((cache, name) => {
      if (cache.size > this.config.maxCacheSize) {
        console.log(`[MemoryManager] Cache '${name}' exceeded max size, clearing`);
        cache.clear();
      }
    });
  }
}

// Create singleton instance
export const memoryManager = new MemoryManager();

/**
 * React hook for automatic cleanup management
 */
export const useCleanup = () => {
  const cleanupFunctions = useRef<CleanupFunction[]>([]);

  const addCleanup = useCallback((cleanup: CleanupFunction) => {
    cleanupFunctions.current.push(cleanup);
    memoryManager.addCleanup(cleanup);
  }, []);

  const addTimer = useCallback((timer: NodeJS.Timeout) => {
    memoryManager.addTimer(timer);
    addCleanup(() => clearTimeout(timer));
  }, [addCleanup]);

  const addInterval = useCallback((interval: NodeJS.Timeout) => {
    memoryManager.addInterval(interval);
    addCleanup(() => clearInterval(interval));
  }, [addCleanup]);

  useEffect(() => {
    return () => {
      cleanupFunctions.current.forEach(cleanup => {
        memoryManager.removeCleanup(cleanup);
      });
      cleanupFunctions.current = [];
    };
  }, []);

  return {
    addCleanup,
    addTimer,
    addInterval,
  };
};

/**
 * React hook for managed cache
 */
export const useManagedCache = <T>(cacheName: string) => {
  const cache = useRef<Map<string, T>>();

  if (!cache.current) {
    cache.current = memoryManager.createCache<T>(cacheName);
  }

  const { addCleanup } = useCleanup();

  useEffect(() => {
    addCleanup(() => {
      cache.current?.clear();
    });
  }, [addCleanup]);

  const set = useCallback((key: string, value: T) => {
    cache.current?.set(key, value);
  }, []);

  const get = useCallback((key: string): T | undefined => {
    return cache.current?.get(key);
  }, []);

  const remove = useCallback((key: string): boolean => {
    return cache.current?.delete(key) || false;
  }, []);

  const clear = useCallback(() => {
    cache.current?.clear();
  }, []);

  const has = useCallback((key: string): boolean => {
    return cache.current?.has(key) || false;
  }, []);

  return {
    set,
    get,
    remove,
    clear,
    has,
    size: cache.current?.size || 0,
  };
};

/**
 * React hook for memory-efficient timers
 */
export const useManagedTimer = () => {
  const { addTimer, addInterval } = useCleanup();

  const setTimeout = useCallback((callback: () => void, delay: number) => {
    const timer = global.setTimeout(callback, delay);
    addTimer(timer);
    return timer;
  }, [addTimer]);

  const setInterval = useCallback((callback: () => void, delay: number) => {
    const interval = global.setInterval(callback, delay);
    addInterval(interval);
    return interval;
  }, [addInterval]);

  return {
    setTimeout,
    setInterval,
  };
};

export default memoryManager;
