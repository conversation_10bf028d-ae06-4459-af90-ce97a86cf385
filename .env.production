# PRODUCTION ENVIRONMENT - HTTPS ONLY
# This file contains production-ready configuration with HTTPS-only URLs

# APP CONFIGURATION
NODE_ENV=production
EXPO_PUBLIC_APP_NAME=FoodWay
EXPO_PUBLIC_APP_VERSION=1.0.0

# API CONFIGURATION - HTTPS ONLY
EXPO_PUBLIC_API_URL=https://backend-production-f106.up.railway.app/api/v1

# PAYMENT (Production keys - replace with actual values)
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_production_stripe_key_here

# MAPS (Production key - replace with actual value)
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=your_production_google_maps_api_key_here

# PRODUCTION FLAGS
DEBUG=false
EXPO_PUBLIC_DEV_MODE=false
EXPO_PUBLIC_USE_MOCK_DATA=false

# FEATURE FLAGS (Production settings)
EXPO_PUBLIC_ENABLE_SOCIAL_LOGIN=true
EXPO_PUBLIC_ENABLE_APPLE_PAY=true
EXPO_PUBLIC_ENABLE_GOOGLE_PAY=true
EXPO_PUBLIC_ENABLE_CHAT=true
EXPO_PUBLIC_ENABLE_REVIEWS=true
EXPO_PUBLIC_ENABLE_PROMOTIONS=true
EXPO_PUBLIC_ENABLE_LOYALTY_PROGRAM=true
EXPO_PUBLIC_ENABLE_PUSH_NOTIFICATIONS=true
EXPO_PUBLIC_ENABLE_LOCATION_TRACKING=true

# SECURITY SETTINGS
EXPO_PUBLIC_ENFORCE_HTTPS=true
EXPO_PUBLIC_CERTIFICATE_PINNING=true

# ANALYTICS & MONITORING (Production)
EXPO_PUBLIC_ANALYTICS_ENABLED=true
EXPO_PUBLIC_CRASH_REPORTING=true
EXPO_PUBLIC_PERFORMANCE_MONITORING=true
