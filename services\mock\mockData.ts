import { User, Restaurant, Category, MenuItem, Order, Address, PaymentMethod, OrderStatus } from '../../types';

// Mock Users
export const mockUsers: User[] = [
  {
    id: 'user1',
    email: '<EMAIL>',
    firstName: '<PERSON>',
    lastName: 'Doe',
    phone: '******-0123',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    dateOfBirth: '1990-05-15',
    isEmailVerified: true,
    isPhoneVerified: true,
    preferences: {
      notifications: {
        orderUpdates: true,
        promotions: true,
        newRestaurants: false,
      },
      dietary: ['vegetarian'],
      cuisines: ['Italian', 'Mexican', 'Asian'],
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T12:00:00Z',
  },
  {
    id: 'user2',
    email: '<EMAIL>',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    phone: '******-0124',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    dateOfBirth: '1985-08-22',
    isEmailVerified: true,
    isPhoneVerified: true,
    preferences: {
      notifications: {
        orderUpdates: true,
        promotions: false,
        newRestaurants: true,
      },
      dietary: ['gluten-free'],
      cuisines: ['American', 'Mediterranean'],
    },
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-16T10:30:00Z',
  },
];

// Mock Categories
export const mockCategories: Category[] = [
  { id: '1', name: 'Pizza', image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=100&h=100&fit=crop' },
  { id: '2', name: 'Burgers', image: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=100&h=100&fit=crop' },
  { id: '3', name: 'Sushi', image: 'https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=100&h=100&fit=crop' },
  { id: '4', name: 'Mexican', image: 'https://images.unsplash.com/photo-1565299507177-b0ac66763828?w=100&h=100&fit=crop' },
  { id: '5', name: 'Chinese', image: 'https://images.unsplash.com/photo-1526318896980-cf78c088247c?w=100&h=100&fit=crop' },
  { id: '6', name: 'Indian', image: 'https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=100&h=100&fit=crop' },
  { id: '7', name: 'Thai', image: 'https://images.unsplash.com/photo-1559847844-d7b4ac2b7b8e?w=100&h=100&fit=crop' },
  { id: '8', name: 'Italian', image: 'https://images.unsplash.com/photo-1551183053-bf91a1d81141?w=100&h=100&fit=crop' },
  { id: '9', name: 'Mediterranean', image: 'https://images.unsplash.com/photo-1544510808-5c4ac4d5c6e8?w=100&h=100&fit=crop' },
  { id: '10', name: 'American', image: 'https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=100&h=100&fit=crop' },
];

// Mock Addresses
export const mockAddresses: Address[] = [
  {
    id: 'addr1',
    userId: 'user1',
    type: 'home',
    label: 'Home',
    street: '123 Main St',
    city: 'San Francisco',
    state: 'CA',
    zipCode: '94102',
    country: 'US',
    latitude: 37.7749,
    longitude: -122.4194,
    isDefault: true,
    instructions: 'Ring doorbell twice',
  },
  {
    id: 'addr2',
    userId: 'user1',
    type: 'work',
    label: 'Office',
    street: '456 Business Ave',
    city: 'San Francisco',
    state: 'CA',
    zipCode: '94105',
    country: 'US',
    latitude: 37.7849,
    longitude: -122.4094,
    isDefault: false,
    instructions: 'Leave at reception',
  },
  {
    id: 'addr3',
    userId: 'user1',
    type: 'other',
    label: 'Mom\'s House',
    street: '789 Family Lane',
    city: 'San Francisco',
    state: 'CA',
    zipCode: '94110',
    country: 'US',
    latitude: 37.7649,
    longitude: -122.4294,
    isDefault: false,
    instructions: 'Use side entrance',
  },
];

// Mock Payment Methods
export const mockPaymentMethods: PaymentMethod[] = [
  {
    id: 'pm1',
    type: 'card',
    last4: '4242',
    brand: 'visa',
    expiryMonth: 12,
    expiryYear: 2025,
    isDefault: true,
  },
  {
    id: 'pm2',
    type: 'card',
    last4: '5555',
    brand: 'mastercard',
    expiryMonth: 8,
    expiryYear: 2026,
    isDefault: false,
  },
  {
    id: 'pm3',
    type: 'card',
    last4: '1234',
    brand: 'amex',
    expiryMonth: 3,
    expiryYear: 2027,
    isDefault: false,
  },
];

// Default mock user for authentication
export const defaultMockUser = mockUsers[0];

// Mock authentication credentials
export const mockCredentials = [
  { email: '<EMAIL>', password: 'password123', user: mockUsers[0] },
  { email: '<EMAIL>', password: 'password123', user: mockUsers[1] },
  { email: '<EMAIL>', password: 'TestPassword123!', user: mockUsers[0] },
  { email: '<EMAIL>', password: 'demo123', user: mockUsers[0] },
];

// Mock tokens
export const mockTokens = {
  accessToken: 'mock_access_token_12345',
  refreshToken: 'mock_refresh_token_67890',
};

// Utility functions
export const findUserByEmail = (email: string): User | undefined => {
  return mockUsers.find(user => user.email.toLowerCase() === email.toLowerCase());
};

export const validateMockCredentials = (email: string, password: string): User | null => {
  const credential = mockCredentials.find(
    cred => cred.email.toLowerCase() === email.toLowerCase() && cred.password === password
  );
  return credential ? credential.user : null;
};

export const getUserAddresses = (userId: string): Address[] => {
  return mockAddresses.filter(address => address.userId === userId);
};

export const getUserPaymentMethods = (userId: string): PaymentMethod[] => {
  return mockPaymentMethods.filter(method => method.id); // All payment methods for demo
};

// Generate random delay for realistic API simulation
export const mockDelay = (min: number = 500, max: number = 1500): Promise<void> => {
  const delay = Math.random() * (max - min) + min;
  return new Promise(resolve => setTimeout(resolve, delay));
};
