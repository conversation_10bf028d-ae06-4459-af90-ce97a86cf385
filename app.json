{"expo": {"name": "FoodWay Customer", "slug": "foodway-customer", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "foodway", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.foodway.customer", "infoPlist": {"NSLocationWhenInUseUsageDescription": "This app needs access to your location to show nearby restaurants and provide delivery services.", "NSCameraUsageDescription": "This app needs access to your camera to take photos for reviews and profile pictures.", "NSPhotoLibraryUsageDescription": "This app needs access to your photo library to select images for reviews and profile pictures."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#FF6B35"}, "package": "com.foodway.customer", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "INTERNET"], "usesCleartextTraffic": false, "networkSecurityConfig": "./android/app/src/main/res/xml/network_security_config.xml"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#FF6B35"}]], "experiments": {"typedRoutes": true}, "extra": {"eas": {"projectId": "your-eas-project-id"}}}}