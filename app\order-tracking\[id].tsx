import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useLocalSearchParams } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
    Alert,
    Animated,
    Image,
    Linking,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import { Order, OrderStatus, TrackingEvent } from '../../types';
import { BORDER_RADIUS, COLORS, SPACING, TYPOGRAPHY } from '../../utils/constants';

// Mock order data
const mockOrder: Order = {
  id: 'order-123',
  userId: 'user1',
  restaurantId: 'rest1',
  restaurant: {
    id: 'rest1',
    name: '<PERSON>\'s Pizza Palace',
    description: 'Authentic Italian pizza',
    image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300&h=200&fit=crop',
    cuisine: ['Italian', 'Pizza'],
    rating: 4.5,
    reviewCount: 324,
    deliveryTime: '25-35 min',
    deliveryFee: 2.99,
    minimumOrder: 15,
    isOpen: true,
    address: '123 Main St, Downtown',
    latitude: 37.7749,
    longitude: -122.4194,
    phone: '******-0123',
    categories: [],
    featured: true,
    promoted: false,
    tags: ['Popular', 'Fast Delivery'],
  },
  items: [
    {
      id: 'item1',
      menuItem: {
        id: 'menu1',
        restaurantId: 'rest1',
        categoryId: 'cat1',
        name: 'Margherita Pizza',
        description: 'Classic pizza with fresh mozzarella',
        image: 'https://images.unsplash.com/photo-1604382354936-07c5d9983bd3?w=300&h=200&fit=crop',
        price: 16.99,
        isAvailable: true,
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: false,
        isSpicy: false,
        customizations: [],
        addOns: [],
        tags: [],
      },
      quantity: 2,
      customizations: [],
      addOns: [],
      price: 33.98,
    },
  ],
  status: 'on_the_way' as OrderStatus,
  subtotal: 33.98,
  deliveryFee: 2.99,
  tax: 2.72,
  tip: 5.00,
  total: 44.69,
  paymentMethod: {
    id: 'pm1',
    type: 'card',
    last4: '4242',
    brand: 'visa',
    isDefault: true,
  },
  deliveryAddress: {
    id: 'addr1',
    userId: 'user1',
    type: 'home',
    label: 'Home',
    street: '123 Main St',
    city: 'San Francisco',
    state: 'CA',
    zipCode: '94102',
    country: 'US',
    latitude: 37.7749,
    longitude: -122.4194,
    isDefault: true,
    instructions: 'Ring doorbell twice',
  },
  estimatedDeliveryTime: '2024-01-15T19:30:00Z',
  createdAt: '2024-01-15T18:45:00Z',
  updatedAt: '2024-01-15T19:15:00Z',
  tracking: {
    orderId: 'order-123',
    driverId: 'driver1',
    driverName: 'John Smith',
    driverPhone: '******-0199',
    driverLocation: {
      latitude: 37.7849,
      longitude: -122.4094,
    },
    estimatedArrival: '2024-01-15T19:30:00Z',
    status: 'on_the_way' as OrderStatus,
    timeline: [
      {
        status: 'pending' as OrderStatus,
        timestamp: '2024-01-15T18:45:00Z',
        message: 'Order placed successfully',
      },
      {
        status: 'confirmed' as OrderStatus,
        timestamp: '2024-01-15T18:47:00Z',
        message: 'Restaurant confirmed your order',
      },
      {
        status: 'preparing' as OrderStatus,
        timestamp: '2024-01-15T18:50:00Z',
        message: 'Your food is being prepared',
      },
      {
        status: 'ready' as OrderStatus,
        timestamp: '2024-01-15T19:10:00Z',
        message: 'Order is ready for pickup',
      },
      {
        status: 'picked_up' as OrderStatus,
        timestamp: '2024-01-15T19:15:00Z',
        message: 'Driver picked up your order',
      },
      {
        status: 'on_the_way' as OrderStatus,
        timestamp: '2024-01-15T19:16:00Z',
        message: 'Driver is on the way to your location',
      },
    ],
  },
};

// Helper function to get progress value based on order status
const getProgressValue = (status: OrderStatus): number => {
  switch (status) {
    case 'pending': return 0.2;
    case 'confirmed': return 0.4;
    case 'preparing': return 0.6;
    case 'ready': return 0.8;
    case 'out_for_delivery': return 0.9;
    case 'delivered': return 1.0;
    case 'cancelled': return 0;
    default: return 0;
  }
};

// Helper function to get status gradient colors
const getStatusGradient = (status: OrderStatus): string[] => {
  switch (status) {
    case 'pending': return ['#FFA726', '#FF9800'];
    case 'confirmed': return ['#42A5F5', '#2196F3'];
    case 'preparing': return ['#AB47BC', '#9C27B0'];
    case 'ready': return ['#66BB6A', '#4CAF50'];
    case 'out_for_delivery': return ['#FF7043', '#FF5722'];
    case 'delivered': return ['#26A69A', '#009688'];
    case 'cancelled': return ['#EF5350', '#F44336'];
    default: return ['#9E9E9E', '#757575'];
  }
};

// Helper function to get status icon
const getStatusIcon = (status: OrderStatus): string => {
  switch (status) {
    case 'pending': return 'time-outline';
    case 'confirmed': return 'checkmark-circle-outline';
    case 'preparing': return 'restaurant-outline';
    case 'ready': return 'bag-check-outline';
    case 'out_for_delivery': return 'bicycle-outline';
    case 'delivered': return 'checkmark-done-outline';
    case 'cancelled': return 'close-circle-outline';
    default: return 'help-outline';
  }
};

export default function OrderTrackingScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [order] = useState<Order>(mockOrder);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Modern animations
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute

    // Initialize animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(progressAnim, {
        toValue: getProgressValue(order.status),
        duration: 1000,
        useNativeDriver: false,
      }),
    ]).start();

    return () => clearInterval(timer);
  }, []);

  const getStatusText = (status: OrderStatus) => {
    switch (status) {
      case 'pending':
        return 'Order Pending';
      case 'confirmed':
        return 'Order Confirmed';
      case 'preparing':
        return 'Being Prepared';
      case 'ready':
        return 'Ready for Pickup';
      case 'picked_up':
        return 'Picked Up';
      case 'on_the_way':
        return 'On the Way';
      case 'delivered':
        return 'Delivered';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'Unknown Status';
    }
  };

  const getEstimatedTime = () => {
    const estimatedTime = new Date(order.estimatedDeliveryTime);
    const diffMs = estimatedTime.getTime() - currentTime.getTime();
    const diffMins = Math.ceil(diffMs / (1000 * 60));
    
    if (diffMins <= 0) {
      return 'Arriving now';
    } else if (diffMins < 60) {
      return `${diffMins} min`;
    } else {
      const hours = Math.floor(diffMins / 60);
      const mins = diffMins % 60;
      return `${hours}h ${mins}m`;
    }
  };

  const handleCallDriver = () => {
    if (order.tracking?.driverPhone) {
      Linking.openURL(`tel:${order.tracking.driverPhone}`);
    }
  };

  const handleCallRestaurant = () => {
    Linking.openURL(`tel:${order.restaurant.phone}`);
  };

  const handleCancelOrder = () => {
    Alert.alert(
      'Cancel Order',
      'Are you sure you want to cancel this order? This action cannot be undone.',
      [
        { text: 'Keep Order', style: 'cancel' },
        {
          text: 'Cancel Order',
          style: 'destructive',
          onPress: () => {
            // Handle order cancellation
            Alert.alert('Order Cancelled', 'Your order has been cancelled successfully.');
          },
        },
      ]
    );
  };

  const renderTimelineItem = (event: TrackingEvent, index: number) => {
    const isCompleted = order.tracking!.timeline.findIndex(e => e.status === order.status) >= index;
    const isCurrent = event.status === order.status;

    const itemAnimatedStyle = {
      opacity: fadeAnim,
      transform: [
        {
          translateX: slideAnim.interpolate({
            inputRange: [0, 30],
            outputRange: [0, index * 5],
          }),
        },
      ],
    };

    return (
      <Animated.View key={index} style={[modernStyles.timelineItem, itemAnimatedStyle]}>
        <View style={modernStyles.timelineIndicator}>
          <View style={[
            modernStyles.timelineDot,
            {
              backgroundColor: isCompleted ? COLORS.primary : '#E0E0E0',
              borderColor: isCurrent ? COLORS.primary : '#E0E0E0',
            }
          ]}>
            {isCompleted && (
              <Ionicons
                name="checkmark"
                size={12}
                color={COLORS.white}
              />
            )}
          </View>
          {index < order.tracking!.timeline.length - 1 && (
            <View style={[
              modernStyles.timelineLine,
              isCompleted && modernStyles.timelineLineCompleted,
            ]} />
          )}
        </View>
        <View style={modernStyles.timelineContent}>
          <Text style={[
            modernStyles.timelineEventTitle,
            { color: isCurrent ? COLORS.primary : COLORS.textPrimary }
          ]}>
            {event.message}
          </Text>
          <Text style={modernStyles.timelineEventTime}>
            {new Date(event.timestamp).toLocaleTimeString([], {
              hour: '2-digit',
              minute: '2-digit'
            })}
          </Text>
        </View>
      </Animated.View>
    );
  };

  return (
    <SafeAreaView style={modernStyles.container}>
      <ScrollView
        style={modernStyles.scrollView}
        showsVerticalScrollIndicator={false}
        scrollEventThrottle={16}
      >
        {/* Modern Order Status Header */}
        <Animated.View style={[modernStyles.statusCard, { opacity: fadeAnim }]}>
          <LinearGradient
            colors={getStatusGradient(order.status)}
            style={modernStyles.statusGradient}
          >
            <View style={modernStyles.statusHeader}>
              <View style={modernStyles.statusInfo}>
                <Text style={modernStyles.statusText}>{getStatusText(order.status)}</Text>
                <Text style={modernStyles.orderNumber}>Order #{order.id}</Text>
                <Text style={modernStyles.restaurantName}>{order.restaurant.name}</Text>
              </View>
              <View style={modernStyles.statusIcon}>
                <Ionicons
                  name={getStatusIcon(order.status)}
                  size={32}
                  color={COLORS.white}
                />
              </View>
            </View>

            {/* Progress Bar */}
            <View style={modernStyles.progressContainer}>
              <View style={modernStyles.progressTrack}>
                <Animated.View
                  style={[
                    modernStyles.progressBar,
                    {
                      width: progressAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: ['0%', '100%'],
                      }),
                    },
                  ]}
                />
              </View>
              <Text style={modernStyles.estimatedTime}>
                ETA: {getEstimatedTime()}
              </Text>
            </View>
          </LinearGradient>
        </Animated.View>

        {/* Modern Driver Info */}
        {order.tracking?.driverName && order.status !== 'delivered' && (
          <Animated.View style={[modernStyles.driverCard, {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }]
          }]}>
            <View style={modernStyles.driverHeader}>
              <Image
                source={{ uri: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face' }}
                style={modernStyles.driverAvatar}
              />
              <View style={modernStyles.driverInfo}>
                <Text style={modernStyles.driverName}>{order.tracking.driverName}</Text>
                <View style={modernStyles.driverRating}>
                  <Ionicons name="star" size={14} color="#FFB400" />
                  <Text style={modernStyles.ratingText}>4.8 (324 reviews)</Text>
                </View>
              </View>
            </View>

            <View style={modernStyles.driverActions}>
              <TouchableOpacity
                style={[modernStyles.actionButton, modernStyles.callButton]}
                onPress={handleCallDriver}
                activeOpacity={0.8}
              >
                <Ionicons name="call" size={18} color="#2196F3" />
                <Text style={[modernStyles.actionButtonText, { color: '#2196F3' }]}>Call</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[modernStyles.actionButton, modernStyles.messageButton]}
                onPress={() => Alert.alert('Message', 'Messaging feature coming soon!')}
                activeOpacity={0.8}
              >
                <Ionicons name="chatbubble" size={18} color="#9C27B0" />
                <Text style={[modernStyles.actionButtonText, { color: '#9C27B0' }]}>Message</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        )}

        {/* Modern Order Timeline */}
        <Animated.View style={[modernStyles.timelineCard, {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }]}>
          <Text style={modernStyles.timelineTitle}>Order Progress</Text>
          <View style={modernStyles.timeline}>
            {order.tracking?.timeline.map(renderTimelineItem)}
          </View>
        </Animated.View>

        {/* Order Items */}
        <Card style={styles.itemsCard}>
          <Text style={styles.sectionTitle}>Order Items</Text>
          {order.items.map((item, index) => (
            <View key={index} style={styles.orderItem}>
              <Image 
                source={{ uri: item.menuItem.image }} 
                style={styles.itemImage} 
              />
              <View style={styles.itemDetails}>
                <Text style={styles.itemName}>{item.menuItem.name}</Text>
                <Text style={styles.itemQuantity}>Qty: {item.quantity}</Text>
                <Text style={styles.itemPrice}>${item.price.toFixed(2)}</Text>
              </View>
            </View>
          ))}
        </Card>

        {/* Delivery Address */}
        <Card style={styles.addressCard}>
          <Text style={styles.sectionTitle}>Delivery Address</Text>
          <View style={styles.addressInfo}>
            <Ionicons name="location" size={20} color={COLORS.primary} />
            <View style={styles.addressText}>
              <Text style={styles.addressLabel}>{order.deliveryAddress.label}</Text>
              <Text style={styles.addressDetails}>
                {order.deliveryAddress.street}, {order.deliveryAddress.city}
              </Text>
              {order.deliveryAddress.instructions && (
                <Text style={styles.addressInstructions}>
                  Note: {order.deliveryAddress.instructions}
                </Text>
              )}
            </View>
          </View>
        </Card>

        {/* Order Summary */}
        <Card style={styles.summaryCard}>
          <Text style={styles.sectionTitle}>Order Summary</Text>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Subtotal</Text>
            <Text style={styles.summaryValue}>${order.subtotal.toFixed(2)}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Delivery Fee</Text>
            <Text style={styles.summaryValue}>${order.deliveryFee.toFixed(2)}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Tax</Text>
            <Text style={styles.summaryValue}>${order.tax.toFixed(2)}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Tip</Text>
            <Text style={styles.summaryValue}>${order.tip.toFixed(2)}</Text>
          </View>
          <View style={[styles.summaryRow, styles.totalRow]}>
            <Text style={styles.totalLabel}>Total</Text>
            <Text style={styles.totalValue}>${order.total.toFixed(2)}</Text>
          </View>
        </Card>
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <Button
          title="Call Restaurant"
          onPress={handleCallRestaurant}
          variant="outline"
          style={styles.actionButton}
        />
        {order.status !== 'delivered' && order.status !== 'cancelled' && (
          <Button
            title="Cancel Order"
            onPress={handleCancelOrder}
            variant="outline"
            style={[styles.actionButton, styles.cancelButton]}
          />
        )}
      </View>
    </SafeAreaView>
  );
}

// Modern styles for order tracking screen
const modernStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  scrollView: {
    flex: 1,
  },
  statusCard: {
    margin: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  statusGradient: {
    padding: SPACING.lg,
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.md,
  },
  statusInfo: {
    flex: 1,
  },
  statusText: {
    fontSize: TYPOGRAPHY.fontSize['2xl'],
    fontWeight: '700',
    color: COLORS.white,
    marginBottom: SPACING.xs,
  },
  orderNumber: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: SPACING.xs,
  },
  restaurantName: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  statusIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressContainer: {
    marginTop: SPACING.sm,
  },
  progressTrack: {
    height: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 3,
    marginBottom: SPACING.sm,
  },
  progressBar: {
    height: '100%',
    backgroundColor: COLORS.white,
    borderRadius: 3,
  },
  estimatedTime: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  driverCard: {
    margin: SPACING.md,
    marginTop: 0,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: COLORS.white,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  driverHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  driverAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: SPACING.md,
  },
  driverInfo: {
    flex: 1,
  },
  driverName: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '700',
    color: COLORS.textPrimary,
    marginBottom: 2,
  },
  driverRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    marginLeft: 4,
  },
  driverActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    marginHorizontal: SPACING.xs,
  },
  callButton: {
    backgroundColor: '#E3F2FD',
  },
  messageButton: {
    backgroundColor: '#F3E5F5',
  },
  actionButtonText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '600',
    marginLeft: SPACING.xs,
  },
  timelineCard: {
    margin: SPACING.md,
    marginTop: 0,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: COLORS.white,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  timelineTitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: '700',
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: SPACING.md,
  },
  timelineIndicator: {
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  timelineDot: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
  },
  timelineLine: {
    width: 2,
    flex: 1,
    backgroundColor: '#E0E0E0',
    marginTop: SPACING.xs,
  },
  timelineLineCompleted: {
    backgroundColor: COLORS.primary,
  },
  timelineContent: {
    flex: 1,
    paddingTop: 2,
  },
  timelineEventTitle: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: 2,
  },
  timelineEventTime: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
  },
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollView: {
    flex: 1,
  },
  statusCard: {
    margin: SPACING.md,
    marginBottom: SPACING.sm,
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  statusBadge: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.full,
  },
  statusText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  estimatedTime: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  orderNumber: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  restaurantName: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
  },
  driverCard: {
    margin: SPACING.md,
    marginTop: 0,
    marginBottom: SPACING.sm,
  },
  driverInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  driverAvatar: {
    width: 50,
    height: 50,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: COLORS.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.md,
  },
  driverDetails: {
    flex: 1,
  },
  driverName: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  driverStatus: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
  },
  callButton: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: COLORS.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  timelineCard: {
    margin: SPACING.md,
    marginTop: 0,
    marginBottom: SPACING.sm,
  },
  timelineTitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
  },
  timeline: {
    paddingLeft: SPACING.sm,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: SPACING.md,
  },
  timelineLeft: {
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  timelineIcon: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: COLORS.border,
    backgroundColor: COLORS.white,
    alignItems: 'center',
    justifyContent: 'center',
  },
  timelineIconCompleted: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.primary + '20',
  },
  timelineIconCurrent: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.primary,
  },
  timelineLine: {
    width: 2,
    height: 30,
    backgroundColor: COLORS.border,
    marginTop: SPACING.xs,
  },
  timelineLineCompleted: {
    backgroundColor: COLORS.primary,
  },
  timelineContent: {
    flex: 1,
    paddingTop: SPACING.xs,
  },
  timelineTitleCurrent: {
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  timelineTime: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
  },
  itemsCard: {
    margin: SPACING.md,
    marginTop: 0,
    marginBottom: SPACING.sm,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
  },
  orderItem: {
    flexDirection: 'row',
    marginBottom: SPACING.md,
    paddingBottom: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  itemImage: {
    width: 60,
    height: 60,
    borderRadius: BORDER_RADIUS.md,
    marginRight: SPACING.md,
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  itemQuantity: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  itemPrice: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  addressCard: {
    margin: SPACING.md,
    marginTop: 0,
    marginBottom: SPACING.sm,
  },
  addressInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  addressText: {
    flex: 1,
    marginLeft: SPACING.sm,
  },
  addressLabel: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  addressDetails: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  addressInstructions: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    fontStyle: 'italic',
  },
  summaryCard: {
    margin: SPACING.md,
    marginTop: 0,
    marginBottom: SPACING.sm,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  summaryLabel: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
  },
  summaryValue: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textPrimary,
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    paddingTop: SPACING.sm,
    marginTop: SPACING.sm,
  },
  totalLabel: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
  },
  totalValue: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  actionButtons: {
    flexDirection: 'row',
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    gap: SPACING.sm,
  },
  actionButton: {
    flex: 1,
  },
  cancelButton: {
    borderColor: COLORS.error,
  },
});
