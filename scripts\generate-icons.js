// <PERSON>ript to generate app icons from SVG
// This is a placeholder - in a real project you would use tools like:
// - @expo/image-utils
// - sharp
// - or online converters

console.log('To generate app icons from SVG:');
console.log('1. Use online SVG to PNG converter (like convertio.co)');
console.log('2. Convert foodway-icon.svg to the following sizes:');
console.log('   - icon.png: 1024x1024');
console.log('   - adaptive-icon.png: 1024x1024');
console.log('   - splash-icon.png: 512x512');
console.log('   - favicon.png: 32x32');
console.log('3. Replace the files in assets/images/');
console.log('');
console.log('Or use a tool like:');
console.log('npx @expo/image-utils resize assets/images/foodway-icon.svg --width 1024 --height 1024 --output assets/images/icon.png');
