import { router } from 'expo-router';
import { Alert } from 'react-native';
import { AppError, errorHandler } from './errorHandler';
import { addBreadcrumb } from './errorLogger';
import { ERROR_TYPES } from './constants';

// Navigation error types
export class NavigationError extends AppError {
  constructor(message: string, route?: string, params?: any) {
    super(
      ERROR_TYPES.NAVIGATION_ERROR || 'NAVIGATION_ERROR',
      message,
      'Navigation failed. Please try again.',
      'NAV_ERROR',
      undefined,
      true
    );
    this.route = route;
    this.params = params;
  }

  public readonly route?: string;
  public readonly params?: any;
}

export class RouteValidationError extends AppError {
  constructor(message: string, route: string, invalidParams: string[]) {
    super(
      ERROR_TYPES.VALIDATION_ERROR,
      message,
      'Invalid navigation parameters. Please check your input.',
      'ROUTE_VALIDATION_ERROR',
      400,
      false
    );
    this.route = route;
    this.invalidParams = invalidParams;
  }

  public readonly route: string;
  public readonly invalidParams: string[];
}

// Route parameter validation schemas
export interface RouteParamSchema {
  [key: string]: {
    required?: boolean;
    type?: 'string' | 'number' | 'boolean';
    pattern?: RegExp;
    validator?: (value: any) => boolean;
  };
}

export const routeSchemas: Record<string, RouteParamSchema> = {
  '/restaurant/[id]': {
    id: {
      required: true,
      type: 'string',
      pattern: /^[a-zA-Z0-9_-]+$/,
    },
  },
  '/order/[orderId]': {
    orderId: {
      required: true,
      type: 'string',
      pattern: /^order_[a-zA-Z0-9]+$/,
    },
  },
  '/menu/[restaurantId]/[categoryId]': {
    restaurantId: {
      required: true,
      type: 'string',
    },
    categoryId: {
      required: false,
      type: 'string',
    },
  },
  '/profile/edit': {
    section: {
      required: false,
      type: 'string',
      validator: (value) => ['personal', 'address', 'payment', 'preferences'].includes(value),
    },
  },
};

class NavigationErrorHandler {
  private static instance: NavigationErrorHandler;
  private navigationHistory: Array<{ route: string; timestamp: number; params?: any }> = [];
  private maxHistorySize = 20;

  private constructor() {}

  public static getInstance(): NavigationErrorHandler {
    if (!NavigationErrorHandler.instance) {
      NavigationErrorHandler.instance = new NavigationErrorHandler();
    }
    return NavigationErrorHandler.instance;
  }

  // Validate route parameters
  public validateRouteParams(route: string, params: any): RouteValidationError | null {
    const schema = routeSchemas[route];
    if (!schema) return null;

    const invalidParams: string[] = [];

    Object.keys(schema).forEach(paramName => {
      const rule = schema[paramName];
      const value = params[paramName];

      // Check required parameters
      if (rule.required && (value === undefined || value === null || value === '')) {
        invalidParams.push(`${paramName} is required`);
        return;
      }

      // Skip validation if parameter is not provided and not required
      if (!rule.required && (value === undefined || value === null)) {
        return;
      }

      // Type validation
      if (rule.type) {
        const actualType = typeof value;
        if (rule.type === 'number' && actualType !== 'number') {
          if (isNaN(Number(value))) {
            invalidParams.push(`${paramName} must be a number`);
          }
        } else if (rule.type !== actualType) {
          invalidParams.push(`${paramName} must be a ${rule.type}`);
        }
      }

      // Pattern validation
      if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
        invalidParams.push(`${paramName} format is invalid`);
      }

      // Custom validation
      if (rule.validator && !rule.validator(value)) {
        invalidParams.push(`${paramName} is invalid`);
      }
    });

    if (invalidParams.length > 0) {
      return new RouteValidationError(
        `Invalid parameters for route ${route}: ${invalidParams.join(', ')}`,
        route,
        invalidParams
      );
    }

    return null;
  }

  // Safe navigation with error handling
  public async navigate(route: string, params?: any): Promise<boolean> {
    try {
      // Add to navigation history
      this.addToHistory(route, params);

      // Validate parameters
      const validationError = this.validateRouteParams(route, params);
      if (validationError) {
        throw validationError;
      }

      // Add breadcrumb
      addBreadcrumb('navigation', `Navigating to ${route}`, 'info', { params });

      // Attempt navigation
      if (params) {
        router.push({ pathname: route as any, params });
      } else {
        router.push(route as any);
      }

      return true;
    } catch (error) {
      const navError = this.handleNavigationError(error, route, params);
      this.showNavigationError(navError);
      return false;
    }
  }

  // Safe replace navigation
  public async replace(route: string, params?: any): Promise<boolean> {
    try {
      this.addToHistory(route, params);

      const validationError = this.validateRouteParams(route, params);
      if (validationError) {
        throw validationError;
      }

      addBreadcrumb('navigation', `Replacing with ${route}`, 'info', { params });

      if (params) {
        router.replace({ pathname: route as any, params });
      } else {
        router.replace(route as any);
      }

      return true;
    } catch (error) {
      const navError = this.handleNavigationError(error, route, params);
      this.showNavigationError(navError);
      return false;
    }
  }

  // Safe back navigation
  public async goBack(): Promise<boolean> {
    try {
      addBreadcrumb('navigation', 'Going back', 'info');
      
      if (router.canGoBack()) {
        router.back();
        return true;
      } else {
        // If can't go back, navigate to home
        return this.navigate('/(tabs)');
      }
    } catch (error) {
      const navError = this.handleNavigationError(error, 'back');
      this.showNavigationError(navError);
      return false;
    }
  }

  // Handle deep links safely
  public async handleDeepLink(url: string): Promise<boolean> {
    try {
      addBreadcrumb('navigation', `Handling deep link: ${url}`, 'info');

      // Parse the URL
      const parsedUrl = this.parseDeepLink(url);
      if (!parsedUrl) {
        throw new NavigationError('Invalid deep link format', url);
      }

      // Validate and navigate
      return this.navigate(parsedUrl.route, parsedUrl.params);
    } catch (error) {
      const navError = this.handleNavigationError(error, url);
      this.showNavigationError(navError);
      return false;
    }
  }

  // Parse deep link URL
  private parseDeepLink(url: string): { route: string; params?: any } | null {
    try {
      // Handle different URL schemes
      const cleanUrl = url.replace(/^(foodway|https?):\/\//, '');
      const [path, queryString] = cleanUrl.split('?');
      
      // Parse query parameters
      const params: any = {};
      if (queryString) {
        queryString.split('&').forEach(param => {
          const [key, value] = param.split('=');
          if (key && value) {
            params[decodeURIComponent(key)] = decodeURIComponent(value);
          }
        });
      }

      // Map external paths to internal routes
      const routeMap: Record<string, string> = {
        'restaurant': '/restaurant/[id]',
        'order': '/order/[orderId]',
        'menu': '/menu/[restaurantId]',
        'profile': '/profile',
        'search': '/(tabs)/search',
        'cart': '/(tabs)/cart',
        'orders': '/(tabs)/orders',
      };

      const route = routeMap[path] || `/${path}`;
      
      return { route, params: Object.keys(params).length > 0 ? params : undefined };
    } catch (error) {
      return null;
    }
  }

  // Add navigation to history
  private addToHistory(route: string, params?: any): void {
    this.navigationHistory.push({
      route,
      params,
      timestamp: Date.now(),
    });

    // Keep history size manageable
    if (this.navigationHistory.length > this.maxHistorySize) {
      this.navigationHistory = this.navigationHistory.slice(-this.maxHistorySize);
    }
  }

  // Handle navigation errors
  private handleNavigationError(error: unknown, route?: string, params?: any): NavigationError {
    if (error instanceof NavigationError || error instanceof RouteValidationError) {
      return error as NavigationError;
    }

    const message = error instanceof Error ? error.message : 'Navigation failed';
    const navError = new NavigationError(message, route, params);
    
    errorHandler.handleError(navError, 'NavigationErrorHandler');
    return navError;
  }

  // Show navigation error to user
  private showNavigationError(error: NavigationError): void {
    if (error instanceof RouteValidationError) {
      Alert.alert(
        'Invalid Link',
        'The link you followed is invalid or has expired.',
        [
          { text: 'Go Home', onPress: () => this.navigate('/(tabs)') },
          { text: 'OK', style: 'cancel' },
        ]
      );
    } else {
      Alert.alert(
        'Navigation Error',
        error.userMessage,
        [
          { text: 'Retry', onPress: () => this.retryLastNavigation() },
          { text: 'Go Home', onPress: () => this.navigate('/(tabs)') },
          { text: 'OK', style: 'cancel' },
        ]
      );
    }
  }

  // Retry last navigation
  private retryLastNavigation(): void {
    const lastNav = this.navigationHistory[this.navigationHistory.length - 1];
    if (lastNav) {
      this.navigate(lastNav.route, lastNav.params);
    }
  }

  // Get navigation history
  public getNavigationHistory(): Array<{ route: string; timestamp: number; params?: any }> {
    return [...this.navigationHistory];
  }

  // Clear navigation history
  public clearHistory(): void {
    this.navigationHistory = [];
  }

  // Check if route exists
  public isValidRoute(route: string): boolean {
    // This would typically check against your app's route configuration
    const validRoutes = [
      '/(tabs)',
      '/(tabs)/home',
      '/(tabs)/search',
      '/(tabs)/cart',
      '/(tabs)/orders',
      '/(tabs)/profile',
      '/restaurant/[id]',
      '/order/[orderId]',
      '/menu/[restaurantId]',
      '/checkout',
      '/profile/edit',
      '/(auth)/login',
      '/(auth)/register',
    ];

    return validRoutes.some(validRoute => {
      // Handle dynamic routes
      const pattern = validRoute.replace(/\[([^\]]+)\]/g, '[^/]+');
      const regex = new RegExp(`^${pattern}$`);
      return regex.test(route);
    });
  }
}

// Create singleton instance
export const navigationErrorHandler = NavigationErrorHandler.getInstance();

// Convenience functions
export const safeNavigate = (route: string, params?: any) => 
  navigationErrorHandler.navigate(route, params);

export const safeReplace = (route: string, params?: any) => 
  navigationErrorHandler.replace(route, params);

export const safeGoBack = () => 
  navigationErrorHandler.goBack();

export const handleDeepLink = (url: string) => 
  navigationErrorHandler.handleDeepLink(url);

export const validateRouteParams = (route: string, params: any) => 
  navigationErrorHandler.validateRouteParams(route, params);

export default navigationErrorHandler;
