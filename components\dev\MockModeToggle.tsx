import React, { useState } from 'react';
import { View, Text, Switch, StyleSheet, Alert } from 'react-native';
import { MOCK_CONFIG, toggleMockMode, shouldShowMockIndicator } from '../../config/mockConfig';
import { COLORS, SPACING } from '../../utils/constants';

interface MockModeToggleProps {
  onToggle?: (enabled: boolean) => void;
}

export const MockModeToggle: React.FC<MockModeToggleProps> = ({ onToggle }) => {
  const [isMockEnabled, setIsMockEnabled] = useState(MOCK_CONFIG.ENABLED);

  const handleToggle = () => {
    const newState = toggleMockMode();
    setIsMockEnabled(newState);
    
    Alert.alert(
      'Mock Mode',
      `Mock mode is now ${newState ? 'enabled' : 'disabled'}. Please restart the app for changes to take effect.`,
      [{ text: 'OK' }]
    );
    
    onToggle?.(newState);
  };

  // Only show in development mode
  if (!shouldShowMockIndicator()) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.label}>Mock API Mode</Text>
        <Switch
          value={isMockEnabled}
          onValueChange={handleToggle}
          trackColor={{ false: COLORS.gray, true: COLORS.primary }}
          thumbColor={isMockEnabled ? COLORS.white : COLORS.lightGray}
        />
      </View>
      <Text style={styles.description}>
        {isMockEnabled 
          ? 'Using mock data for development' 
          : 'Using real API endpoints'
        }
      </Text>
    </View>
  );
};

// Mock mode indicator for the status bar area
export const MockModeIndicator: React.FC = () => {
  if (!shouldShowMockIndicator() || !MOCK_CONFIG.ENABLED) {
    return null;
  }

  return (
    <View style={styles.indicator}>
      <Text style={styles.indicatorText}>MOCK MODE</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.border,
    margin: SPACING.sm,
  },
  content: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
  },
  description: {
    fontSize: 12,
    color: COLORS.textSecondary,
    fontStyle: 'italic',
  },
  indicator: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: COLORS.warning,
    paddingHorizontal: SPACING.xs,
    paddingVertical: 2,
    borderBottomLeftRadius: 4,
    zIndex: 1000,
  },
  indicatorText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: COLORS.white,
  },
});

export default MockModeToggle;
