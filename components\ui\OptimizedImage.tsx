import React, { useState, useCallback, useMemo } from 'react';
import {
  Image,
  ImageStyle,
  StyleSheet,
  View,
  ActivityIndicator,
  Text,
  ImageSourcePropType,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, SPACING, BORDER_RADIUS } from '../../utils/constants';
import { getOptimizedImageUrl, getOptimizedImageDimensions } from '../../utils/performance';

interface OptimizedImageProps {
  source: string | ImageSourcePropType;
  width: number;
  height: number;
  style?: ImageStyle;
  placeholder?: React.ReactNode;
  fallback?: React.ReactNode;
  quality?: number;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
  borderRadius?: number;
  showLoadingIndicator?: boolean;
  onLoad?: () => void;
  onError?: () => void;
  lazy?: boolean;
  cache?: boolean;
}

export default function OptimizedImage({
  source,
  width,
  height,
  style,
  placeholder,
  fallback,
  quality = 80,
  resizeMode = 'cover',
  borderRadius,
  showLoadingIndicator = true,
  onLoad,
  onError,
  lazy = false,
  cache = true,
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isVisible, setIsVisible] = useState(!lazy);

  // Optimize image URL and dimensions
  const optimizedSource = useMemo(() => {
    if (typeof source === 'string') {
      const optimizedDimensions = getOptimizedImageDimensions(width, height);
      return {
        uri: getOptimizedImageUrl(source, optimizedDimensions.width, optimizedDimensions.height, quality),
        cache: cache ? 'force-cache' : 'reload',
      };
    }
    return source;
  }, [source, width, height, quality, cache]);

  const handleLoad = useCallback(() => {
    setIsLoading(false);
    setHasError(false);
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback(() => {
    setIsLoading(false);
    setHasError(true);
    onError?.();
  }, [onError]);

  const handleLayout = useCallback(() => {
    if (lazy && !isVisible) {
      setIsVisible(true);
    }
  }, [lazy, isVisible]);

  const containerStyle = useMemo(() => [
    styles.container,
    {
      width,
      height,
      borderRadius: borderRadius || 0,
    },
    style,
  ], [width, height, borderRadius, style]);

  const imageStyle = useMemo(() => [
    styles.image,
    {
      width,
      height,
      borderRadius: borderRadius || 0,
    },
  ], [width, height, borderRadius]);

  // Render placeholder while lazy loading
  if (lazy && !isVisible) {
    return (
      <View style={containerStyle} onLayout={handleLayout}>
        {placeholder || <DefaultPlaceholder width={width} height={height} />}
      </View>
    );
  }

  // Render error state
  if (hasError) {
    return (
      <View style={containerStyle}>
        {fallback || <DefaultFallback width={width} height={height} />}
      </View>
    );
  }

  return (
    <View style={containerStyle}>
      {/* Loading placeholder */}
      {isLoading && (
        <View style={[styles.overlay, imageStyle]}>
          {placeholder || <DefaultPlaceholder width={width} height={height} />}
          {showLoadingIndicator && (
            <View style={styles.loadingIndicator}>
              <ActivityIndicator size="small" color={COLORS.primary} />
            </View>
          )}
        </View>
      )}

      {/* Actual image */}
      <Image
        source={optimizedSource}
        style={[imageStyle, isLoading && styles.hidden]}
        resizeMode={resizeMode}
        onLoad={handleLoad}
        onError={handleError}
        fadeDuration={300}
      />
    </View>
  );
}

// Default placeholder component
const DefaultPlaceholder: React.FC<{ width: number; height: number }> = ({ width, height }) => (
  <View style={[styles.placeholder, { width, height }]}>
    <Ionicons name="image-outline" size={Math.min(width, height) * 0.3} color={COLORS.gray[400]} />
  </View>
);

// Default fallback component for errors
const DefaultFallback: React.FC<{ width: number; height: number }> = ({ width, height }) => (
  <View style={[styles.fallback, { width, height }]}>
    <Ionicons name="image-outline" size={Math.min(width, height) * 0.2} color={COLORS.gray[500]} />
    <Text style={styles.fallbackText}>Image not available</Text>
  </View>
);

// Memoized version for better performance in lists
export const MemoizedOptimizedImage = React.memo(OptimizedImage, (prevProps, nextProps) => {
  return (
    prevProps.source === nextProps.source &&
    prevProps.width === nextProps.width &&
    prevProps.height === nextProps.height &&
    prevProps.quality === nextProps.quality &&
    prevProps.resizeMode === nextProps.resizeMode &&
    prevProps.borderRadius === nextProps.borderRadius
  );
});

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    overflow: 'hidden',
  },
  image: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 1,
  },
  hidden: {
    opacity: 0,
  },
  placeholder: {
    backgroundColor: COLORS.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
  },
  fallback: {
    backgroundColor: COLORS.gray[200],
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.sm,
  },
  fallbackText: {
    fontSize: 12,
    color: COLORS.gray[500],
    textAlign: 'center',
    marginTop: SPACING.xs,
  },
  loadingIndicator: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -10 }, { translateY: -10 }],
    zIndex: 2,
  },
});
