#!/usr/bin/env node

const { execSync } = require('child_process');
const readline = require('readline');
const crypto = require('crypto');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function execCommand(command, options = {}) {
  try {
    const result = execSync(command, { 
      stdio: 'pipe', 
      encoding: 'utf8',
      ...options 
    });
    return result.trim();
  } catch (error) {
    log(`Error executing command: ${command}`, colors.red);
    throw error;
  }
}

function generateSecretKey(length = 32) {
  return crypto.randomBytes(length).toString('hex');
}

function createReadlineInterface() {
  return readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
}

function askQuestion(rl, question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

async function setupRailwayProject() {
  log('🚂 Setting up Railway project for FoodWay Customer App', colors.bright);
  log('=' .repeat(60), colors.cyan);

  const rl = createReadlineInterface();

  try {
    // Check if Railway CLI is installed
    try {
      execCommand('railway --version');
      log('✅ Railway CLI is installed', colors.green);
    } catch (error) {
      log('❌ Railway CLI is not installed', colors.red);
      log('Please install it with: npm install -g @railway/cli', colors.yellow);
      process.exit(1);
    }

    // Login to Railway
    log('🔐 Logging in to Railway...', colors.blue);
    try {
      execCommand('railway whoami');
      log('✅ Already logged in to Railway', colors.green);
    } catch (error) {
      log('Please login to Railway:', colors.yellow);
      execCommand('railway login', { stdio: 'inherit' });
    }

    // Create or link project
    const projectAction = await askQuestion(rl, 
      '📁 Do you want to (c)reate a new project or (l)ink to existing? [c/l]: '
    );

    if (projectAction.toLowerCase() === 'c') {
      const projectName = await askQuestion(rl, 
        '📝 Enter project name (default: foodway-customer-app): '
      ) || 'foodway-customer-app';
      
      log(`🆕 Creating new Railway project: ${projectName}`, colors.blue);
      execCommand(`railway init ${projectName}`);
    } else {
      log('🔗 Linking to existing project...', colors.blue);
      execCommand('railway link', { stdio: 'inherit' });
    }

    // Add PostgreSQL service
    log('🐘 Adding PostgreSQL database...', colors.blue);
    try {
      execCommand('railway add postgresql');
      log('✅ PostgreSQL service added', colors.green);
    } catch (error) {
      log('⚠️  PostgreSQL service might already exist', colors.yellow);
    }

    // Add Redis service
    log('🔴 Adding Redis cache...', colors.blue);
    try {
      execCommand('railway add redis');
      log('✅ Redis service added', colors.green);
    } catch (error) {
      log('⚠️  Redis service might already exist', colors.yellow);
    }

    // Set up environment variables
    log('🔧 Setting up environment variables...', colors.blue);
    
    const envVars = {
      NODE_ENV: 'production',
      PORT: '3000',
      JWT_SECRET: generateSecretKey(),
      REFRESH_TOKEN_SECRET: generateSecretKey(),
      SESSION_SECRET: generateSecretKey(),
      BCRYPT_ROUNDS: '12',
      RATE_LIMIT_WINDOW_MS: '900000',
      RATE_LIMIT_MAX_REQUESTS: '100',
      CORS_CREDENTIALS: 'true',
      CACHE_TTL: '3600',
      MAX_FILE_SIZE: '10485760',
      ALLOWED_FILE_TYPES: 'image/jpeg,image/png,image/gif',
    };

    // Set basic environment variables
    for (const [key, value] of Object.entries(envVars)) {
      try {
        execCommand(`railway variables set ${key}="${value}"`);
        log(`✅ Set ${key}`, colors.green);
      } catch (error) {
        log(`⚠️  Failed to set ${key}`, colors.yellow);
      }
    }

    // Prompt for external service API keys
    log('🔑 Setting up external service API keys...', colors.blue);
    log('You can skip these for now and set them later', colors.yellow);

    const externalServices = [
      {
        key: 'STRIPE_PUBLISHABLE_KEY',
        description: 'Stripe Publishable Key (for payments)',
        required: false
      },
      {
        key: 'STRIPE_SECRET_KEY',
        description: 'Stripe Secret Key (for payments)',
        required: true
      },
      {
        key: 'STRIPE_WEBHOOK_SECRET',
        description: 'Stripe Webhook Secret',
        required: false
      },
      {
        key: 'GOOGLE_MAPS_API_KEY',
        description: 'Google Maps API Key (for location services)',
        required: false
      },
      {
        key: 'FIREBASE_SERVER_KEY',
        description: 'Firebase Server Key (for push notifications)',
        required: false
      },
      {
        key: 'EMAIL_SERVICE_API_KEY',
        description: 'Email Service API Key (SendGrid, Mailgun, etc.)',
        required: false
      },
      {
        key: 'CLOUDINARY_CLOUD_NAME',
        description: 'Cloudinary Cloud Name (for image uploads)',
        required: false
      },
      {
        key: 'CLOUDINARY_API_KEY',
        description: 'Cloudinary API Key',
        required: false
      },
      {
        key: 'CLOUDINARY_API_SECRET',
        description: 'Cloudinary API Secret',
        required: false
      }
    ];

    for (const service of externalServices) {
      const value = await askQuestion(rl, 
        `${service.description} (${service.required ? 'required' : 'optional'}): `
      );
      
      if (value) {
        try {
          execCommand(`railway variables set ${service.key}="${value}"`);
          log(`✅ Set ${service.key}`, colors.green);
        } catch (error) {
          log(`⚠️  Failed to set ${service.key}`, colors.yellow);
        }
      } else if (service.required) {
        log(`⚠️  ${service.key} is required but not provided`, colors.yellow);
        log(`You can set it later with: railway variables set ${service.key}=your_value`, colors.cyan);
      }
    }

    // Set CORS origin
    const corsOrigin = await askQuestion(rl, 
      'CORS Origin URLs (comma-separated, default: http://localhost:3000,http://localhost:19006): '
    ) || 'http://localhost:3000,http://localhost:19006';
    
    try {
      execCommand(`railway variables set CORS_ORIGIN="${corsOrigin}"`);
      log('✅ Set CORS_ORIGIN', colors.green);
    } catch (error) {
      log('⚠️  Failed to set CORS_ORIGIN', colors.yellow);
    }

    // Show current variables
    log('📋 Current environment variables:', colors.blue);
    try {
      const variables = execCommand('railway variables');
      console.log(variables);
    } catch (error) {
      log('Could not fetch variables', colors.yellow);
    }

    // Show next steps
    log('🎉 Railway setup completed!', colors.green);
    log('', colors.reset);
    log('📋 Next steps:', colors.bright);
    log('1. Deploy your application: npm run railway:deploy', colors.cyan);
    log('2. Check deployment status: railway status', colors.cyan);
    log('3. View logs: railway logs', colors.cyan);
    log('4. Open your app: railway open', colors.cyan);
    log('', colors.reset);
    log('🔧 Additional commands:', colors.bright);
    log('- Set environment variable: railway variables set KEY=value', colors.cyan);
    log('- Delete environment variable: railway variables delete KEY', colors.cyan);
    log('- Connect to database: railway connect postgresql', colors.cyan);
    log('- Connect to Redis: railway connect redis', colors.cyan);

  } catch (error) {
    log('❌ Setup failed:', colors.red);
    log(error.message, colors.red);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
Railway Setup Script for FoodWay Customer App

Usage: node scripts/railway-setup.js

This script will:
1. Check Railway CLI installation
2. Login to Railway (if needed)
3. Create or link to a Railway project
4. Add PostgreSQL and Redis services
5. Set up environment variables
6. Provide next steps for deployment

Options:
  --help, -h    Show this help message
  `);
  process.exit(0);
}

if (require.main === module) {
  setupRailwayProject();
}

module.exports = {
  setupRailwayProject,
  generateSecretKey,
};
