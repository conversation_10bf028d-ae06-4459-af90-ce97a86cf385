// App Constants for FoodWay Customer App

// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.EXPO_PUBLIC_API_URL || 'https://your-railway-app.railway.app/api',
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
};

// App Configuration
export const APP_CONFIG = {
  NAME: 'FoodWay',
  VERSION: '1.0.0',
  SUPPORT_EMAIL: '<EMAIL>',
  SUPPORT_PHONE: '******-FOODWAY',
};

// Colors
export const COLORS = {
  // Primary Colors
  primary: '#FF6B35',
  primaryDark: '#E55A2B',
  primaryLight: '#FF8A65',
  
  // Secondary Colors
  secondary: '#2E7D32',
  secondaryDark: '#1B5E20',
  secondaryLight: '#4CAF50',
  
  // Neutral Colors
  white: '#FFFFFF',
  black: '#000000',
  gray: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#EEEEEE',
    300: '#E0E0E0',
    400: '#BDBDBD',
    500: '#9E9E9E',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121',
  },
  
  // Status Colors
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',
  
  // Background Colors
  background: '#FAFAFA',
  surface: '#FFFFFF',
  
  // Text Colors
  textPrimary: '#212121',
  textSecondary: '#757575',
  textDisabled: '#BDBDBD',
  
  // Border Colors
  border: '#E0E0E0',
  divider: '#EEEEEE',
};

// Typography
export const TYPOGRAPHY = {
  fontFamily: {
    regular: 'System',
    medium: 'System',
    bold: 'System',
  },
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
  },
  lineHeight: {
    xs: 16,
    sm: 20,
    md: 24,
    lg: 28,
    xl: 32,
    '2xl': 36,
    '3xl': 42,
    '4xl': 48,
  },
};

// Spacing
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
};

// Border Radius
export const BORDER_RADIUS = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 24,
  full: 9999,
};

// Shadows
export const SHADOWS = {
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
};

// Screen Dimensions
export const SCREEN = {
  WIDTH: 375, // Default iPhone width
  HEIGHT: 812, // Default iPhone height
};

// Animation Durations
export const ANIMATION = {
  fast: 150,
  normal: 300,
  slow: 500,
};

// Order Status
export const ORDER_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  PREPARING: 'preparing',
  READY: 'ready',
  PICKED_UP: 'picked_up',
  ON_THE_WAY: 'on_the_way',
  DELIVERED: 'delivered',
  CANCELLED: 'cancelled',
} as const;

// Order Status Colors
export const ORDER_STATUS_COLORS = {
  [ORDER_STATUS.PENDING]: COLORS.warning,
  [ORDER_STATUS.CONFIRMED]: COLORS.info,
  [ORDER_STATUS.PREPARING]: COLORS.secondary,
  [ORDER_STATUS.READY]: COLORS.primary,
  [ORDER_STATUS.PICKED_UP]: COLORS.primary,
  [ORDER_STATUS.ON_THE_WAY]: COLORS.primary,
  [ORDER_STATUS.DELIVERED]: COLORS.success,
  [ORDER_STATUS.CANCELLED]: COLORS.error,
};

// Payment Methods
export const PAYMENT_METHODS = {
  CARD: 'card',
  PAYPAL: 'paypal',
  APPLE_PAY: 'apple_pay',
  GOOGLE_PAY: 'google_pay',
  CASH: 'cash',
} as const;

// Address Types
export const ADDRESS_TYPES = {
  HOME: 'home',
  WORK: 'work',
  OTHER: 'other',
} as const;

// Cuisine Types
export const CUISINE_TYPES = [
  'American',
  'Italian',
  'Chinese',
  'Mexican',
  'Indian',
  'Japanese',
  'Thai',
  'Mediterranean',
  'French',
  'Korean',
  'Vietnamese',
  'Greek',
  'Spanish',
  'Turkish',
  'Lebanese',
  'Brazilian',
  'Moroccan',
  'Ethiopian',
  'Peruvian',
  'Fusion',
];

// Dietary Preferences
export const DIETARY_PREFERENCES = [
  'Vegetarian',
  'Vegan',
  'Gluten-Free',
  'Dairy-Free',
  'Nut-Free',
  'Halal',
  'Kosher',
  'Keto',
  'Low-Carb',
  'High-Protein',
];

// Restaurant Features
export const RESTAURANT_FEATURES = [
  'Free Delivery',
  'Fast Delivery',
  'Highly Rated',
  'New',
  'Promoted',
  'Eco-Friendly',
  'Local Favorite',
  'Award Winner',
];

// Sort Options
export const SORT_OPTIONS = [
  { label: 'Recommended', value: 'recommended' },
  { label: 'Rating', value: 'rating' },
  { label: 'Delivery Time', value: 'delivery_time' },
  { label: 'Distance', value: 'distance' },
  { label: 'Popularity', value: 'popularity' },
  { label: 'Price: Low to High', value: 'price_asc' },
  { label: 'Price: High to Low', value: 'price_desc' },
];

// Map Configuration
export const MAP_CONFIG = {
  DEFAULT_REGION: {
    latitude: 37.7749,
    longitude: -122.4194,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  },
  SEARCH_RADIUS: 10000, // 10km in meters
};

// Notification Types
export const NOTIFICATION_TYPES = {
  ORDER: 'order',
  PROMOTION: 'promotion',
  GENERAL: 'general',
} as const;

// Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_DATA: 'user_data',
  CART_DATA: 'cart_data',
  RECENT_SEARCHES: 'recent_searches',
  FAVORITE_RESTAURANTS: 'favorite_restaurants',
  NOTIFICATION_SETTINGS: 'notification_settings',
  LOCATION_PERMISSION: 'location_permission',
} as const;

// Validation Rules
export const VALIDATION = {
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE_REGEX: /^\+?[\d\s\-\(\)]+$/,
  PASSWORD_MIN_LENGTH: 8,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  REVIEW_MAX_LENGTH: 500,
  SPECIAL_INSTRUCTIONS_MAX_LENGTH: 200,
};

// Error Types and Codes
export const ERROR_TYPES = {
  // Network Errors
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  CONNECTION_ERROR: 'CONNECTION_ERROR',

  // Authentication Errors
  AUTH_ERROR: 'AUTH_ERROR',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  UNAUTHORIZED: 'UNAUTHORIZED',

  // Validation Errors
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  REQUIRED_FIELD: 'REQUIRED_FIELD',
  INVALID_FORMAT: 'INVALID_FORMAT',

  // API Errors
  SERVER_ERROR: 'SERVER_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  CONFLICT: 'CONFLICT',
  RATE_LIMITED: 'RATE_LIMITED',

  // Payment Errors
  PAYMENT_ERROR: 'PAYMENT_ERROR',
  PAYMENT_DECLINED: 'PAYMENT_DECLINED',
  PAYMENT_FAILED: 'PAYMENT_FAILED',

  // Location Errors
  LOCATION_ERROR: 'LOCATION_ERROR',
  LOCATION_DENIED: 'LOCATION_DENIED',
  LOCATION_UNAVAILABLE: 'LOCATION_UNAVAILABLE',

  // Storage Errors
  STORAGE_ERROR: 'STORAGE_ERROR',
  STORAGE_FULL: 'STORAGE_FULL',

  // Permission Errors
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  CAMERA_PERMISSION_DENIED: 'CAMERA_PERMISSION_DENIED',
  NOTIFICATION_PERMISSION_DENIED: 'NOTIFICATION_PERMISSION_DENIED',

  // Generic Errors
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  OPERATION_FAILED: 'OPERATION_FAILED',
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  // Network Errors
  [ERROR_TYPES.NETWORK_ERROR]: 'Network connection failed. Please check your internet connection.',
  [ERROR_TYPES.TIMEOUT_ERROR]: 'Request timed out. Please try again.',
  [ERROR_TYPES.CONNECTION_ERROR]: 'Unable to connect to server. Please try again later.',

  // Authentication Errors
  [ERROR_TYPES.AUTH_ERROR]: 'Authentication failed. Please log in again.',
  [ERROR_TYPES.TOKEN_EXPIRED]: 'Your session has expired. Please log in again.',
  [ERROR_TYPES.INVALID_CREDENTIALS]: 'Invalid email or password. Please try again.',
  [ERROR_TYPES.UNAUTHORIZED]: 'You are not authorized to perform this action.',

  // Validation Errors
  [ERROR_TYPES.VALIDATION_ERROR]: 'Please check your input and try again.',
  [ERROR_TYPES.REQUIRED_FIELD]: 'This field is required.',
  [ERROR_TYPES.INVALID_FORMAT]: 'Please enter a valid format.',

  // API Errors
  [ERROR_TYPES.SERVER_ERROR]: 'Server error occurred. Please try again later.',
  [ERROR_TYPES.NOT_FOUND]: 'The requested resource was not found.',
  [ERROR_TYPES.CONFLICT]: 'A conflict occurred. Please try again.',
  [ERROR_TYPES.RATE_LIMITED]: 'Too many requests. Please wait and try again.',

  // Payment Errors
  [ERROR_TYPES.PAYMENT_ERROR]: 'Payment processing failed. Please try again.',
  [ERROR_TYPES.PAYMENT_DECLINED]: 'Your payment was declined. Please check your payment method.',
  [ERROR_TYPES.PAYMENT_FAILED]: 'Payment failed. Please try a different payment method.',

  // Location Errors
  [ERROR_TYPES.LOCATION_ERROR]: 'Unable to get your location. Please try again.',
  [ERROR_TYPES.LOCATION_DENIED]: 'Location permission denied. Please enable location services.',
  [ERROR_TYPES.LOCATION_UNAVAILABLE]: 'Location services are not available.',

  // Storage Errors
  [ERROR_TYPES.STORAGE_ERROR]: 'Storage operation failed. Please try again.',
  [ERROR_TYPES.STORAGE_FULL]: 'Device storage is full. Please free up space.',

  // Permission Errors
  [ERROR_TYPES.PERMISSION_DENIED]: 'Permission denied. Please grant the required permissions.',
  [ERROR_TYPES.CAMERA_PERMISSION_DENIED]: 'Camera permission denied. Please enable camera access.',
  [ERROR_TYPES.NOTIFICATION_PERMISSION_DENIED]: 'Notification permission denied. Please enable notifications.',

  // Generic Errors
  [ERROR_TYPES.UNKNOWN_ERROR]: 'An unexpected error occurred. Please try again.',
  [ERROR_TYPES.OPERATION_FAILED]: 'Operation failed. Please try again.',
} as const;

// Retry Configuration
export const RETRY_CONFIG = {
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second
  EXPONENTIAL_BACKOFF: true,
  RETRYABLE_ERRORS: [
    ERROR_TYPES.NETWORK_ERROR,
    ERROR_TYPES.TIMEOUT_ERROR,
    ERROR_TYPES.CONNECTION_ERROR,
    ERROR_TYPES.SERVER_ERROR,
  ],
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  ORDER_PLACED: 'Your order has been placed successfully!',
  PROFILE_UPDATED: 'Your profile has been updated.',
  ADDRESS_ADDED: 'Address has been added successfully.',
  PAYMENT_METHOD_ADDED: 'Payment method has been added.',
  REVIEW_SUBMITTED: 'Thank you for your review!',
  PASSWORD_RESET: 'Password reset email has been sent.',
};

// Feature Flags
export const FEATURE_FLAGS = {
  ENABLE_SOCIAL_LOGIN: true,
  ENABLE_APPLE_PAY: true,
  ENABLE_GOOGLE_PAY: true,
  ENABLE_CHAT: true,
  ENABLE_REVIEWS: true,
  ENABLE_PROMOTIONS: true,
  ENABLE_LOYALTY_PROGRAM: true,
  ENABLE_PUSH_NOTIFICATIONS: true,
  ENABLE_LOCATION_TRACKING: true,
};
