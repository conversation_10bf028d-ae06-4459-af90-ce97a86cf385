import { Platform } from 'react-native';
// import * as Device from 'expo-device'; // Temporarily disabled
import * as Application from 'expo-application';
import { AppError } from './errorHandler';
import { networkManager } from './networkManager';
import { storageManager } from './storage';

// Error log entry interface
export interface ErrorLogEntry {
  id: string;
  timestamp: string;
  error: {
    type: string;
    message: string;
    stack?: string;
    code?: string;
    statusCode?: number;
  };
  context: {
    screen?: string;
    action?: string;
    userId?: string;
    sessionId: string;
  };
  device: {
    platform: string;
    version: string;
    model?: string;
    brand?: string;
  };
  app: {
    version: string;
    buildNumber?: string;
    environment: 'development' | 'staging' | 'production';
  };
  network: {
    isConnected: boolean;
    type: string;
    quality?: string;
  };
  breadcrumbs: BreadcrumbEntry[];
  severity: 'low' | 'medium' | 'high' | 'critical';
  tags: string[];
}

// Breadcrumb entry interface
export interface BreadcrumbEntry {
  timestamp: string;
  category: 'navigation' | 'user' | 'network' | 'state' | 'system';
  message: string;
  level: 'info' | 'warning' | 'error';
  data?: Record<string, any>;
}

// User feedback interface
export interface UserFeedback {
  id: string;
  errorId: string;
  timestamp: string;
  email?: string;
  description: string;
  rating: 1 | 2 | 3 | 4 | 5;
  reproduced: boolean;
  additionalInfo?: string;
}

class ErrorLogger {
  private static instance: ErrorLogger;
  private sessionId: string;
  private breadcrumbs: BreadcrumbEntry[] = [];
  private maxBreadcrumbs: number = 50;
  private maxStoredLogs: number = 100;
  private userId?: string;

  private constructor() {
    this.sessionId = this.generateSessionId();
    this.initializeLogger();
  }

  public static getInstance(): ErrorLogger {
    if (!ErrorLogger.instance) {
      ErrorLogger.instance = new ErrorLogger();
    }
    return ErrorLogger.instance;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async initializeLogger(): Promise<void> {
    try {
      // Load existing breadcrumbs from storage
      const savedBreadcrumbs = await storageManager.retrieveData<BreadcrumbEntry[]>('error_breadcrumbs');
      if (savedBreadcrumbs && Array.isArray(savedBreadcrumbs)) {
        this.breadcrumbs = savedBreadcrumbs.slice(-this.maxBreadcrumbs);
      }

      // Add session start breadcrumb
      this.addBreadcrumb('system', 'Session started', 'info');

      // Set up global error handlers
      this.setupGlobalErrorHandlers();
    } catch (error) {
      console.error('Failed to initialize error logger:', error);
    }
  }

  private setupGlobalErrorHandlers(): void {
    // Handle unhandled promise rejections
    if (typeof window !== 'undefined') {
      window.addEventListener('unhandledrejection', (event) => {
        this.logError(
          new AppError('UNHANDLED_PROMISE_REJECTION', event.reason?.message || 'Unhandled promise rejection'),
          'Global.unhandledRejection',
          'critical'
        );
      });
    }

    // Handle React Native errors
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      const originalConsoleError = console.error;
      console.error = (...args) => {
        // Check if this is a React Native error
        const errorMessage = args.join(' ');
        if (errorMessage.includes('Warning:') || errorMessage.includes('Error:')) {
          this.addBreadcrumb('system', `Console error: ${errorMessage}`, 'error');
        }
        originalConsoleError.apply(console, args);
      };
    }
  }

  public setUserId(userId: string): void {
    this.userId = userId;
    this.addBreadcrumb('user', `User logged in: ${userId}`, 'info');
  }

  public clearUserId(): void {
    if (this.userId) {
      this.addBreadcrumb('user', `User logged out: ${this.userId}`, 'info');
    }
    this.userId = undefined;
  }

  public addBreadcrumb(
    category: BreadcrumbEntry['category'],
    message: string,
    level: BreadcrumbEntry['level'] = 'info',
    data?: Record<string, any>
  ): void {
    const breadcrumb: BreadcrumbEntry = {
      timestamp: new Date().toISOString(),
      category,
      message,
      level,
      data,
    };

    this.breadcrumbs.push(breadcrumb);

    // Keep only the most recent breadcrumbs
    if (this.breadcrumbs.length > this.maxBreadcrumbs) {
      this.breadcrumbs = this.breadcrumbs.slice(-this.maxBreadcrumbs);
    }

    // Save to storage periodically
    this.saveBreadcrumbsToStorage();
  }

  public async logError(
    error: Error | AppError,
    context?: string,
    severity: ErrorLogEntry['severity'] = 'medium',
    tags: string[] = []
  ): Promise<string> {
    try {
      const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Get device info
      const deviceInfo = await this.getDeviceInfo();
      
      // Get app info
      const appInfo = await this.getAppInfo();
      
      // Get network info
      const networkInfo = networkManager.getNetworkStatus();

      // Create error log entry
      const logEntry: ErrorLogEntry = {
        id: errorId,
        timestamp: new Date().toISOString(),
        error: {
          type: error.name || 'Error',
          message: error.message,
          stack: error.stack,
          code: (error as AppError).code,
          statusCode: (error as AppError).statusCode,
        },
        context: {
          screen: this.extractScreenFromContext(context),
          action: this.extractActionFromContext(context),
          userId: this.userId,
          sessionId: this.sessionId,
        },
        device: deviceInfo,
        app: appInfo,
        network: {
          isConnected: networkInfo.isConnected,
          type: networkInfo.type,
          quality: networkInfo.details?.connectionQuality,
        },
        breadcrumbs: [...this.breadcrumbs],
        severity,
        tags: [...tags, this.getAutoTags(error)].filter(Boolean),
      };

      // Store error log
      await this.storeErrorLog(logEntry);

      // Add error breadcrumb
      this.addBreadcrumb('system', `Error logged: ${error.message}`, 'error', {
        errorId,
        errorType: error.name,
      });

      // Send to remote logging service if connected
      if (networkInfo.isConnected) {
        this.sendToRemoteLogger(logEntry);
      }

      return errorId;
    } catch (loggingError) {
      console.error('Failed to log error:', loggingError);
      return 'logging_failed';
    }
  }

  private async getDeviceInfo(): Promise<ErrorLogEntry['device']> {
    return {
      platform: Platform.OS,
      version: Platform.Version.toString(),
      model: 'Unknown', // Device.modelName || undefined,
      brand: 'Unknown', // Device.brand || undefined,
    };
  }

  private async getAppInfo(): Promise<ErrorLogEntry['app']> {
    return {
      version: Application.nativeApplicationVersion || '1.0.0',
      buildNumber: Application.nativeBuildVersion || undefined,
      environment: __DEV__ ? 'development' : 'production',
    };
  }

  private extractScreenFromContext(context?: string): string | undefined {
    if (!context) return undefined;
    const screenMatch = context.match(/(\w+Screen|\w+Page|\w+Component)/);
    return screenMatch ? screenMatch[1] : undefined;
  }

  private extractActionFromContext(context?: string): string | undefined {
    if (!context) return undefined;
    const actionMatch = context.match(/\.(\w+)$/);
    return actionMatch ? actionMatch[1] : undefined;
  }

  private getAutoTags(error: Error | AppError): string {
    if (error instanceof AppError) {
      return error.type.toLowerCase();
    }
    return error.name.toLowerCase();
  }

  private async storeErrorLog(logEntry: ErrorLogEntry): Promise<void> {
    try {
      // Get existing logs
      const existingLogs = await storageManager.retrieveData<ErrorLogEntry[]>('error_logs') || [];
      
      // Add new log
      existingLogs.push(logEntry);
      
      // Keep only the most recent logs
      const recentLogs = existingLogs.slice(-this.maxStoredLogs);
      
      // Save back to storage
      await storageManager.storeData('error_logs', recentLogs);
    } catch (error) {
      console.error('Failed to store error log:', error);
    }
  }

  private async saveBreadcrumbsToStorage(): Promise<void> {
    try {
      await storageManager.storeData('error_breadcrumbs', this.breadcrumbs);
    } catch (error) {
      console.error('Failed to save breadcrumbs:', error);
    }
  }

  private async sendToRemoteLogger(logEntry: ErrorLogEntry): Promise<void> {
    try {
      // Queue the request for sending when network is available
      await networkManager.queueRequest(
        '/api/errors/log',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          data: logEntry,
        },
        'normal'
      );
    } catch (error) {
      console.error('Failed to queue error log for remote sending:', error);
    }
  }

  // Public methods for retrieving logs
  public async getErrorLogs(): Promise<ErrorLogEntry[]> {
    try {
      return await storageManager.retrieveData<ErrorLogEntry[]>('error_logs') || [];
    } catch (error) {
      console.error('Failed to retrieve error logs:', error);
      return [];
    }
  }

  public async getErrorLog(errorId: string): Promise<ErrorLogEntry | null> {
    try {
      const logs = await this.getErrorLogs();
      return logs.find(log => log.id === errorId) || null;
    } catch (error) {
      console.error('Failed to retrieve error log:', error);
      return null;
    }
  }

  public async clearErrorLogs(): Promise<void> {
    try {
      await storageManager.removeData('error_logs');
      await storageManager.removeData('error_breadcrumbs');
      this.breadcrumbs = [];
    } catch (error) {
      console.error('Failed to clear error logs:', error);
    }
  }

  // User feedback methods
  public async submitUserFeedback(
    errorId: string,
    feedback: Omit<UserFeedback, 'id' | 'errorId' | 'timestamp'>
  ): Promise<string> {
    try {
      const feedbackId = `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const userFeedback: UserFeedback = {
        id: feedbackId,
        errorId,
        timestamp: new Date().toISOString(),
        ...feedback,
      };

      // Store feedback locally
      const existingFeedback = await storageManager.retrieveData<UserFeedback[]>('user_feedback') || [];
      existingFeedback.push(userFeedback);
      await storageManager.storeData('user_feedback', existingFeedback);

      // Send to remote service
      const networkStatus = networkManager.getNetworkStatus();
      if (networkStatus.isConnected) {
        await networkManager.queueRequest(
          '/api/errors/feedback',
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            data: userFeedback,
          },
          'high'
        );
      }

      return feedbackId;
    } catch (error) {
      console.error('Failed to submit user feedback:', error);
      throw error;
    }
  }

  public getBreadcrumbs(): BreadcrumbEntry[] {
    return [...this.breadcrumbs];
  }
}

// Create singleton instance
export const errorLogger = ErrorLogger.getInstance();

// Convenience functions
export const logError = (error: Error | AppError, context?: string, severity?: ErrorLogEntry['severity'], tags?: string[]) =>
  errorLogger.logError(error, context, severity, tags);

export const addBreadcrumb = (category: BreadcrumbEntry['category'], message: string, level?: BreadcrumbEntry['level'], data?: Record<string, any>) =>
  errorLogger.addBreadcrumb(category, message, level, data);

export const setUserId = (userId: string) => errorLogger.setUserId(userId);
export const clearUserId = () => errorLogger.clearUserId();
export const getErrorLogs = () => errorLogger.getErrorLogs();
export const submitUserFeedback = (errorId: string, feedback: Omit<UserFeedback, 'id' | 'errorId' | 'timestamp'>) =>
  errorLogger.submitUserFeedback(errorId, feedback);

export default errorLogger;
