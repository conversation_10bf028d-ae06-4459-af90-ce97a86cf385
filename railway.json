{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "NIXPACKS", "buildCommand": "npm run build", "watchPatterns": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "package.json", "package-lock.json"]}, "deploy": {"startCommand": "npm start", "healthcheckPath": "/health", "healthcheckTimeout": 300, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 3}, "environments": {"production": {"variables": {"NODE_ENV": "production", "EXPO_PUBLIC_API_URL": "${{RAILWAY_PUBLIC_DOMAIN}}/api", "DATABASE_URL": "${{DATABASE_URL}}", "REDIS_URL": "${{REDIS_URL}}", "JWT_SECRET": "${{JWT_SECRET}}", "STRIPE_SECRET_KEY": "${{STRIPE_SECRET_KEY}}", "STRIPE_WEBHOOK_SECRET": "${{STRIPE_WEBHOOK_SECRET}}", "GOOGLE_MAPS_API_KEY": "${{GOOGLE_MAPS_API_KEY}}", "FIREBASE_SERVER_KEY": "${{FIREBASE_SERVER_KEY}}", "EMAIL_SERVICE_API_KEY": "${{EMAIL_SERVICE_API_KEY}}", "CLOUDINARY_CLOUD_NAME": "${{CLOUDINARY_CLOUD_NAME}}", "CLOUDINARY_API_KEY": "${{CLOUDINARY_API_KEY}}", "CLOUDINARY_API_SECRET": "${{CLOUDINARY_API_SECRET}}"}}, "staging": {"variables": {"NODE_ENV": "staging", "EXPO_PUBLIC_API_URL": "${{RAILWAY_PUBLIC_DOMAIN}}/api", "DATABASE_URL": "${{DATABASE_URL}}", "REDIS_URL": "${{REDIS_URL}}", "JWT_SECRET": "${{JWT_SECRET}}", "STRIPE_SECRET_KEY": "${{STRIPE_SECRET_KEY}}", "GOOGLE_MAPS_API_KEY": "${{GOOGLE_MAPS_API_KEY}}"}}}, "services": [{"name": "foodway-customer-app", "source": {"type": "repo", "repo": "your-repo/customer_app"}, "variables": {"PORT": "3000"}}, {"name": "postgres", "source": {"type": "image", "image": "postgres:15-alpine"}, "variables": {"POSTGRES_DB": "foodway_db", "POSTGRES_USER": "foodway_user", "POSTGRES_PASSWORD": "${{POSTGRES_PASSWORD}}"}, "volumes": [{"name": "postgres_data", "mountPath": "/var/lib/postgresql/data"}]}, {"name": "redis", "source": {"type": "image", "image": "redis:7-alpine"}, "variables": {"REDIS_PASSWORD": "${{REDIS_PASSWORD}}"}, "volumes": [{"name": "redis_data", "mountPath": "/data"}]}]}