# Railway Environment Variables Template
# Copy this file to .env.local for local development

# Application Configuration
NODE_ENV=development
PORT=3000
EXPO_PUBLIC_API_URL=http://localhost:3000/api

# Database Configuration (Railway PostgreSQL)
DATABASE_URL=postgresql://username:password@host:port/database
POSTGRES_DB=foodway_db
POSTGRES_USER=foodway_user
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# Redis Configuration (Railway Redis)
REDIS_URL=redis://username:password@host:port
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# Authentication
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_SECRET=your_refresh_token_secret
REFRESH_TOKEN_EXPIRES_IN=30d

# Stripe Payment Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Google Maps API
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# Firebase Configuration (for push notifications)
FIREBASE_SERVER_KEY=your_firebase_server_key
FIREBASE_PROJECT_ID=your_firebase_project_id

# Email Service (SendGrid, Mailgun, etc.)
EMAIL_SERVICE_API_KEY=your_email_service_api_key
EMAIL_FROM=<EMAIL>

# File Upload (Cloudinary)
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# SMS Service (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# App Configuration
APP_NAME=FoodWay
APP_VERSION=1.0.0
SUPPORT_EMAIL=<EMAIL>

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FORMAT=combined

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:19006
CORS_CREDENTIALS=true

# Session Configuration
SESSION_SECRET=your_session_secret
SESSION_MAX_AGE=********

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_KEYS=1000

# File Upload Limits
MAX_FILE_SIZE=********
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif

# Geolocation
DEFAULT_LATITUDE=37.7749
DEFAULT_LONGITUDE=-122.4194
SEARCH_RADIUS_KM=10

# Feature Flags
ENABLE_SOCIAL_LOGIN=true
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_SMS_NOTIFICATIONS=false
ENABLE_LOYALTY_PROGRAM=true
ENABLE_REVIEWS=true
ENABLE_CHAT=true
