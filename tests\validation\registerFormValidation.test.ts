import { VALIDATION } from '../../utils/constants';

// Mock form data type
interface RegisterForm {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
}

// Simulate the validation logic from the register form
const validateForm = (formData: RegisterForm): { isValid: boolean; errors: Partial<RegisterForm> } => {
  const newErrors: Partial<RegisterForm> = {};

  if (!formData.firstName.trim()) {
    newErrors.firstName = 'First name is required';
  } else if (formData.firstName.length < VALIDATION.NAME_MIN_LENGTH) {
    newErrors.firstName = `First name must be at least ${VALIDATION.NAME_MIN_LENGTH} characters`;
  }

  if (!formData.lastName.trim()) {
    newErrors.lastName = 'Last name is required';
  } else if (formData.lastName.length < VALIDATION.NAME_MIN_LENGTH) {
    newErrors.lastName = `Last name must be at least ${VALIDATION.NAME_MIN_LENGTH} characters`;
  }

  if (!formData.email) {
    newErrors.email = 'Email is required';
  } else if (!VALIDATION.EMAIL_REGEX.test(formData.email)) {
    newErrors.email = 'Please enter a valid email';
  }

  if (!formData.phone) {
    newErrors.phone = 'Phone number is required';
  } else if (!VALIDATION.PHONE_REGEX.test(formData.phone)) {
    newErrors.phone = 'Please enter a valid phone number';
  }

  if (!formData.password) {
    newErrors.password = 'Password is required';
  } else if (formData.password.length < VALIDATION.PASSWORD_MIN_LENGTH) {
    newErrors.password = `Password must be at least ${VALIDATION.PASSWORD_MIN_LENGTH} characters`;
  }

  if (!formData.confirmPassword) {
    newErrors.confirmPassword = 'Please confirm your password';
  } else if (formData.password !== formData.confirmPassword) {
    newErrors.confirmPassword = 'Passwords do not match';
  }

  return {
    isValid: Object.keys(newErrors).length === 0,
    errors: newErrors
  };
};

// Simulate the field validation logic from handleInputChange
const shouldClearFieldError = (field: keyof RegisterForm, value: string, formData: RegisterForm): boolean => {
  if (!value.trim()) return false;

  switch (field) {
    case 'firstName':
    case 'lastName':
      return value.trim().length >= VALIDATION.NAME_MIN_LENGTH;
    case 'email':
      return VALIDATION.EMAIL_REGEX.test(value);
    case 'phone':
      return VALIDATION.PHONE_REGEX.test(value);
    case 'password':
      return value.length >= VALIDATION.PASSWORD_MIN_LENGTH;
    case 'confirmPassword':
      return value === formData.password && value.length > 0;
    default:
      return value.trim().length > 0;
  }
};

describe('Register Form Validation', () => {
  const emptyForm: RegisterForm = {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
  };

  const validForm: RegisterForm = {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+1234567890',
    password: 'password123',
    confirmPassword: 'password123',
  };

  describe('Full form validation', () => {
    it('should fail validation for empty form', () => {
      const result = validateForm(emptyForm);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.firstName).toBe('First name is required');
      expect(result.errors.lastName).toBe('Last name is required');
      expect(result.errors.email).toBe('Email is required');
      expect(result.errors.phone).toBe('Phone number is required');
      expect(result.errors.password).toBe('Password is required');
      expect(result.errors.confirmPassword).toBe('Please confirm your password');
    });

    it('should pass validation for valid form', () => {
      const result = validateForm(validForm);
      
      expect(result.isValid).toBe(true);
      expect(Object.keys(result.errors)).toHaveLength(0);
    });

    it('should validate password confirmation', () => {
      const formWithMismatchedPasswords = {
        ...validForm,
        confirmPassword: 'different123'
      };
      
      const result = validateForm(formWithMismatchedPasswords);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.confirmPassword).toBe('Passwords do not match');
    });
  });

  describe('Field-level validation for error clearing', () => {
    it('should clear firstName error when valid name is entered', () => {
      expect(shouldClearFieldError('firstName', 'John', emptyForm)).toBe(true);
      expect(shouldClearFieldError('firstName', 'J', emptyForm)).toBe(false);
      expect(shouldClearFieldError('firstName', '', emptyForm)).toBe(false);
    });

    it('should clear email error when valid email is entered', () => {
      expect(shouldClearFieldError('email', '<EMAIL>', emptyForm)).toBe(true);
      expect(shouldClearFieldError('email', 'invalid-email', emptyForm)).toBe(false);
      expect(shouldClearFieldError('email', '', emptyForm)).toBe(false);
    });

    it('should clear password error when valid password is entered', () => {
      expect(shouldClearFieldError('password', 'password123', emptyForm)).toBe(true);
      expect(shouldClearFieldError('password', 'short', emptyForm)).toBe(false);
      expect(shouldClearFieldError('password', '', emptyForm)).toBe(false);
    });

    it('should clear confirmPassword error when passwords match', () => {
      const formWithPassword = { ...emptyForm, password: 'password123' };
      
      expect(shouldClearFieldError('confirmPassword', 'password123', formWithPassword)).toBe(true);
      expect(shouldClearFieldError('confirmPassword', 'different', formWithPassword)).toBe(false);
      expect(shouldClearFieldError('confirmPassword', '', formWithPassword)).toBe(false);
    });
  });

  describe('Partial form validation scenarios', () => {
    it('should show errors for remaining empty fields when one field is filled', () => {
      const partialForm = {
        ...emptyForm,
        firstName: 'John'
      };
      
      const result = validateForm(partialForm);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.firstName).toBeUndefined(); // Should not have error
      expect(result.errors.lastName).toBe('Last name is required');
      expect(result.errors.email).toBe('Email is required');
      expect(result.errors.phone).toBe('Phone number is required');
      expect(result.errors.password).toBe('Password is required');
      expect(result.errors.confirmPassword).toBe('Please confirm your password');
    });
  });
});
