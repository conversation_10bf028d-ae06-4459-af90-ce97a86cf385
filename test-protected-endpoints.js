// Test All Protected Endpoints with Real User Token
const axios = require('axios');

const BASE_URL = 'https://backend-production-f106.up.railway.app/api/v1';

let authToken = null;
let refreshToken = null;

async function authenticate() {
  console.log('🔐 Authenticating...');
  
  const credentials = {
    email: '<EMAIL>',
    password: '/Abd/0321'
  };
  
  try {
    const response = await axios.post(`${BASE_URL}/auth/login`, credentials, {
      timeout: 15000,
      headers: { 'Content-Type': 'application/json' }
    });
    
    authToken = response.data.data.token;
    refreshToken = response.data.data.refreshToken;
    
    console.log('✅ Authentication successful');
    return true;
  } catch (error) {
    console.log('❌ Authentication failed:', error.message);
    return false;
  }
}

async function testEndpoint(method, endpoint, data = null, description = '') {
  console.log(`\n🔄 ${method} ${endpoint}`);
  if (description) console.log(`   ${description}`);
  
  const config = {
    method: method.toLowerCase(),
    url: `${BASE_URL}${endpoint}`,
    headers: {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json'
    },
    timeout: 15000
  };
  
  if (data) config.data = data;
  
  try {
    const response = await axios.request(config);
    console.log(`✅ SUCCESS: ${response.status}`);
    
    // Show useful response info
    if (response.data?.data) {
      const responseData = response.data.data;
      if (Array.isArray(responseData)) {
        console.log(`   📊 Items: ${responseData.length}`);
      } else if (typeof responseData === 'object') {
        console.log(`   📋 Keys: ${Object.keys(responseData).slice(0, 5).join(', ')}`);
      }
    }
    
    return { success: true, status: response.status, data: response.data };
  } catch (error) {
    console.log(`❌ FAILED: ${error.response?.status || 'ERROR'}`);
    const errorMsg = error.response?.data?.error?.message || error.message;
    console.log(`   Error: ${errorMsg}`);
    return { success: false, status: error.response?.status || 0, error: errorMsg };
  }
}

async function runAllTests() {
  console.log('\n🛡️ TESTING ALL PROTECTED ENDPOINTS');
  console.log('=' * 50);
  
  const results = [];
  
  // USER MANAGEMENT
  console.log('\n📂 USER MANAGEMENT ENDPOINTS:');
  
  results.push(await testEndpoint('GET', '/user/profile', null, 'Get user profile'));
  
  results.push(await testEndpoint('PUT', '/user/profile', {
    firstName: 'PowerGamer',
    lastName: 'Updated'
  }, 'Update profile'));
  
  results.push(await testEndpoint('GET', '/user/addresses', null, 'Get addresses'));
  
  results.push(await testEndpoint('POST', '/user/addresses', {
    street: '123 Gaming Street',
    city: 'Test City',
    state: 'TC',
    zipCode: '12345',
    country: 'US',
    isDefault: true,
    label: 'Home'
  }, 'Add address'));
  
  results.push(await testEndpoint('GET', '/user/payment-methods', null, 'Get payment methods'));
  
  results.push(await testEndpoint('GET', '/user/favorites', null, 'Get favorites'));
  
  results.push(await testEndpoint('GET', '/user/reviews', null, 'Get user reviews'));
  
  results.push(await testEndpoint('GET', '/user/notification-settings', null, 'Get notification settings'));
  
  // ORDERS
  console.log('\n📂 ORDER ENDPOINTS:');
  
  results.push(await testEndpoint('GET', '/orders', null, 'Get user orders'));
  
  // Get restaurant for order test
  try {
    const restaurantsResponse = await axios.get(`${BASE_URL}/restaurants`, { timeout: 10000 });
    const restaurants = restaurantsResponse.data.data.restaurants;
    
    if (restaurants.length > 0) {
      const restaurant = restaurants[0];
      console.log(`   Using restaurant: ${restaurant.name}`);
      
      results.push(await testEndpoint('POST', '/orders', {
        restaurantId: restaurant.id,
        items: [{
          menuItemId: 'sample-item-id',
          quantity: 2,
          price: 25.99,
          specialInstructions: 'Extra spicy'
        }],
        deliveryAddress: {
          street: '123 Gaming Street',
          city: 'Test City',
          state: 'TC',
          zipCode: '12345',
          country: 'US'
        },
        paymentMethodId: 'test-payment-method'
      }, 'Create order'));
    }
  } catch (error) {
    console.log('   ⚠️ Could not fetch restaurants for order test');
  }
  
  // REVIEWS
  console.log('\n📂 REVIEW ENDPOINTS:');
  
  try {
    const restaurantsResponse = await axios.get(`${BASE_URL}/restaurants`, { timeout: 10000 });
    const restaurants = restaurantsResponse.data.data.restaurants;
    
    if (restaurants.length > 0) {
      const restaurant = restaurants[0];
      
      results.push(await testEndpoint('POST', '/reviews', {
        restaurantId: restaurant.id,
        rating: 5,
        comment: 'Amazing food! Highly recommend.'
      }, 'Create review'));
    }
  } catch (error) {
    console.log('   ⚠️ Could not fetch restaurants for review test');
  }
  
  // NOTIFICATIONS
  console.log('\n📂 NOTIFICATION ENDPOINTS:');
  
  results.push(await testEndpoint('GET', '/notifications', null, 'Get notifications'));
  
  results.push(await testEndpoint('PATCH', '/notifications/read-all', null, 'Mark all as read'));
  
  // PROMOTIONS
  console.log('\n📂 PROMOTION ENDPOINTS:');
  
  results.push(await testEndpoint('POST', '/promotions/validate', {
    code: 'WELCOME10'
  }, 'Validate promo code'));
  
  // AUTH ENDPOINTS
  console.log('\n📂 AUTH ENDPOINTS:');
  
  results.push(await testEndpoint('POST', '/auth/refresh', {
    refreshToken: refreshToken
  }, 'Refresh token'));
  
  results.push(await testEndpoint('POST', '/auth/logout', null, 'Logout'));
  
  return results;
}

function generateReport(results) {
  console.log('\n' + '=' * 60);
  console.log('📊 PROTECTED ENDPOINTS TEST REPORT');
  console.log('=' * 60);
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`\nTotal Tests: ${results.length}`);
  console.log(`✅ Successful: ${successful.length}`);
  console.log(`❌ Failed: ${failed.length}`);
  console.log(`📊 Success Rate: ${((successful.length / results.length) * 100).toFixed(1)}%`);
  
  if (successful.length > 0) {
    console.log(`\n✅ WORKING ENDPOINTS:`);
    successful.forEach((result, index) => {
      console.log(`   ${index + 1}. Status ${result.status} - Success`);
    });
  }
  
  if (failed.length > 0) {
    console.log(`\n❌ FAILED ENDPOINTS:`);
    failed.forEach((result, index) => {
      console.log(`   ${index + 1}. Status ${result.status} - ${result.error}`);
    });
  }
  
  console.log(`\n🎯 ASSESSMENT:`);
  if (successful.length === results.length) {
    console.log('🟢 ALL PROTECTED ENDPOINTS WORKING PERFECTLY!');
  } else if (successful.length >= results.length * 0.8) {
    console.log('🟡 MOST ENDPOINTS WORKING - Minor issues to address');
  } else if (successful.length >= results.length * 0.5) {
    console.log('🟠 PARTIAL SUCCESS - Several endpoints need attention');
  } else {
    console.log('🔴 MAJOR ISSUES - Most endpoints failing');
  }
}

async function main() {
  console.log('🚀 COMPREHENSIVE PROTECTED ENDPOINT TESTING');
  console.log('Using real user: <EMAIL>');
  console.log('=' * 60);
  
  const authSuccess = await authenticate();
  if (!authSuccess) {
    console.log('🔴 Cannot proceed without authentication');
    return;
  }
  
  const results = await runAllTests();
  generateReport(results);
  
  console.log('\n✨ Testing completed!');
}

main().catch(console.error);
