<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="256" cy="256" r="240" fill="#FF6B35"/>
  
  <!-- Inner Circle -->
  <circle cx="256" cy="256" r="200" fill="#FF4500"/>
  
  <!-- Food Bowl/Plate -->
  <ellipse cx="256" cy="320" rx="120" ry="20" fill="#FFE4B5" opacity="0.9"/>
  <ellipse cx="256" cy="315" rx="100" ry="15" fill="#FFF8DC"/>
  
  <!-- Fork -->
  <g transform="translate(180, 150)">
    <rect x="0" y="0" width="8" height="100" fill="#FFFFFF" rx="3"/>
    <rect x="15" y="0" width="8" height="85" fill="#FFFFFF" rx="3"/>
    <rect x="30" y="0" width="8" height="100" fill="#FFFFFF" rx="3"/>
    <rect x="45" y="0" width="8" height="85" fill="#FFFFFF" rx="3"/>
    <rect x="0" y="85" width="53" height="10" fill="#FFFFFF" rx="5"/>
    <rect x="20" y="95" width="13" height="60" fill="#FFFFFF" rx="6"/>
  </g>
  
  <!-- Spoon -->
  <g transform="translate(280, 150)">
    <ellipse cx="25" cy="20" rx="25" ry="20" fill="#FFFFFF"/>
    <rect x="20" y="40" width="10" height="60" fill="#FFFFFF" rx="5"/>
  </g>
  
  <!-- Food Items -->
  <!-- Burger Stack -->
  <g transform="translate(220, 240)">
    <ellipse cx="36" cy="20" rx="30" ry="10" fill="#8B4513"/>
    <ellipse cx="36" cy="30" rx="25" ry="8" fill="#228B22"/>
    <ellipse cx="36" cy="38" rx="28" ry="8" fill="#FF6347"/>
    <ellipse cx="36" cy="46" rx="30" ry="10" fill="#DEB887"/>
  </g>
  
  <!-- Letter F -->
  <g transform="translate(200, 380)">
    <text x="56" y="60" font-family="Arial, sans-serif" font-size="72" font-weight="bold" text-anchor="middle" fill="white">
      F
    </text>
  </g>
  
  <!-- Decorative Stars -->
  <g fill="#FFD700" opacity="0.8">
    <polygon points="100,120 105,130 115,130 107,137 110,147 100,140 90,147 93,137 85,130 95,130" />
    <polygon points="400,140 405,150 415,150 407,157 410,167 400,160 390,167 393,157 385,150 395,150" />
    <polygon points="120,380 125,390 135,390 127,397 130,407 120,400 110,407 113,397 105,390 115,390" />
    <polygon points="380,360 385,370 395,370 387,377 390,387 380,380 370,387 373,377 365,370 375,370" />
  </g>
</svg>
