import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
  Animated,
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Order, OrderStatus } from '../../types';
import { BORDER_RADIUS, COLORS, ORDER_STATUS_COLORS, SPACING, TYPOGRAPHY } from '../../utils/constants';

// Using centralized mock data service


const orderTabs = [
  { id: 'active', label: 'Active' },
  { id: 'past', label: 'Past Orders' },
];

export default function OrdersScreen() {
  const [activeTab, setActiveTab] = useState('active');
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuthStore();

  // Modern animations
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const tabAnim = useRef(new Animated.Value(0)).current;

  // Load orders from mock API
  const loadOrders = async () => {
    if (!user) return;

    try {
      setLoading(true);

      // Import mock API service dynamically
      const { mockApiService } = await import('../../services/mock/mockApiService');

      // Load user orders
      const ordersResponse = await mockApiService.orders.getUserOrders(user.id);
      if (ordersResponse.success) {
        setOrders(ordersResponse.data || []);
      }
    } catch (error) {
      console.error('Error loading orders:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Initialize animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(tabAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
    ]).start();

    // Load orders
    loadOrders();
  }, [user]);

  const getStatusColor = (status: OrderStatus) => {
    return ORDER_STATUS_COLORS[status] || COLORS.gray[500];
  };

  const getStatusText = (status: OrderStatus) => {
    switch (status) {
      case 'pending':
        return 'Order Pending';
      case 'confirmed':
        return 'Order Confirmed';
      case 'preparing':
        return 'Being Prepared';
      case 'ready':
        return 'Ready for Pickup';
      case 'picked_up':
        return 'Picked Up';
      case 'on_the_way':
        return 'On the Way';
      case 'delivered':
        return 'Delivered';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'Unknown Status';
    }
  };

  const filteredOrders = orders.filter(order => {
    if (activeTab === 'active') {
      return !['delivered', 'cancelled'].includes(order.status);
    }
    return ['delivered', 'cancelled'].includes(order.status);
  });

  const handleOrderPress = (order: Order) => {
    if (!['delivered', 'cancelled'].includes(order.status)) {
      router.push(`/order-tracking/${order.id}`);
    } else {
      router.push(`/order/${order.id}`);
    }
  };

  const handleReorder = (order: Order) => {
    // Navigate to restaurant and add items to cart
    router.push(`/restaurant/${order.restaurantId}`);
  };

  const renderOrderItem = ({ item, index }: { item: Order; index: number }) => {
    const itemAnimatedStyle = {
      opacity: fadeAnim,
      transform: [
        {
          translateY: slideAnim.interpolate({
            inputRange: [0, 30],
            outputRange: [0, index * 10],
          }),
        },
      ],
    };

    return (
      <Animated.View style={itemAnimatedStyle}>
        <TouchableOpacity
          onPress={() => handleOrderPress(item)}
          activeOpacity={0.9}
        >
          <View style={modernStyles.orderCard}>
            <View style={modernStyles.orderHeader}>
              <View style={modernStyles.orderInfo}>
                <Text style={modernStyles.restaurantName}>{item.restaurant.name}</Text>
                <Text style={modernStyles.orderDate}>
                  {new Date(item.createdAt).toLocaleDateString()}
                </Text>
                <Text style={modernStyles.orderTotal}>${item.total.toFixed(2)}</Text>
              </View>
              <View style={[
                modernStyles.statusBadge,
                { backgroundColor: getStatusColor(item.status) }
              ]}>
                <Text style={[modernStyles.statusText, { color: COLORS.white }]}>
                  {getStatusText(item.status)}
                </Text>
              </View>
            </View>

            {/* Order Items Preview */}
            <View style={modernStyles.orderItems}>
              <Text style={modernStyles.itemText}>
                {item.items.length} item{item.items.length !== 1 ? 's' : ''}
              </Text>
            </View>

            {/* Modern Action Buttons */}
            <View style={modernStyles.orderActions}>
              <TouchableOpacity
                style={[modernStyles.actionButton, modernStyles.actionButtonBorder]}
                onPress={() => handleReorder(item)}
                activeOpacity={0.8}
              >
                <Text style={modernStyles.actionButtonText}>Reorder</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={modernStyles.actionButton}
                activeOpacity={0.8}
              >
                <Text style={modernStyles.actionButtonText}>View Details</Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="receipt-outline" size={64} color={COLORS.gray[400]} />
      <Text style={styles.emptyStateTitle}>
        {activeTab === 'active' ? 'No active orders' : 'No past orders'}
      </Text>
      <Text style={styles.emptyStateSubtitle}>
        {activeTab === 'active'
          ? 'Your active orders will appear here'
          : 'Your order history will appear here'
        }
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={modernStyles.container}>
      {/* Modern Header */}
      <Animated.View style={[modernStyles.header, {
        opacity: tabAnim,
        transform: [{ translateY: slideAnim }]
      }]}>
        <Text style={modernStyles.headerTitle}>My Orders</Text>

        {/* Modern Tabs */}
        <View style={modernStyles.tabsContainer}>
          {orderTabs.map((tab) => (
            <TouchableOpacity
              key={tab.id}
              style={[
                modernStyles.tab,
                activeTab === tab.id && modernStyles.activeTab,
              ]}
              onPress={() => setActiveTab(tab.id)}
              activeOpacity={0.8}
            >
              <Text
                style={[
                  modernStyles.tabText,
                  activeTab === tab.id && modernStyles.activeTabText,
                ]}
              >
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </Animated.View>

      {/* Modern Orders List */}
      {filteredOrders.length > 0 ? (
        <Animated.View style={{ flex: 1, opacity: fadeAnim }}>
          <FlatList
            data={filteredOrders}
            renderItem={renderOrderItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={modernStyles.ordersList}
            showsVerticalScrollIndicator={false}
          />
        </Animated.View>
      ) : (
        <Animated.View style={[modernStyles.emptyState, { opacity: fadeAnim }]}>
          <View style={modernStyles.emptyIcon}>
            <Ionicons name="receipt-outline" size={40} color={COLORS.textSecondary} />
          </View>
          <Text style={modernStyles.emptyTitle}>
            {activeTab === 'active' ? 'No Active Orders' : 'No Past Orders'}
          </Text>
          <Text style={modernStyles.emptyDescription}>
            {activeTab === 'active'
              ? 'You don\'t have any active orders right now. Start browsing restaurants to place your first order!'
              : 'You haven\'t placed any orders yet. Discover amazing restaurants and delicious food nearby.'
            }
          </Text>
        </Animated.View>
      )}
    </SafeAreaView>
  );
}

// Modern styles for orders screen
const modernStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    backgroundColor: COLORS.white,
    paddingHorizontal: SPACING.md,
    paddingTop: SPACING.md,
    paddingBottom: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 3,
  },
  headerTitle: {
    fontSize: TYPOGRAPHY.fontSize['2xl'],
    fontWeight: '700',
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: '#F3F4F6',
    borderRadius: BORDER_RADIUS.lg,
    padding: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: COLORS.white,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tabText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '600',
    color: COLORS.textSecondary,
  },
  activeTabText: {
    color: COLORS.primary,
  },
  ordersList: {
    flex: 1,
    paddingTop: SPACING.sm,
  },
  orderCard: {
    marginHorizontal: SPACING.md,
    marginBottom: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: COLORS.white,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: SPACING.md,
    paddingBottom: SPACING.sm,
  },
  orderInfo: {
    flex: 1,
  },
  restaurantName: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: '700',
    color: COLORS.textPrimary,
    marginBottom: 4,
  },
  orderDate: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  orderTotal: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  statusBadge: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.full,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 80,
  },
  statusText: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    fontWeight: '700',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  orderItems: {
    paddingHorizontal: SPACING.md,
    paddingBottom: SPACING.sm,
  },
  itemText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    marginBottom: 2,
  },
  orderActions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  actionButton: {
    flex: 1,
    paddingVertical: SPACING.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionButtonBorder: {
    borderRightWidth: 1,
    borderRightColor: '#F3F4F6',
  },
  actionButtonText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '600',
    color: COLORS.primary,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: SPACING.xl,
  },
  emptyIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.lg,
  },
  emptyTitle: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: '700',
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    backgroundColor: COLORS.white,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  headerTitle: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: COLORS.white,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  tab: {
    flex: 1,
    paddingVertical: SPACING.md,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: COLORS.primary,
  },
  tabText: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '500',
    color: COLORS.textSecondary,
  },
  activeTabText: {
    color: COLORS.primary,
    fontWeight: 'bold',
  },
  ordersList: {
    padding: SPACING.md,
  },
  orderCard: {
    marginBottom: SPACING.md,
  },
  orderHeader: {
    flexDirection: 'row',
    marginBottom: SPACING.md,
  },
  restaurantImage: {
    width: 60,
    height: 60,
    borderRadius: BORDER_RADIUS.md,
    marginRight: SPACING.md,
  },
  orderInfo: {
    flex: 1,
  },
  restaurantName: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  orderDate: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  orderTotal: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  statusContainer: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.sm,
    marginBottom: SPACING.xs,
  },
  statusText: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  trackButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    backgroundColor: COLORS.primary + '20',
    borderRadius: BORDER_RADIUS.sm,
  },
  trackButtonText: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    fontWeight: '600',
    color: COLORS.primary,
    marginLeft: SPACING.xs,
  },
  orderActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    paddingTop: SPACING.md,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
  },
  actionButtonText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '500',
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
  },
  emptyStateTitle: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
  },
  emptyStateSubtitle: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
});
