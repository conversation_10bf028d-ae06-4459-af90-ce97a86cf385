import { renderHook, act } from '@testing-library/react-native';
import { useAuthStore } from '../../store/authStore';
import { LoginForm, RegisterForm } from '../../types';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

// Mock SecureStore
jest.mock('expo-secure-store', () => ({
  getItemAsync: jest.fn(),
  setItemAsync: jest.fn(),
  deleteItemAsync: jest.fn(),
}));

// Mock fetch
global.fetch = jest.fn();

describe('AuthStore', () => {
  beforeEach(() => {
    // Reset store state
    useAuthStore.setState({
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    });
    
    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      const { result } = renderHook(() => useAuthStore());
      
      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
      expect(result.current.refreshToken).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
    });
  });

  describe('Login', () => {
    const mockLoginData: LoginForm = {
      email: '<EMAIL>',
      password: 'password123',
    };

    const mockResponse = {
      success: true,
      data: {
        user: {
          id: '1',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          isVerified: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
        token: 'mock-token',
        refreshToken: 'mock-refresh-token',
      },
    };

    it('should login successfully', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const { result } = renderHook(() => useAuthStore());

      await act(async () => {
        await result.current.login(mockLoginData);
      });

      expect(result.current.isAuthenticated).toBe(true);
      expect(result.current.user).toEqual(mockResponse.data.user);
      expect(result.current.token).toBe(mockResponse.data.token);
      expect(result.current.refreshToken).toBe(mockResponse.data.refreshToken);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
    });

    it('should handle login error', async () => {
      const errorResponse = {
        message: 'Invalid credentials',
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => errorResponse,
      });

      const { result } = renderHook(() => useAuthStore());

      await act(async () => {
        try {
          await result.current.login(mockLoginData);
        } catch (error) {
          // Expected to throw
        }
      });

      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.user).toBeNull();
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe('Invalid credentials');
    });

    it('should set loading state during login', async () => {
      let resolvePromise: (value: any) => void;
      const promise = new Promise((resolve) => {
        resolvePromise = resolve;
      });

      (global.fetch as jest.Mock).mockReturnValueOnce(promise);

      const { result } = renderHook(() => useAuthStore());

      act(() => {
        result.current.login(mockLoginData);
      });

      expect(result.current.isLoading).toBe(true);

      await act(async () => {
        resolvePromise!({
          ok: true,
          json: async () => mockResponse,
        });
        await promise;
      });

      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('Register', () => {
    const mockRegisterData: RegisterForm = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+1234567890',
      password: 'password123',
      confirmPassword: 'password123',
    };

    it('should register successfully', async () => {
      const mockResponse = {
        success: true,
        data: {
          user: {
            id: '1',
            email: '<EMAIL>',
            firstName: 'John',
            lastName: 'Doe',
            phone: '+1234567890',
            isVerified: false,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
          token: 'mock-token',
          refreshToken: 'mock-refresh-token',
        },
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const { result } = renderHook(() => useAuthStore());

      await act(async () => {
        await result.current.register(mockRegisterData);
      });

      expect(result.current.isAuthenticated).toBe(true);
      expect(result.current.user).toEqual(mockResponse.data.user);
      expect(result.current.token).toBe(mockResponse.data.token);
    });
  });

  describe('Logout', () => {
    it('should logout successfully', async () => {
      // Set initial authenticated state
      useAuthStore.setState({
        user: { id: '1', email: '<EMAIL>' } as any,
        token: 'mock-token',
        refreshToken: 'mock-refresh-token',
        isAuthenticated: true,
      });

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      });

      const { result } = renderHook(() => useAuthStore());

      await act(async () => {
        await result.current.logout();
      });

      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
      expect(result.current.refreshToken).toBeNull();
    });
  });

  describe('Update Profile', () => {
    it('should update profile successfully', async () => {
      const initialUser = {
        id: '1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        isVerified: true,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      };

      useAuthStore.setState({
        user: initialUser,
        token: 'mock-token',
        isAuthenticated: true,
      });

      const updateData = { firstName: 'Jane' };
      const mockResponse = {
        success: true,
        data: { ...initialUser, ...updateData },
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const { result } = renderHook(() => useAuthStore());

      await act(async () => {
        await result.current.updateProfile(updateData);
      });

      expect(result.current.user?.firstName).toBe('Jane');
      expect(result.current.error).toBeNull();
    });
  });

  describe('Error Handling', () => {
    it('should clear error', () => {
      useAuthStore.setState({ error: 'Some error' });

      const { result } = renderHook(() => useAuthStore());

      act(() => {
        result.current.clearError();
      });

      expect(result.current.error).toBeNull();
    });

    it('should set loading state', () => {
      const { result } = renderHook(() => useAuthStore());

      act(() => {
        result.current.setLoading(true);
      });

      expect(result.current.isLoading).toBe(true);

      act(() => {
        result.current.setLoading(false);
      });

      expect(result.current.isLoading).toBe(false);
    });
  });
});

export {};
