# 🚨 Troubleshooting: "Welcome to Expo" Default Screen Error

## 📋 Overview

This document provides a comprehensive guide to fix the common issue where your React Native/Expo app shows the default "Welcome to Expo" screen instead of your custom app content.

## 🔍 Root Cause Analysis

The default Expo screen appears when:
1. **Compilation errors** prevent your custom code from loading
2. **Missing dependencies** cause the app to fall back to defaults
3. **Import/export issues** in the main entry point
4. **TypeScript errors** block the build process
5. **Circular dependencies** create initialization problems

## 🛠️ Step-by-Step Fix Process

### Step 1: Identify Compilation Errors

```bash
# Check for TypeScript errors
npx tsc --noEmit

# Skip library checks to focus on your code
npx tsc --noEmit --skipLibCheck

# Check for ESLint issues
npm run lint
```

**Look for:**
- Missing imports/exports
- Type mismatches
- Undefined variables
- Module not found errors

### Step 2: Check Terminal Output

Start your development server and carefully read the output:

```bash
npm start
# or
npx expo start --clear
```

**Common error patterns:**
```
❌ CommandError: "package-name" is added as a dependency but doesn't seem to be installed
❌ ReferenceError: Property 'CONSTANT_NAME' doesn't exist
❌ Route "./index.tsx" is missing the required default export
❌ Require cycle: fileA.ts -> fileB.ts -> fileA.ts
```

### Step 3: Fix Missing Dependencies

#### Check package.json vs actual imports:

```typescript
// If you see this error:
// CommandError: "@react-native-community/netinfo" is added as a dependency but doesn't seem to be installed

// Option 1: Install the missing package
npm install @react-native-community/netinfo

// Option 2: Remove from package.json if not needed
// Delete the line from package.json dependencies

// Option 3: Temporarily disable the import
// import NetInfo from '@react-native-community/netinfo'; // Disabled
```

#### Common missing packages:
```bash
# Network info
npm install @react-native-community/netinfo

# Device info
npx expo install expo-device

# Media library
npx expo install expo-media-library

# Location services
npx expo install expo-location
```

### Step 4: Fix Import/Export Issues

#### A. Check Main Entry Point (app/index.tsx)

```typescript
// ✅ Correct structure
import React from 'react';
import { View, Text } from 'react-native';

export default function HomeScreen() {
  return (
    <View>
      <Text>Your App Content</Text>
    </View>
  );
}
```

#### B. Verify All Required Imports

```typescript
// ❌ Missing imports cause "Property doesn't exist" errors
import { COLORS } from '../utils/constants';

// ✅ Import all needed constants
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../utils/constants';

// ✅ Import React hooks
import React, { useState, useEffect } from 'react';
```

### Step 5: Fix Circular Dependencies

#### Problem:
```typescript
// fileA.ts
import { functionB } from './fileB';

// fileB.ts  
import { functionA } from './fileA'; // Creates cycle
```

#### Solution:
```typescript
// Use dynamic imports to break cycles
const moduleB = await import('./fileB');
const result = moduleB.functionB();

// Or restructure to avoid the cycle
// Move shared code to a third file
```

### Step 6: Fix API Integration Issues

#### When replacing fetch with axios:

```typescript
// ❌ Wrong - mixing fetch and axios syntax
const response = await axiosInstance.post(url, {
  body: JSON.stringify(data), // fetch syntax
  headers: { 'Content-Type': 'application/json' }
});

// ✅ Correct - pure axios syntax
const response = await axiosInstance.post(url, data, {
  headers: { 'Content-Type': 'application/json' }
});
```

## 🔧 Common Fix Patterns

### Pattern 1: Missing Constants

```typescript
// Error: Property 'BORDER_RADIUS' doesn't exist
// Fix: Add to imports
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../utils/constants';
```

### Pattern 2: Missing React Hooks

```typescript
// Error: Property 'useState' doesn't exist
// Fix: Import React hooks
import React, { useState, useEffect, useCallback } from 'react';
```

### Pattern 3: Axios Integration

```typescript
// Replace all fetch calls:
// Before
const response = await fetch(url, {
  method: 'POST',
  body: JSON.stringify(data),
  headers: { 'Content-Type': 'application/json' }
});

// After
const response = await axiosInstance.post(url, data);
```

### Pattern 4: Type Errors

```typescript
// Error: Argument of type 'string | undefined' is not assignable to type 'string'
// Fix: Add type guards
const error = validator.validateField(field, value, rule);
setErrors(prev => ({
  ...prev,
  [field]: error || '', // Use empty string instead of undefined
}));
```

## 🚀 Testing and Verification

### 1. Clear Cache and Restart
```bash
# Clear Metro cache
npx expo start --clear

# Clear npm cache if needed
npm cache clean --force

# Restart development server
npm start
```

### 2. Test Different Platforms
```bash
# Test web version (easier debugging)
npm run web
# or press 'w' in terminal

# Test mobile
# Scan QR code with Expo Go
```

### 3. Check Browser Console
- Open browser dev tools (F12)
- Look for JavaScript errors
- Check network requests

## 🎯 Prevention Strategies

### 1. Set Up Proper Development Environment
```json
// tsconfig.json
{
  "extends": "expo/tsconfig.base",
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true
  }
}
```

### 2. Use ESLint Configuration
```json
// .eslintrc.js
module.exports = {
  extends: ['expo', '@react-native-community'],
  rules: {
    'import/no-unresolved': 'error',
    'import/no-cycle': 'error'
  }
};
```

### 3. Regular Dependency Audits
```bash
# Check for outdated packages
npm outdated

# Check for security issues
npm audit

# Update dependencies
npm update
```

## 📋 Quick Checklist

When you see the default Expo screen:

- [ ] Run `npx tsc --noEmit` to check for TypeScript errors
- [ ] Check terminal output for error messages
- [ ] Verify all imports in main entry point (app/index.tsx)
- [ ] Check package.json vs actual imports
- [ ] Look for circular dependency warnings
- [ ] Clear cache and restart development server
- [ ] Test web version first (easier debugging)
- [ ] Check browser console for JavaScript errors

## 🆘 Emergency Quick Fix

If you need a quick temporary fix:

```typescript
// Create a minimal app/index.tsx
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

export default function App() {
  return (
    <View style={styles.container}>
      <Text style={styles.text}>Your App is Working!</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    fontSize: 20,
    fontWeight: 'bold',
  },
});
```

This will at least show your custom content instead of the default Expo screen.

## 📞 Additional Resources

- [Expo Troubleshooting Guide](https://docs.expo.dev/troubleshooting/)
- [React Native Debugging](https://reactnative.dev/docs/debugging)
- [TypeScript Error Reference](https://www.typescriptlang.org/docs/handbook/error-reference.html)

## 🔬 Advanced Debugging Techniques

### Metro Bundler Analysis

```bash
# Start with verbose logging
npx expo start --clear --verbose

# Check Metro config
cat metro.config.js

# Reset Metro cache completely
npx expo start --clear --reset-cache
```

### Dependency Tree Analysis

```bash
# Check for duplicate dependencies
npm ls --depth=0

# Find conflicting versions
npm ls package-name

# Check peer dependencies
npm ls --peer
```

### Platform-Specific Issues

```typescript
// Platform-specific imports
import { Platform } from 'react-native';

if (Platform.OS === 'web') {
  // Web-specific code
} else {
  // Mobile-specific code
}
```

### Bundle Analysis

```bash
# Analyze bundle size and dependencies
npx expo export --platform web
npx expo export --platform android
```

## 🐛 Common Error Scenarios & Solutions

### Scenario 1: "Cannot resolve module" Errors

```bash
# Error: Cannot resolve module '@/components/Button'

# Solution 1: Check tsconfig.json paths
{
  "compilerOptions": {
    "paths": {
      "@/*": ["./*"]
    }
  }
}

# Solution 2: Use relative imports temporarily
import { Button } from '../components/Button';
```

### Scenario 2: "Require cycle" Warnings

```typescript
// Problem: store/authStore.ts ↔ services/api.ts

// Solution: Break the cycle with dependency injection
// api.ts
export const createApiClient = (getToken: () => string) => {
  // Use getToken function instead of direct import
};

// authStore.ts
const apiClient = createApiClient(() => useAuthStore.getState().token);
```

### Scenario 3: "Property doesn't exist" on Constants

```typescript
// Error: Property 'PRIMARY_COLOR' doesn't exist

// Check constants file export
// utils/constants.ts
export const COLORS = {
  PRIMARY_COLOR: '#FF6B35', // Make sure it's exported
};

// Check import statement
import { COLORS } from '../utils/constants'; // Correct path?
```

### Scenario 4: React Native vs Web Compatibility

```typescript
// Some packages don't work on web
import { Platform } from 'react-native';

let NetworkInfo;
if (Platform.OS !== 'web') {
  NetworkInfo = require('@react-native-community/netinfo');
}
```

## 🛡️ Preventive Measures

### 1. Pre-commit Hooks

```json
// package.json
{
  "husky": {
    "hooks": {
      "pre-commit": "npm run type-check && npm run lint"
    }
  }
}
```

### 2. Continuous Integration

```yaml
# .github/workflows/ci.yml
name: CI
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Type check
        run: npm run type-check
      - name: Lint
        run: npm run lint
      - name: Test
        run: npm test
```

### 3. Development Scripts

```json
// package.json scripts
{
  "scripts": {
    "dev": "npm run type-check && npm start",
    "type-check": "tsc --noEmit",
    "type-check:watch": "tsc --noEmit --watch",
    "clean": "rm -rf node_modules && npm install",
    "reset": "npx expo start --clear --reset-cache"
  }
}
```

## 📊 Debugging Workflow Diagram

```
Start App → Default Expo Screen Appears
    ↓
Check Terminal Output
    ↓
TypeScript Errors? → Yes → Fix Type Errors → Restart
    ↓ No
Missing Dependencies? → Yes → Install/Remove → Restart
    ↓ No
Import/Export Issues? → Yes → Fix Imports → Restart
    ↓ No
Circular Dependencies? → Yes → Refactor Code → Restart
    ↓ No
Clear Cache → Restart → Still Issues?
    ↓ Yes
Create Minimal App → Test → Gradually Add Features
```

---

**Pro Tip:** Always fix errors in this order:
1. **Syntax errors** (TypeScript/ESLint)
2. **Missing dependencies**
3. **Import/export issues**
4. **Runtime errors**

Most "default screen" issues are actually compilation problems in disguise. Fix the compilation first, then worry about functionality.
