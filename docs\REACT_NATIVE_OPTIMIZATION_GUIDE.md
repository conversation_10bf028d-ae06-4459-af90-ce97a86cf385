# React Native Optimization Guide for FoodWay

## 🎯 Performance Optimization

### **1. JavaScript Thread Optimization**

#### **Reduce Bridge Communication**
```javascript
// ❌ Bad - Multiple bridge calls
const processItems = (items) => {
  items.forEach(item => {
    console.log(item.name); // Each log is a bridge call
  });
};

// ✅ Good - Batch operations
const processItems = (items) => {
  const names = items.map(item => item.name);
  console.log('Items:', names.join(', ')); // Single bridge call
};
```

#### **Use FlatList Optimizations**
```javascript
// In restaurant/menu lists
<FlatList
  data={menuItems}
  renderItem={renderMenuItem}
  keyExtractor={(item) => item.id}
  // Performance optimizations
  removeClippedSubviews={true}
  maxToRenderPerBatch={10}
  updateCellsBatchingPeriod={50}
  initialNumToRender={10}
  windowSize={10}
  getItemLayout={(data, index) => (
    {length: ITEM_HEIGHT, offset: ITEM_HEIGHT * index, index}
  )}
/>
```

### **2. Image Optimization**

#### **Implement Progressive Image Loading**
```javascript
import { Image } from 'expo-image';

const OptimizedFoodImage = ({ uri, placeholder }) => (
  <Image
    source={{ uri }}
    placeholder={placeholder}
    contentFit="cover"
    transition={200}
    cachePolicy="memory-disk"
    // Use WebP format for better compression
    format="webp"
  />
);
```

#### **Image Caching Strategy**
```javascript
// utils/imageCache.ts
import * as FileSystem from 'expo-file-system';

export const cacheImage = async (uri: string): Promise<string> => {
  const filename = uri.split('/').pop();
  const fileUri = `${FileSystem.cacheDirectory}${filename}`;
  
  const info = await FileSystem.getInfoAsync(fileUri);
  if (info.exists) {
    return fileUri;
  }
  
  await FileSystem.downloadAsync(uri, fileUri);
  return fileUri;
};
```

### **3. State Management Optimization**

#### **Optimize Zustand Store**
```javascript
// store/optimizedStore.ts
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

// Use selectors to prevent unnecessary re-renders
export const useRestaurantStore = create(
  subscribeWithSelector((set, get) => ({
    restaurants: [],
    selectedRestaurant: null,
    
    // Memoized selectors
    getFeaturedRestaurants: () => 
      get().restaurants.filter(r => r.featured),
    
    // Optimized actions
    setRestaurants: (restaurants) => set({ restaurants }),
  }))
);

// In components - use specific selectors
const restaurants = useRestaurantStore(state => state.restaurants);
const featured = useRestaurantStore(state => state.getFeaturedRestaurants());
```

### **4. Navigation Optimization**

#### **Lazy Loading Screens**
```javascript
// navigation/LazyScreens.tsx
import { lazy, Suspense } from 'react';
import LoadingSpinner from '../components/LoadingSpinner';

const RestaurantScreen = lazy(() => import('../app/restaurant/[id]'));
const OrderTrackingScreen = lazy(() => import('../app/order-tracking/[id]'));

export const LazyRestaurantScreen = (props) => (
  <Suspense fallback={<LoadingSpinner />}>
    <RestaurantScreen {...props} />
  </Suspense>
);
```

## 🔋 Battery & Memory Optimization

### **1. Efficient API Calls**

#### **Request Batching & Caching**
```javascript
// services/optimizedApi.ts
class OptimizedApiService {
  private cache = new Map();
  private requestQueue = [];
  
  // Batch multiple requests
  async batchRequests(requests) {
    return Promise.all(requests.map(req => this.makeRequest(req)));
  }
  
  // Cache with TTL
  async getCachedData(key, fetcher, ttl = 300000) { // 5 min TTL
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < ttl) {
      return cached.data;
    }
    
    const data = await fetcher();
    this.cache.set(key, { data, timestamp: Date.now() });
    return data;
  }
}
```

### **2. Background Task Optimization**

#### **Efficient Location Updates**
```javascript
// services/locationService.ts
import * as Location from 'expo-location';
import * as TaskManager from 'expo-task-manager';

const LOCATION_TASK_NAME = 'background-location-task';

// Optimize location accuracy based on context
export const startLocationTracking = async (accuracy = 'balanced') => {
  const accuracyMap = {
    'low': Location.Accuracy.Low,
    'balanced': Location.Accuracy.Balanced,
    'high': Location.Accuracy.High,
  };
  
  await Location.startLocationUpdatesAsync(LOCATION_TASK_NAME, {
    accuracy: accuracyMap[accuracy],
    timeInterval: 30000, // 30 seconds
    distanceInterval: 100, // 100 meters
    deferredUpdatesInterval: 60000, // 1 minute
  });
};
```

## 📱 App Size Optimization

### **1. Bundle Optimization**

#### **Metro Configuration**
```javascript
// metro.config.js
const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Enable tree shaking
config.transformer.minifierConfig = {
  keep_fnames: true,
  mangle: {
    keep_fnames: true,
  },
};

// Optimize asset resolution
config.resolver.assetExts.push('db', 'mp3', 'ttf', 'obj', 'png', 'jpg');

module.exports = config;
```

### **2. Code Splitting**

#### **Dynamic Imports**
```javascript
// utils/dynamicImports.ts
export const loadFeature = async (featureName: string) => {
  switch (featureName) {
    case 'chat':
      return import('../features/chat/ChatService');
    case 'loyalty':
      return import('../features/loyalty/LoyaltyService');
    default:
      throw new Error(`Unknown feature: ${featureName}`);
  }
};
```

## 🎨 UI/UX Optimization

### **1. Animation Performance**

#### **Use Native Driver**
```javascript
// components/OptimizedAnimations.tsx
import { Animated } from 'react-native';

const OptimizedFadeIn = ({ children }) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  
  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true, // ✅ Always use native driver
    }).start();
  }, []);
  
  return (
    <Animated.View style={{ opacity: fadeAnim }}>
      {children}
    </Animated.View>
  );
};
```

### **2. Gesture Optimization**

#### **Efficient Touch Handling**
```javascript
// components/OptimizedTouchable.tsx
import { Pressable } from 'react-native';

const OptimizedButton = ({ onPress, children }) => (
  <Pressable
    onPress={onPress}
    android_ripple={{ color: 'rgba(0,0,0,0.1)' }}
    style={({ pressed }) => [
      styles.button,
      pressed && styles.pressed
    ]}
  >
    {children}
  </Pressable>
);
```

## 🔧 Development Tools & Monitoring

### **1. Performance Monitoring**

#### **Custom Performance Tracker**
```javascript
// utils/performanceTracker.ts
class PerformanceTracker {
  private metrics = new Map();
  
  startTimer(name: string) {
    this.metrics.set(name, { start: Date.now() });
  }
  
  endTimer(name: string) {
    const metric = this.metrics.get(name);
    if (metric) {
      const duration = Date.now() - metric.start;
      console.log(`${name}: ${duration}ms`);
      
      // Send to analytics in production
      if (!__DEV__) {
        // Analytics.track('performance', { name, duration });
      }
    }
  }
}

export const performanceTracker = new PerformanceTracker();
```

### **2. Memory Leak Detection**

#### **Component Cleanup**
```javascript
// hooks/useCleanup.ts
import { useEffect, useRef } from 'react';

export const useCleanup = () => {
  const cleanupFunctions = useRef([]);
  
  const addCleanup = (fn) => {
    cleanupFunctions.current.push(fn);
  };
  
  useEffect(() => {
    return () => {
      cleanupFunctions.current.forEach(fn => fn());
      cleanupFunctions.current = [];
    };
  }, []);
  
  return addCleanup;
};
```

## 📊 Specific Optimizations for FoodWay

### **1. Restaurant List Optimization**
- Implement virtual scrolling for large restaurant lists
- Lazy load restaurant images
- Cache restaurant data with TTL
- Use skeleton screens for loading states

### **2. Order Tracking Optimization**
- Implement efficient WebSocket connections
- Batch location updates
- Use background tasks for order status updates
- Optimize map rendering performance

### **3. Search Optimization**
- Implement debounced search
- Cache search results
- Use fuzzy search algorithms
- Optimize filter operations

## 🎯 Implementation Priority

1. **High Impact, Low Effort**
   - Enable FlatList optimizations
   - Add image caching
   - Implement request batching

2. **High Impact, Medium Effort**
   - Add performance monitoring
   - Optimize navigation
   - Implement code splitting

3. **Medium Impact, High Effort**
   - Advanced caching strategies
   - Background task optimization
   - Custom native modules

This guide provides React Native-specific optimizations that will significantly improve the FoodWay app's performance, battery usage, and user experience.
