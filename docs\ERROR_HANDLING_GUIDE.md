# Comprehensive Error Handling Guide

This document outlines the complete error handling system implemented in the FoodWay customer app.

## Overview

The error handling system provides:
- Centralized error management
- User-friendly error messages
- Comprehensive error logging
- Network-aware error handling
- Form validation with real-time feedback
- Error recovery mechanisms
- Offline support with request queuing

## Core Components

### 1. <PERSON><PERSON><PERSON> Handler (`utils/errorHandler.ts`)

The main error handling system with custom error classes:

```typescript
import { AppError, NetworkError, AuthenticationError, ValidationError, PaymentError } from '../utils/errorHandler';

// Handle any error
const appError = errorHandler.handleError(error, 'LoginScreen.handleSubmit');

// Show user-friendly error with retry option
errorHandler.showErrorWithRetry(appError, () => retryOperation(), 'Login Failed');
```

**Error Types:**
- `AppError` - Base error class with user-friendly messages
- `NetworkError` - Network connectivity issues (retryable)
- `AuthenticationError` - Authentication failures
- `ValidationError` - Form validation errors
- `PaymentError` - Payment processing errors

### 2. Error Display Components (`components/ui/ErrorDisplay.tsx`)

Pre-built UI components for displaying errors:

```typescript
import { ErrorDisplay, FormError, NetworkError, LoadingError } from '../components/ui/ErrorDisplay';

// General error display
<ErrorDisplay 
  error={error} 
  onRetry={handleRetry} 
  onDismiss={() => setError(null)}
  variant="banner" 
/>

// Form field error
<FormError errors={formErrors} field="email" />

// Network error with retry
<NetworkError onRetry={handleRetry} message="Connection lost" />
```

### 3. Error Boundaries (`components/ui/ErrorBoundary.tsx`)

React error boundaries for catching component errors:

```typescript
import { ScreenErrorBoundary, ComponentErrorBoundary } from '../components/ui/ErrorBoundary';

// Wrap entire screens
<ScreenErrorBoundary screenName="LoginScreen">
  <LoginScreen />
</ScreenErrorBoundary>

// Wrap individual components
<ComponentErrorBoundary componentName="UserProfile">
  <UserProfile />
</ComponentErrorBoundary>
```

### 4. Form Validation (`utils/validation.ts`)

Comprehensive form validation with pre-defined schemas:

```typescript
import { createValidator, loginValidationSchema, validateForm } from '../utils/validation';

// Create validator
const validator = createValidator(loginValidationSchema);

// Validate form
const result = validator.validate(formData);
if (!result.isValid) {
  setErrors(result.errors);
}

// Validate single field
const error = validator.validateSingle('email', emailValue);
```

### 5. Enhanced API Client (`services/api/enhancedClient.ts`)

Network-aware API client with automatic error handling:

```typescript
import { enhancedApiClient } from '../services/api/enhancedClient';

// Automatic retry for network errors
const data = await enhancedApiClient.get('/api/restaurants');

// File upload with progress
await enhancedApiClient.uploadFile('/api/upload', file, {}, (progress) => {
  console.log(`Upload progress: ${progress}%`);
});
```

### 6. Storage Error Handling (`utils/storage.ts`)

Safe storage operations with error recovery:

```typescript
import { storageManager, storeData, retrieveData } from '../utils/storage';

// Store data safely
await storeData('user_preferences', preferences);

// Retrieve with error handling
const preferences = await retrieveData('user_preferences');
```

### 7. Network Management (`utils/networkManager.ts`)

Network status monitoring and offline request queuing:

```typescript
import { networkManager, useNetworkStatus } from '../utils/networkManager';

// React hook for network status
const networkStatus = useNetworkStatus();

// Queue requests when offline
await networkManager.queueRequest('/api/orders', { method: 'POST' }, 'high');
```

### 8. Error Logging (`utils/errorLogger.ts`)

Comprehensive error logging with breadcrumbs:

```typescript
import { errorLogger, addBreadcrumb, logError } from '../utils/errorLogger';

// Add breadcrumb for user actions
addBreadcrumb('user', 'User clicked login button', 'info');

// Log error with context
await logError(error, 'LoginScreen.handleSubmit', 'high', ['authentication']);
```

## React Hooks

### 1. useErrorHandler

General purpose error handling hook:

```typescript
import { useErrorHandler } from '../hooks/useErrorHandler';

const [errorState, { handleError, clearError, retry }] = useErrorHandler({
  context: 'LoginScreen',
  showAlerts: true,
  logErrors: true,
});

// Handle any error
const appError = handleError(error, 'login attempt');

// Retry operation
if (errorState.error?.isRetryable) {
  await retry();
}
```

### 2. useApiErrorHandler

Specialized hook for API calls:

```typescript
import { useApiErrorHandler } from '../hooks/useErrorHandler';

const { handleApiCall, error, isRetrying } = useApiErrorHandler('UserProfile');

// Handle API call with automatic error handling
const userData = await handleApiCall(
  () => api.getUserProfile(),
  {
    onSuccess: (data) => setUser(data),
    showSuccessMessage: 'Profile updated successfully',
    retryable: true,
  }
);
```

### 3. useFormErrorHandler

Form-specific error handling:

```typescript
import { useFormErrorHandler } from '../hooks/useErrorHandler';

const { 
  fieldErrors, 
  handleFormError, 
  clearFieldError, 
  hasFieldError 
} = useFormErrorHandler('LoginForm');

// Handle form validation error
handleFormError(validationError, 'email');

// Check if field has error
if (hasFieldError('email')) {
  // Show error styling
}
```

## Implementation Examples

### 1. Enhanced Login Screen

```typescript
import React, { useState, useCallback } from 'react';
import { useFormErrorHandler } from '../hooks/useErrorHandler';
import { createValidator, loginValidationSchema } from '../utils/validation';
import { ErrorDisplay, FormError } from '../components/ui/ErrorDisplay';
import { ComponentErrorBoundary } from '../components/ui/ErrorBoundary';

export default function LoginScreen() {
  const [formData, setFormData] = useState({ email: '', password: '' });
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const {
    fieldErrors,
    handleFormError,
    clearFieldError,
    error,
    clearError,
  } = useFormErrorHandler('LoginScreen');
  
  const validator = createValidator(loginValidationSchema);

  const handleSubmit = useCallback(async () => {
    try {
      setIsSubmitting(true);
      clearError();

      // Validate form
      const result = validator.validate(formData);
      if (!result.isValid) {
        Object.keys(result.errors).forEach(field => {
          handleFormError(new ValidationError(result.errors[field]), field);
        });
        return;
      }

      // Submit form
      await authStore.login(formData);
      router.replace('/(tabs)');
    } catch (error) {
      handleFormError(error);
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, validator, handleFormError, clearError]);

  return (
    <ComponentErrorBoundary componentName="LoginScreen">
      <View style={styles.container}>
        {/* API Error Display */}
        {error && (
          <ErrorDisplay
            error={error}
            onRetry={error.isRetryable ? handleSubmit : undefined}
            onDismiss={clearError}
            variant="banner"
          />
        )}

        {/* Email Input */}
        <TextInput
          value={formData.email}
          onChangeText={(value) => {
            setFormData(prev => ({ ...prev, email: value }));
            clearFieldError('email');
          }}
          onFocus={() => clearFieldError('email')}
        />
        <FormError errors={fieldErrors} field="email" />

        {/* Submit Button */}
        <TouchableOpacity
          onPress={handleSubmit}
          disabled={isSubmitting}
        >
          <Text>{isSubmitting ? 'Signing In...' : 'Sign In'}</Text>
        </TouchableOpacity>
      </View>
    </ComponentErrorBoundary>
  );
}
```

### 2. Enhanced API Service

```typescript
import { enhancedApiClient } from '../services/api/enhancedClient';
import { withRetry } from '../utils/errorHandler';

export class RestaurantService {
  async getRestaurants(filters?: any) {
    return withRetry(
      () => enhancedApiClient.get('/restaurants', filters),
      3, // max retries
      1000 // delay
    );
  }

  async placeOrder(orderData: any) {
    return enhancedApiClient.post('/orders', orderData, {
      timeout: 30000, // 30 second timeout for orders
      retries: 1, // Only retry once for orders
    });
  }
}
```

## Best Practices

### 1. Error Context
Always provide context when handling errors:
```typescript
handleError(error, 'LoginScreen.handleSubmit');
handleError(error, 'RestaurantList.loadData');
```

### 2. User-Friendly Messages
Use the error's `userMessage` property for display:
```typescript
Alert.alert('Error', appError.userMessage);
```

### 3. Retry Logic
Only retry operations that are marked as retryable:
```typescript
if (error.isRetryable) {
  showErrorWithRetry(error, retryOperation);
} else {
  showError(error);
}
```

### 4. Form Validation
Validate forms both on submit and real-time:
```typescript
// Real-time validation
const handleFieldChange = (field, value) => {
  setFormData(prev => ({ ...prev, [field]: value }));
  
  // Debounced validation
  setTimeout(() => {
    const error = validator.validateSingle(field, value);
    if (error) {
      setFieldErrors(prev => ({ ...prev, [field]: error }));
    }
  }, 300);
};
```

### 5. Error Boundaries
Wrap components with appropriate error boundaries:
```typescript
// Screen level
<ScreenErrorBoundary screenName="RestaurantList">
  <RestaurantListScreen />
</ScreenErrorBoundary>

// Component level
<ComponentErrorBoundary componentName="RestaurantCard">
  <RestaurantCard restaurant={restaurant} />
</ComponentErrorBoundary>
```

## Testing

The error handling system includes comprehensive tests:

- `tests/utils/errorHandler.test.ts` - Core error handling tests
- `tests/utils/validation.test.ts` - Form validation tests
- `tests/hooks/useErrorHandler.test.ts` - Hook tests
- `tests/components/ErrorDisplay.test.tsx` - Component tests

Run tests with:
```bash
npm test
```

## Debugging

Use the Error Dashboard component for debugging:

```typescript
import { ErrorDashboard } from '../components/debug/ErrorDashboard';

// Show in development builds
{__DEV__ && (
  <ErrorDashboard
    visible={showDebugModal}
    onClose={() => setShowDebugModal(false)}
  />
)}
```

## Configuration

Error handling can be configured in `utils/constants.ts`:

```typescript
export const ERROR_CONFIG = {
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
  LOG_ERRORS: true,
  SHOW_ALERTS: true,
  AUTO_CLEAR_TIMEOUT: 10000,
};
```

This comprehensive error handling system ensures a robust and user-friendly experience throughout the FoodWay customer app.
