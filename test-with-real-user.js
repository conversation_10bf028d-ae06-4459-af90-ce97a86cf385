// Test with Real User Credentials and Protected Endpoints
const axios = require('axios');

const BASE_URL = 'https://backend-production-f106.up.railway.app/api/v1';

const apiClient = axios.create({
  baseURL: BASE_URL,
  timeout: 30000, // Increased timeout for performance issues
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Real user credentials provided
const USER_CREDENTIALS = {
  email: '<EMAIL>',
  password: '/Abd/0321'
};

let authToken = null;
let refreshToken = null;
let userData = null;

async function loginWithRealUser() {
  console.log('🔐 Attempting login with real user credentials...');
  console.log('Email:', USER_CREDENTIALS.email);
  
  try {
    const loginResponse = await apiClient.post('/auth/login', USER_CREDENTIALS);
    
    console.log('✅ Login successful!');
    console.log('Status:', loginResponse.status);
    
    const responseData = loginResponse.data.data;
    authToken = responseData.token;
    refreshToken = responseData.refreshToken;
    userData = responseData.user;
    
    console.log('🔑 Token received:', authToken ? 'Yes' : 'No');
    console.log('🔄 Refresh token received:', refreshToken ? 'Yes' : 'No');
    console.log('👤 User ID:', userData.id);
    console.log('📧 Email verified:', userData.emailVerified);
    console.log('📱 Phone verified:', userData.phoneVerified);
    
    return true;
  } catch (error) {
    console.log('❌ Login failed:');
    console.log('Status:', error.response?.status);
    console.log('Error:', error.response?.data);
    return false;
  }
}

async function testProtectedEndpoint(method, endpoint, data = null, description = '') {
  console.log(`\n🔄 Testing: ${method} ${endpoint}`);
  if (description) console.log(`   ${description}`);
  
  const config = {
    method: method.toLowerCase(),
    url: endpoint,
    headers: {
      'Authorization': `Bearer ${authToken}`
    }
  };
  
  if (data) {
    config.data = data;
  }
  
  try {
    const response = await apiClient.request(config);
    console.log(`✅ SUCCESS: ${response.status} - ${endpoint}`);
    
    // Show relevant response data
    if (response.data?.data) {
      const responseData = response.data.data;
      if (Array.isArray(responseData)) {
        console.log(`   📊 Returned ${responseData.length} items`);
        if (responseData.length > 0) {
          console.log(`   📋 Sample item keys:`, Object.keys(responseData[0]));
        }
      } else if (typeof responseData === 'object') {
        console.log(`   📋 Response keys:`, Object.keys(responseData));
      }
    }
    
    return { success: true, status: response.status, data: response.data };
  } catch (error) {
    console.log(`❌ FAILED: ${error.response?.status || 'ERROR'} - ${endpoint}`);
    if (error.response?.data) {
      console.log(`   Error:`, error.response.data.error?.message || error.response.data);
    }
    return { 
      success: false, 
      status: error.response?.status || 0, 
      error: error.response?.data 
    };
  }
}

async function testAllProtectedEndpoints() {
  console.log('\n🛡️ Testing All Protected Endpoints');
  console.log('=' * 50);
  
  const results = [];
  
  // User Management Endpoints
  console.log('\n📂 USER MANAGEMENT:');
  
  results.push(await testProtectedEndpoint('GET', '/user/profile', null, 'Get user profile'));
  
  results.push(await testProtectedEndpoint('PUT', '/user/profile', {
    firstName: 'Updated',
    lastName: 'Name'
  }, 'Update user profile'));
  
  results.push(await testProtectedEndpoint('GET', '/user/addresses', null, 'Get user addresses'));
  
  results.push(await testProtectedEndpoint('POST', '/user/addresses', {
    street: '123 Test Street',
    city: 'Test City',
    state: 'TS',
    zipCode: '12345',
    country: 'US',
    isDefault: false,
    label: 'Test Address'
  }, 'Add new address'));
  
  results.push(await testProtectedEndpoint('GET', '/user/payment-methods', null, 'Get payment methods'));
  
  results.push(await testProtectedEndpoint('GET', '/user/favorites', null, 'Get user favorites'));
  
  results.push(await testProtectedEndpoint('GET', '/user/reviews', null, 'Get user reviews'));
  
  results.push(await testProtectedEndpoint('GET', '/user/notification-settings', null, 'Get notification settings'));
  
  // Order Management
  console.log('\n📂 ORDER MANAGEMENT:');
  
  results.push(await testProtectedEndpoint('GET', '/orders', null, 'Get user orders'));
  
  // Get a restaurant ID first for order creation
  try {
    const restaurantsResponse = await apiClient.get('/restaurants');
    const restaurants = restaurantsResponse.data.data.restaurants;
    
    if (restaurants.length > 0) {
      const testRestaurant = restaurants[0];
      console.log(`   Using restaurant: ${testRestaurant.name} (${testRestaurant.id})`);
      
      results.push(await testProtectedEndpoint('POST', '/orders', {
        restaurantId: testRestaurant.id,
        items: [{
          menuItemId: 'test-item-id',
          quantity: 1,
          price: 15.99,
          specialInstructions: 'Test order'
        }],
        deliveryAddress: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          country: 'US'
        },
        paymentMethodId: 'test-payment-method'
      }, 'Create test order'));
    }
  } catch (error) {
    console.log('   ⚠️ Could not get restaurant for order test');
  }
  
  // Reviews
  console.log('\n📂 REVIEWS:');
  
  try {
    const restaurantsResponse = await apiClient.get('/restaurants');
    const restaurants = restaurantsResponse.data.data.restaurants;
    
    if (restaurants.length > 0) {
      const testRestaurant = restaurants[0];
      
      results.push(await testProtectedEndpoint('POST', '/reviews', {
        restaurantId: testRestaurant.id,
        rating: 5,
        comment: 'Test review - great food!'
      }, 'Create restaurant review'));
    }
  } catch (error) {
    console.log('   ⚠️ Could not get restaurant for review test');
  }
  
  // Notifications
  console.log('\n📂 NOTIFICATIONS:');
  
  results.push(await testProtectedEndpoint('GET', '/notifications', null, 'Get notifications'));
  
  results.push(await testProtectedEndpoint('PATCH', '/notifications/read-all', null, 'Mark all notifications as read'));
  
  // Promotions
  console.log('\n📂 PROMOTIONS:');
  
  results.push(await testProtectedEndpoint('POST', '/promotions/validate', {
    code: 'TESTCODE'
  }, 'Validate promotion code'));
  
  // Auth endpoints that require token
  console.log('\n📂 AUTHENTICATION:');
  
  results.push(await testProtectedEndpoint('POST', '/auth/refresh', {
    refreshToken: refreshToken
  }, 'Refresh authentication token'));
  
  results.push(await testProtectedEndpoint('POST', '/auth/logout', null, 'Logout user'));
  
  return results;
}

function generateSummary(results) {
  console.log('\n' + '=' * 60);
  console.log('📊 PROTECTED ENDPOINTS TEST SUMMARY');
  console.log('=' * 60);
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`\n📈 Overall Results:`);
  console.log(`Total Tests: ${results.length}`);
  console.log(`✅ Successful: ${successful.length}`);
  console.log(`❌ Failed: ${failed.length}`);
  console.log(`📊 Success Rate: ${((successful.length / results.length) * 100).toFixed(1)}%`);
  
  if (failed.length > 0) {
    console.log(`\n❌ Failed Endpoints:`);
    failed.forEach(result => {
      console.log(`   ${result.status} - ${result.error?.error?.message || 'Unknown error'}`);
    });
  }
  
  console.log(`\n🎯 Key Findings:`);
  if (successful.length === results.length) {
    console.log('🟢 All protected endpoints are working perfectly!');
  } else if (successful.length > results.length * 0.8) {
    console.log('🟡 Most protected endpoints working, minor issues to address');
  } else if (successful.length > results.length * 0.5) {
    console.log('🟠 Significant portion working, some endpoints need attention');
  } else {
    console.log('🔴 Major issues with protected endpoints');
  }
}

async function main() {
  console.log('🚀 Testing FoodWay Backend with Real User Credentials');
  console.log('=' * 60);
  
  // Step 1: Login with real user
  const loginSuccess = await loginWithRealUser();
  
  if (!loginSuccess) {
    console.log('\n🔴 Cannot proceed without successful authentication');
    return;
  }
  
  // Step 2: Test all protected endpoints
  const results = await testAllProtectedEndpoints();
  
  // Step 3: Generate summary
  generateSummary(results);
  
  console.log('\n✨ Testing completed!');
}

main().catch(console.error);
