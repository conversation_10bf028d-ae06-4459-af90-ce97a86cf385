<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="80" viewBox="0 0 300 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="300" height="80" fill="transparent"/>
  
  <!-- Logo Icon -->
  <g transform="translate(10, 10)">
    <!-- Background Circle -->
    <circle cx="30" cy="30" r="25" fill="#FF6B35"/>
    
    <!-- Fork -->
    <g transform="translate(20, 18)">
      <rect x="0" y="0" width="2" height="15" fill="#FFFFFF" rx="1"/>
      <rect x="4" y="0" width="2" height="12" fill="#FFFFFF" rx="1"/>
      <rect x="8" y="0" width="2" height="15" fill="#FFFFFF" rx="1"/>
      <rect x="12" y="0" width="2" height="12" fill="#FFFFFF" rx="1"/>
      <rect x="0" y="12" width="14" height="2" fill="#FFFFFF" rx="1"/>
      <rect x="6" y="14" width="2" height="10" fill="#FFFFFF" rx="1"/>
    </g>
    
    <!-- Spoon -->
    <g transform="translate(35, 18)">
      <ellipse cx="5" cy="3" rx="5" ry="3" fill="#FFFFFF"/>
      <rect x="4" y="6" width="2" height="10" fill="#FFFFFF" rx="1"/>
    </g>
    
    <!-- Food Item -->
    <g transform="translate(22, 35)">
      <ellipse cx="8" cy="3" rx="6" ry="2" fill="#8B4513"/>
      <ellipse cx="8" cy="5" rx="5" ry="1.5" fill="#228B22"/>
      <ellipse cx="8" cy="7" rx="6" ry="2" fill="#DEB887"/>
    </g>
  </g>
  
  <!-- Text -->
  <text x="80" y="45" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="#FF6B35">
    FoodWay
  </text>
  
  <!-- Tagline -->
  <text x="80" y="65" font-family="Arial, sans-serif" font-size="14" fill="#666666">
    Delicious food, delivered fast
  </text>
</svg>
