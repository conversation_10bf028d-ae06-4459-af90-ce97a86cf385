import { Alert } from 'react-native';
import { ERROR_TYPES, ERROR_MESSAGES, RETRY_CONFIG } from './constants';

// Custom Error Classes
export class AppError extends Error {
  public readonly type: string;
  public readonly code?: string;
  public readonly statusCode?: number;
  public readonly isRetryable: boolean;
  public readonly userMessage: string;

  constructor(
    type: string,
    message: string,
    userMessage?: string,
    code?: string,
    statusCode?: number,
    isRetryable: boolean = false
  ) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.code = code;
    this.statusCode = statusCode;
    this.isRetryable = isRetryable;
    this.userMessage = userMessage || ERROR_MESSAGES[type as keyof typeof ERROR_MESSAGES] || ERROR_MESSAGES[ERROR_TYPES.UNKNOWN_ERROR];
  }
}

export class NetworkError extends AppError {
  constructor(message: string, statusCode?: number) {
    super(
      ERROR_TYPES.NETWORK_ERROR,
      message,
      ERROR_MESSAGES[ERROR_TYPES.NETWORK_ERROR],
      'NETWORK_ERROR',
      statusCode,
      true
    );
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string, code?: string) {
    super(
      ERROR_TYPES.AUTH_ERROR,
      message,
      ERROR_MESSAGES[ERROR_TYPES.AUTH_ERROR],
      code,
      401,
      false
    );
  }
}

export class ValidationError extends AppError {
  public readonly field?: string;
  public readonly validationErrors?: Record<string, string>;

  constructor(message: string, field?: string, validationErrors?: Record<string, string>) {
    super(
      ERROR_TYPES.VALIDATION_ERROR,
      message,
      ERROR_MESSAGES[ERROR_TYPES.VALIDATION_ERROR],
      'VALIDATION_ERROR',
      400,
      false
    );
    this.field = field;
    this.validationErrors = validationErrors;
  }
}

export class PaymentError extends AppError {
  constructor(message: string, code?: string) {
    super(
      ERROR_TYPES.PAYMENT_ERROR,
      message,
      ERROR_MESSAGES[ERROR_TYPES.PAYMENT_ERROR],
      code,
      402,
      false
    );
  }
}

// Error Handler Class
export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorLog: AppError[] = [];

  private constructor() {}

  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  // Handle different types of errors
  public handleError(error: unknown, context?: string): AppError {
    let appError: AppError;

    if (error instanceof AppError) {
      appError = error;
    } else if (error instanceof Error) {
      appError = this.parseError(error);
    } else {
      appError = new AppError(
        ERROR_TYPES.UNKNOWN_ERROR,
        'Unknown error occurred',
        ERROR_MESSAGES[ERROR_TYPES.UNKNOWN_ERROR]
      );
    }

    // Log the error
    this.logError(appError, context);

    return appError;
  }

  // Parse different error types
  private parseError(error: Error): AppError {
    const message = error.message.toLowerCase();

    // Network errors
    if (message.includes('network') || message.includes('fetch')) {
      return new NetworkError(error.message);
    }

    // Timeout errors
    if (message.includes('timeout')) {
      return new AppError(
        ERROR_TYPES.TIMEOUT_ERROR,
        error.message,
        ERROR_MESSAGES[ERROR_TYPES.TIMEOUT_ERROR],
        'TIMEOUT_ERROR',
        408,
        true
      );
    }

    // Authentication errors
    if (message.includes('unauthorized') || message.includes('401')) {
      return new AuthenticationError(error.message);
    }

    // Validation errors
    if (message.includes('validation') || message.includes('invalid')) {
      return new ValidationError(error.message);
    }

    // Default to unknown error
    return new AppError(
      ERROR_TYPES.UNKNOWN_ERROR,
      error.message,
      ERROR_MESSAGES[ERROR_TYPES.UNKNOWN_ERROR]
    );
  }

  // Log error for debugging and monitoring
  private logError(error: AppError, context?: string): void {
    const errorEntry = {
      ...error,
      context,
      timestamp: new Date().toISOString(),
    };

    this.errorLog.push(error);

    // Keep only last 100 errors
    if (this.errorLog.length > 100) {
      this.errorLog = this.errorLog.slice(-100);
    }

    // Log to console in development
    if (__DEV__) {
      console.error('Error Handler:', errorEntry);
    }

    // TODO: Send to crash reporting service in production
    // Example: Sentry, Crashlytics, etc.
  }

  // Show user-friendly error message
  public showError(error: AppError, title?: string): void {
    Alert.alert(
      title || 'Error',
      error.userMessage,
      [{ text: 'OK', style: 'default' }]
    );
  }

  // Show error with retry option
  public showErrorWithRetry(
    error: AppError,
    onRetry: () => void,
    title?: string
  ): void {
    const buttons = [
      { text: 'Cancel', style: 'cancel' as const },
    ];

    if (error.isRetryable) {
      buttons.push({
        text: 'Retry',
        style: 'default' as const,
        onPress: onRetry,
      });
    }

    Alert.alert(
      title || 'Error',
      error.userMessage,
      buttons
    );
  }

  // Get error logs for debugging
  public getErrorLogs(): AppError[] {
    return [...this.errorLog];
  }

  // Clear error logs
  public clearErrorLogs(): void {
    this.errorLog = [];
  }
}

// Utility functions
export const errorHandler = ErrorHandler.getInstance();

export const handleApiError = (error: unknown, context?: string): AppError => {
  return errorHandler.handleError(error, context);
};

export const showError = (error: unknown, title?: string): void => {
  const appError = errorHandler.handleError(error);
  errorHandler.showError(appError, title);
};

export const showErrorWithRetry = (
  error: unknown,
  onRetry: () => void,
  title?: string
): void => {
  const appError = errorHandler.handleError(error);
  errorHandler.showErrorWithRetry(appError, onRetry, title);
};

// Retry mechanism
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = RETRY_CONFIG.MAX_RETRIES,
  delay: number = RETRY_CONFIG.RETRY_DELAY
): Promise<T> => {
  let lastError: unknown;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      const appError = errorHandler.handleError(error);
      
      // Don't retry if error is not retryable or this is the last attempt
      if (!appError.isRetryable || attempt === maxRetries) {
        throw error;
      }

      // Wait before retrying with exponential backoff
      const retryDelay = RETRY_CONFIG.EXPONENTIAL_BACKOFF 
        ? delay * Math.pow(2, attempt)
        : delay;
      
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }

  throw lastError;
};

// Error boundary helper
export const createErrorBoundary = (fallbackComponent: React.ComponentType<any>) => {
  return class ErrorBoundary extends React.Component<
    { children: React.ReactNode },
    { hasError: boolean; error?: AppError }
  > {
    constructor(props: { children: React.ReactNode }) {
      super(props);
      this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error) {
      const appError = errorHandler.handleError(error, 'ErrorBoundary');
      return { hasError: true, error: appError };
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
      errorHandler.handleError(error, `ErrorBoundary: ${errorInfo.componentStack}`);
    }

    render() {
      if (this.state.hasError) {
        return React.createElement(fallbackComponent, { error: this.state.error });
      }

      return this.props.children;
    }
  };
};
