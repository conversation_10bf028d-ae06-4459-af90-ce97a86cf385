import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Alert,
  Share,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { errorLogger, ErrorLogEntry, BreadcrumbEntry } from '../../utils/errorLogger';
import { networkManager } from '../../utils/networkManager';
import { storageManager } from '../../utils/storage';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../utils/constants';

interface ErrorDashboardProps {
  visible: boolean;
  onClose: () => void;
}

export const ErrorDashboard: React.FC<ErrorDashboardProps> = ({
  visible,
  onClose,
}) => {
  const [errorLogs, setErrorLogs] = useState<ErrorLogEntry[]>([]);
  const [selectedError, setSelectedError] = useState<ErrorLogEntry | null>(null);
  const [activeTab, setActiveTab] = useState<'errors' | 'network' | 'storage'>('errors');
  const [networkStatus, setNetworkStatus] = useState(networkManager.getNetworkStatus());
  const [storageInfo, setStorageInfo] = useState<any>(null);

  useEffect(() => {
    if (visible) {
      loadErrorLogs();
      loadStorageInfo();
    }
  }, [visible]);

  useEffect(() => {
    const unsubscribe = networkManager.addNetworkListener((status) => {
      setNetworkStatus(status);
    });

    return unsubscribe;
  }, []);

  const loadErrorLogs = async () => {
    try {
      const logs = await errorLogger.getErrorLogs();
      setErrorLogs(logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()));
    } catch (error) {
      console.error('Failed to load error logs:', error);
    }
  };

  const loadStorageInfo = async () => {
    try {
      const info = await storageManager.getStorageInfo();
      setStorageInfo(info);
    } catch (error) {
      console.error('Failed to load storage info:', error);
    }
  };

  const clearAllLogs = () => {
    Alert.alert(
      'Clear All Logs',
      'Are you sure you want to clear all error logs? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              await errorLogger.clearErrorLogs();
              setErrorLogs([]);
              setSelectedError(null);
            } catch (error) {
              Alert.alert('Error', 'Failed to clear logs');
            }
          },
        },
      ]
    );
  };

  const exportLogs = async () => {
    try {
      const logsData = {
        errors: errorLogs,
        network: networkStatus,
        storage: storageInfo,
        timestamp: new Date().toISOString(),
      };

      const content = JSON.stringify(logsData, null, 2);
      
      await Share.share({
        message: content,
        title: 'Error Logs Export',
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to export logs');
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return COLORS.error;
      case 'high': return COLORS.warning;
      case 'medium': return COLORS.info;
      case 'low': return COLORS.success;
      default: return COLORS.textSecondary;
    }
  };

  const renderErrorList = () => (
    <ScrollView style={styles.tabContent}>
      <View style={styles.listHeader}>
        <Text style={styles.listTitle}>Error Logs ({errorLogs.length})</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity onPress={exportLogs} style={styles.actionButton}>
            <Ionicons name="share-outline" size={20} color={COLORS.primary} />
          </TouchableOpacity>
          <TouchableOpacity onPress={clearAllLogs} style={styles.actionButton}>
            <Ionicons name="trash-outline" size={20} color={COLORS.error} />
          </TouchableOpacity>
        </View>
      </View>

      {errorLogs.map((error) => (
        <TouchableOpacity
          key={error.id}
          style={styles.errorItem}
          onPress={() => setSelectedError(error)}
        >
          <View style={styles.errorHeader}>
            <View style={[styles.severityBadge, { backgroundColor: getSeverityColor(error.severity) }]}>
              <Text style={styles.severityText}>{error.severity.toUpperCase()}</Text>
            </View>
            <Text style={styles.errorTime}>{formatTimestamp(error.timestamp)}</Text>
          </View>
          <Text style={styles.errorType}>{error.error.type}</Text>
          <Text style={styles.errorMessage} numberOfLines={2}>
            {error.error.message}
          </Text>
          {error.context.screen && (
            <Text style={styles.errorContext}>Screen: {error.context.screen}</Text>
          )}
        </TouchableOpacity>
      ))}

      {errorLogs.length === 0 && (
        <View style={styles.emptyState}>
          <Ionicons name="checkmark-circle-outline" size={48} color={COLORS.success} />
          <Text style={styles.emptyStateText}>No errors logged</Text>
        </View>
      )}
    </ScrollView>
  );

  const renderNetworkInfo = () => (
    <ScrollView style={styles.tabContent}>
      <Text style={styles.listTitle}>Network Status</Text>
      
      <View style={styles.infoCard}>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Connected:</Text>
          <View style={[styles.statusBadge, { backgroundColor: networkStatus.isConnected ? COLORS.success : COLORS.error }]}>
            <Text style={styles.statusText}>
              {networkStatus.isConnected ? 'YES' : 'NO'}
            </Text>
          </View>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Type:</Text>
          <Text style={styles.infoValue}>{networkStatus.type}</Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Internet Reachable:</Text>
          <Text style={styles.infoValue}>
            {networkStatus.isInternetReachable === null ? 'Unknown' : 
             networkStatus.isInternetReachable ? 'Yes' : 'No'}
          </Text>
        </View>
        
        {networkStatus.details?.connectionQuality && (
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Quality:</Text>
            <Text style={styles.infoValue}>{networkStatus.details.connectionQuality}</Text>
          </View>
        )}
        
        {networkStatus.details?.queuedRequests !== undefined && (
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Queued Requests:</Text>
            <Text style={styles.infoValue}>{networkStatus.details.queuedRequests}</Text>
          </View>
        )}
      </View>
    </ScrollView>
  );

  const renderStorageInfo = () => (
    <ScrollView style={styles.tabContent}>
      <Text style={styles.listTitle}>Storage Information</Text>
      
      {storageInfo && (
        <View style={styles.infoCard}>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>AsyncStorage Size:</Text>
            <Text style={styles.infoValue}>
              {(storageInfo.asyncStorageSize / 1024).toFixed(2)} KB
            </Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Secure Store Keys:</Text>
            <Text style={styles.infoValue}>{storageInfo.secureStoreKeys.length}</Text>
          </View>
          
          {storageInfo.secureStoreKeys.length > 0 && (
            <View style={styles.keysList}>
              <Text style={styles.keysTitle}>Secure Keys:</Text>
              {storageInfo.secureStoreKeys.map((key: string, index: number) => (
                <Text key={index} style={styles.keyItem}>• {key}</Text>
              ))}
            </View>
          )}
        </View>
      )}
    </ScrollView>
  );

  const renderErrorDetail = () => {
    if (!selectedError) return null;

    return (
      <Modal
        visible={true}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setSelectedError(null)}
      >
        <View style={styles.detailContainer}>
          <View style={styles.detailHeader}>
            <Text style={styles.detailTitle}>Error Details</Text>
            <TouchableOpacity onPress={() => setSelectedError(null)}>
              <Ionicons name="close" size={24} color={COLORS.textPrimary} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.detailContent}>
            <View style={styles.detailSection}>
              <Text style={styles.detailSectionTitle}>Error Information</Text>
              <Text style={styles.detailText}>Type: {selectedError.error.type}</Text>
              <Text style={styles.detailText}>Message: {selectedError.error.message}</Text>
              {selectedError.error.code && (
                <Text style={styles.detailText}>Code: {selectedError.error.code}</Text>
              )}
              {selectedError.error.statusCode && (
                <Text style={styles.detailText}>Status: {selectedError.error.statusCode}</Text>
              )}
            </View>

            <View style={styles.detailSection}>
              <Text style={styles.detailSectionTitle}>Context</Text>
              <Text style={styles.detailText}>Screen: {selectedError.context.screen || 'Unknown'}</Text>
              <Text style={styles.detailText}>Action: {selectedError.context.action || 'Unknown'}</Text>
              <Text style={styles.detailText}>User ID: {selectedError.context.userId || 'Anonymous'}</Text>
              <Text style={styles.detailText}>Session: {selectedError.context.sessionId}</Text>
            </View>

            <View style={styles.detailSection}>
              <Text style={styles.detailSectionTitle}>Device Information</Text>
              <Text style={styles.detailText}>Platform: {selectedError.device.platform}</Text>
              <Text style={styles.detailText}>Version: {selectedError.device.version}</Text>
              {selectedError.device.model && (
                <Text style={styles.detailText}>Model: {selectedError.device.model}</Text>
              )}
            </View>

            {selectedError.breadcrumbs.length > 0 && (
              <View style={styles.detailSection}>
                <Text style={styles.detailSectionTitle}>Breadcrumbs</Text>
                {selectedError.breadcrumbs.slice(-10).map((breadcrumb, index) => (
                  <View key={index} style={styles.breadcrumbItem}>
                    <Text style={styles.breadcrumbTime}>
                      {formatTimestamp(breadcrumb.timestamp)}
                    </Text>
                    <Text style={styles.breadcrumbMessage}>
                      [{breadcrumb.category}] {breadcrumb.message}
                    </Text>
                  </View>
                ))}
              </View>
            )}

            {selectedError.error.stack && (
              <View style={styles.detailSection}>
                <Text style={styles.detailSectionTitle}>Stack Trace</Text>
                <Text style={styles.stackTrace}>{selectedError.error.stack}</Text>
              </View>
            )}
          </ScrollView>
        </View>
      </Modal>
    );
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Error Dashboard</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={COLORS.textPrimary} />
          </TouchableOpacity>
        </View>

        <View style={styles.tabs}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'errors' && styles.activeTab]}
            onPress={() => setActiveTab('errors')}
          >
            <Text style={[styles.tabText, activeTab === 'errors' && styles.activeTabText]}>
              Errors
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'network' && styles.activeTab]}
            onPress={() => setActiveTab('network')}
          >
            <Text style={[styles.tabText, activeTab === 'network' && styles.activeTabText]}>
              Network
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'storage' && styles.activeTab]}
            onPress={() => setActiveTab('storage')}
          >
            <Text style={[styles.tabText, activeTab === 'storage' && styles.activeTabText]}>
              Storage
            </Text>
          </TouchableOpacity>
        </View>

        {activeTab === 'errors' && renderErrorList()}
        {activeTab === 'network' && renderNetworkInfo()}
        {activeTab === 'storage' && renderStorageInfo()}

        {renderErrorDetail()}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  title: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
  },
  closeButton: {
    padding: SPACING.xs,
  },
  tabs: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  tab: {
    flex: 1,
    paddingVertical: SPACING.md,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: COLORS.primary,
  },
  tabText: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
  },
  activeTabText: {
    color: COLORS.primary,
    fontWeight: '600',
  },
  tabContent: {
    flex: 1,
    padding: SPACING.lg,
  },
  listHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  listTitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
  },
  headerActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: SPACING.sm,
    marginLeft: SPACING.sm,
  },
  errorItem: {
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    marginBottom: SPACING.sm,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  errorHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  severityBadge: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.sm,
  },
  severityText: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  errorTime: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    color: COLORS.textSecondary,
  },
  errorType: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '600',
    color: COLORS.error,
    marginBottom: SPACING.xs,
  },
  errorMessage: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  errorContext: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
  },
  emptyStateText: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
    marginTop: SPACING.sm,
  },
  infoCard: {
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  infoLabel: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  infoValue: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
  },
  statusBadge: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.sm,
  },
  statusText: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  keysList: {
    marginTop: SPACING.sm,
  },
  keysTitle: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  keyItem: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    marginLeft: SPACING.sm,
  },
  detailContainer: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  detailHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  detailTitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
  },
  detailContent: {
    flex: 1,
    padding: SPACING.lg,
  },
  detailSection: {
    marginBottom: SPACING.lg,
  },
  detailSectionTitle: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  detailText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  breadcrumbItem: {
    marginBottom: SPACING.sm,
    paddingLeft: SPACING.sm,
    borderLeftWidth: 2,
    borderLeftColor: COLORS.border,
  },
  breadcrumbTime: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    color: COLORS.textSecondary,
  },
  breadcrumbMessage: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textPrimary,
  },
  stackTrace: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    fontFamily: 'monospace',
    color: COLORS.textSecondary,
    backgroundColor: COLORS.gray[50],
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.sm,
  },
});

export default ErrorDashboard;
