# FoodWay API - Quick Reference Guide

**Base URL:** `https://backend-production-f106.up.railway.app/api/v1`  
**Authentication:** Bearer Token in Authorization header  

## 🟢 WORKING ENDPOINTS

### Authentication
```bash
# Login
POST /api/v1/auth/login
Content-Type: application/json
{
  "email": "<EMAIL>",
  "password": "password123"
}

# Refresh Token
POST /api/v1/auth/refresh
Content-Type: application/json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}

# Logout
POST /api/v1/auth/logout
Authorization: Bearer <token>

# Forgot Password
POST /api/v1/auth/forgot-password
Content-Type: application/json
{
  "email": "<EMAIL>"
}
```

### User Management
```bash
# Get Profile
GET /api/v1/user/profile
Authorization: Bearer <token>

# Update Profile
PUT /api/v1/user/profile
Authorization: Bearer <token>
Content-Type: application/json
{
  "firstName": "<PERSON>",
  "lastName": "Doe"
}

# Get Addresses
GET /api/v1/user/addresses
Authorization: Bearer <token>

# Get Payment Methods
GET /api/v1/user/payment-methods
Authorization: Bearer <token>
```

### Restaurants
```bash
# Get All Restaurants
GET /api/v1/restaurants
# Optional: Authorization: Bearer <token>
```

### System
```bash
# Health Check
GET /health
```

## 🔴 BROKEN ENDPOINTS & FIXES

### Missing Routes (404) - Need Implementation
```bash
# Categories - NEEDS IMPLEMENTATION
GET /api/v1/categories

# User Favorites - NEEDS IMPLEMENTATION
GET /api/v1/user/favorites
POST /api/v1/user/favorites
DELETE /api/v1/user/favorites/{restaurantId}

# User Reviews - NEEDS IMPLEMENTATION
GET /api/v1/user/reviews

# Notifications - NEEDS IMPLEMENTATION
GET /api/v1/notifications
PATCH /api/v1/notifications/read-all

# Promotions - NEEDS IMPLEMENTATION
GET /api/v1/promotions
POST /api/v1/promotions/validate

# OTP Verification - NEEDS IMPLEMENTATION
POST /api/v1/auth/verify-otp
```

### Validation Errors (400) - Need Parameter Fixes
```bash
# Add Address - NEEDS CORRECT FORMAT
POST /api/v1/user/addresses
Authorization: Bearer <token>
Content-Type: application/json
{
  "street": "123 Main St",
  "city": "New York", 
  "state": "NY",
  "zipCode": "10001",
  "country": "US",
  "isDefault": false,
  "label": "Home"
}

# Restaurant Search - NEEDS PARAMETER FIX
GET /api/v1/restaurants/search?q=pizza&lat=40.7128&lng=-74.0060

# Nearby Restaurants - NEEDS PARAMETER FIX  
GET /api/v1/restaurants/nearby?lat=40.7128&lng=-74.0060&radius=5000

# Featured Restaurants - NEEDS PARAMETER FIX
GET /api/v1/restaurants/featured?limit=10

# Create Order - NEEDS CORRECT FORMAT
POST /api/v1/orders
Authorization: Bearer <token>
Content-Type: application/json
{
  "restaurantId": "uuid",
  "items": [
    {
      "menuItemId": "uuid",
      "quantity": 2,
      "price": 12.99,
      "specialInstructions": "No onions"
    }
  ],
  "deliveryAddress": {
    "street": "123 Main St",
    "city": "New York",
    "state": "NY", 
    "zipCode": "10001"
  },
  "paymentMethodId": "uuid"
}

# Create Review - NEEDS CORRECT FORMAT
POST /api/v1/reviews
Authorization: Bearer <token>
Content-Type: application/json
{
  "restaurantId": "uuid",
  "orderId": "uuid", 
  "rating": 5,
  "comment": "Great food!",
  "foodRating": 5,
  "serviceRating": 4,
  "deliveryRating": 5
}
```

### Server Errors (500) - Need Backend Fixes
```bash
# Get Orders - BACKEND LOGIC ERROR
GET /api/v1/orders
Authorization: Bearer <token>
# Returns 500 - needs debugging in backend controller
```

## 🔧 IMMEDIATE FIXES NEEDED

### 1. High Priority (Core Features)
- **Fix Orders GET endpoint** - Users can't see order history
- **Implement Categories** - Needed for restaurant browsing
- **Fix Restaurant Search** - Core discovery feature

### 2. Medium Priority (User Experience)
- **Fix Address Creation** - Needed for delivery
- **Implement Favorites** - User engagement
- **Fix Restaurant Nearby/Featured** - Enhanced discovery

### 3. Low Priority (Future Features)
- **Implement Notifications** - User engagement
- **Implement Promotions** - Marketing features
- **Add OTP Verification** - Enhanced security

## 📱 FRONTEND INTEGRATION STATUS

### ✅ Ready to Use
- User Registration & Login
- Profile Management
- Basic Restaurant Listing
- Authentication Flow

### ⚠️ Partially Working
- Restaurant Browsing (basic list works, search broken)

### ❌ Not Working
- Order Management
- User Favorites
- Reviews System
- Notifications
- Promotions

## 🚀 TESTING COMMANDS

### Test Working Endpoints
```bash
# Test Login
curl -X POST https://backend-production-f106.up.railway.app/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"TestPassword123!"}'

# Test Get Restaurants
curl https://backend-production-f106.up.railway.app/api/v1/restaurants

# Test Health Check
curl https://backend-production-f106.up.railway.app/health
```

### Test Broken Endpoints
```bash
# Test Categories (404)
curl https://backend-production-f106.up.railway.app/api/v1/categories

# Test Orders (500) - Need token
curl -H "Authorization: Bearer <token>" \
  https://backend-production-f106.up.railway.app/api/v1/orders
```

## 📋 RESPONSE FORMATS

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data here
  },
  "message": "Operation successful",
  "timestamp": "2025-07-01T11:35:39.534Z"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "details": null
  },
  "timestamp": "2025-07-01T11:35:39.534Z"
}
```

---

**Last Updated:** July 1, 2025  
**Next Review:** After backend fixes are implemented
