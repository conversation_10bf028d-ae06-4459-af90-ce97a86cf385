// FoodWay Modern Design System
// Optimized for React Native performance and visual appeal

import { Dimensions, Platform } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// 🎨 COLOR PALETTE - Modern & Appetizing
export const Colors = {
  // Primary Brand Colors
  primary: '#FF6B35',        // Vibrant Orange - Food appetite stimulating
  primaryDark: '#E55A2B',    // Darker orange for pressed states
  primaryLight: '#FF8A5C',   // Light orange for backgrounds
  primaryAlpha: 'rgba(255, 107, 53, 0.1)', // Transparent primary
  
  // Secondary Colors
  secondary: '#2ECC71',      // Fresh Green - Success, healthy
  secondaryDark: '#27AE60',  // Dark green
  secondaryLight: '#58D68D', // Light green
  
  // Accent Colors
  accent: '#F39C12',         // Golden Yellow - Premium feel
  accentDark: '#E67E22',     // Dark gold
  accentLight: '#F7DC6F',    // Light gold
  
  // Neutral Colors - Modern & Clean
  background: '#FFFFFF',     // Pure white background
  surface: '#F8F9FA',        // Light gray surface
  surfaceDark: '#E9ECEF',    // Darker surface
  
  // Text Colors
  textPrimary: '#2C3E50',    // Dark blue-gray for primary text
  textSecondary: '#7F8C8D',  // Medium gray for secondary text
  textLight: '#BDC3C7',      // Light gray for disabled text
  textWhite: '#FFFFFF',      // White text for dark backgrounds
  
  // Status Colors
  success: '#2ECC71',        // Green
  warning: '#F39C12',        // Orange
  error: '#E74C3C',          // Red
  info: '#3498DB',           // Blue
  
  // Special Colors
  shadow: 'rgba(0, 0, 0, 0.1)',     // Subtle shadows
  shadowDark: 'rgba(0, 0, 0, 0.2)',  // Darker shadows
  overlay: 'rgba(0, 0, 0, 0.5)',     // Modal overlays
  border: '#E1E8ED',                  // Light borders
  borderDark: '#D5DBDB',              // Darker borders
  
  // Rating Colors
  rating: '#FFD700',         // Gold for stars
  ratingEmpty: '#E0E0E0',    // Gray for empty stars
};

// 📝 TYPOGRAPHY - Modern & Readable
export const Typography = {
  // Font Families (Expo compatible)
  fontFamily: {
    regular: Platform.select({
      ios: 'System',
      android: 'Roboto',
    }),
    medium: Platform.select({
      ios: 'System',
      android: 'Roboto-Medium',
    }),
    bold: Platform.select({
      ios: 'System',
      android: 'Roboto-Bold',
    }),
    light: Platform.select({
      ios: 'System',
      android: 'Roboto-Light',
    }),
  },
  
  // Font Sizes - Responsive
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 28,
    '4xl': 32,
    '5xl': 36,
  },
  
  // Line Heights
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },
  
  // Font Weights
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },
};

// 📏 SPACING - Consistent & Scalable
export const Spacing = {
  xs: 4,
  sm: 8,
  md: 12,
  base: 16,
  lg: 20,
  xl: 24,
  '2xl': 32,
  '3xl': 40,
  '4xl': 48,
  '5xl': 64,
  '6xl': 80,
};

// 🔲 BORDER RADIUS - Modern & Consistent
export const BorderRadius = {
  none: 0,
  sm: 4,
  base: 8,
  md: 12,
  lg: 16,
  xl: 20,
  '2xl': 24,
  full: 9999,
};

// 🌟 SHADOWS - Subtle & Modern
export const Shadows = {
  none: {
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  sm: {
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    shadowColor: Colors.shadow,
    elevation: 2,
  },
  base: {
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    shadowColor: Colors.shadow,
    elevation: 3,
  },
  md: {
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    shadowColor: Colors.shadow,
    elevation: 4,
  },
  lg: {
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    shadowColor: Colors.shadow,
    elevation: 6,
  },
  xl: {
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    shadowColor: Colors.shadow,
    elevation: 8,
  },
};

// 📱 LAYOUT - Responsive Dimensions
export const Layout = {
  window: {
    width: screenWidth,
    height: screenHeight,
  },
  isSmallDevice: screenWidth < 375,
  isMediumDevice: screenWidth >= 375 && screenWidth < 414,
  isLargeDevice: screenWidth >= 414,
  
  // Safe areas
  statusBarHeight: Platform.select({
    ios: 44,
    android: 24,
  }),
  
  // Common dimensions
  headerHeight: 56,
  tabBarHeight: 60,
  buttonHeight: 48,
  inputHeight: 48,
  cardMinHeight: 120,
  
  // Responsive breakpoints
  breakpoints: {
    sm: 375,
    md: 414,
    lg: 768,
  },
};

// 🎭 ANIMATIONS - Smooth & Performant
export const Animations = {
  timing: {
    fast: 200,
    normal: 300,
    slow: 500,
  },
  
  easing: {
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  },
  
  // Common animation configs
  spring: {
    damping: 15,
    stiffness: 150,
  },
  
  fadeIn: {
    from: { opacity: 0 },
    to: { opacity: 1 },
  },
  
  slideUp: {
    from: { transform: [{ translateY: 50 }], opacity: 0 },
    to: { transform: [{ translateY: 0 }], opacity: 1 },
  },
  
  scale: {
    from: { transform: [{ scale: 0.9 }], opacity: 0 },
    to: { transform: [{ scale: 1 }], opacity: 1 },
  },
};

// 🎯 COMPONENT VARIANTS - Reusable Styles
export const ComponentVariants = {
  // Button variants
  button: {
    primary: {
      backgroundColor: Colors.primary,
      borderColor: Colors.primary,
    },
    secondary: {
      backgroundColor: Colors.secondary,
      borderColor: Colors.secondary,
    },
    outline: {
      backgroundColor: 'transparent',
      borderColor: Colors.primary,
      borderWidth: 1,
    },
    ghost: {
      backgroundColor: 'transparent',
      borderColor: 'transparent',
    },
  },
  
  // Card variants
  card: {
    elevated: {
      backgroundColor: Colors.background,
      ...Shadows.md,
    },
    flat: {
      backgroundColor: Colors.surface,
      borderWidth: 1,
      borderColor: Colors.border,
    },
    outlined: {
      backgroundColor: Colors.background,
      borderWidth: 1,
      borderColor: Colors.border,
    },
  },
  
  // Text variants
  text: {
    heading: {
      fontSize: Typography.fontSize['2xl'],
      fontWeight: Typography.fontWeight.bold,
      color: Colors.textPrimary,
    },
    subheading: {
      fontSize: Typography.fontSize.lg,
      fontWeight: Typography.fontWeight.semibold,
      color: Colors.textPrimary,
    },
    body: {
      fontSize: Typography.fontSize.base,
      fontWeight: Typography.fontWeight.normal,
      color: Colors.textPrimary,
    },
    caption: {
      fontSize: Typography.fontSize.sm,
      fontWeight: Typography.fontWeight.normal,
      color: Colors.textSecondary,
    },
  },
};

// 🚀 PERFORMANCE OPTIMIZATIONS
export const Performance = {
  // Image optimization
  imageProps: {
    resizeMode: 'cover',
    fadeDuration: 200,
  },
  
  // List optimization
  listProps: {
    removeClippedSubviews: true,
    maxToRenderPerBatch: 10,
    windowSize: 10,
    initialNumToRender: 5,
    getItemLayout: (data, index) => ({
      length: Layout.cardMinHeight,
      offset: Layout.cardMinHeight * index,
      index,
    }),
  },
  
  // Memory optimization
  imageCache: {
    maxCacheSize: 50, // MB
    maxCacheAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  },
};

export default {
  Colors,
  Typography,
  Spacing,
  BorderRadius,
  Shadows,
  Layout,
  Animations,
  ComponentVariants,
  Performance,
};
