import React, { useCallback, useEffect, useRef } from 'react';
import { Dimensions, PixelRatio } from 'react-native';

// Image optimization utilities
export const getOptimizedImageUrl = (
  url: string,
  width: number,
  height: number,
  quality: number = 80
): string => {
  if (!url) return '';
  
  // If it's already an optimized URL, return as is
  if (url.includes('w=') || url.includes('h=')) {
    return url;
  }
  
  // For Unsplash images, add optimization parameters
  if (url.includes('unsplash.com')) {
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}w=${width}&h=${height}&fit=crop&q=${quality}`;
  }
  
  // For other images, you might want to use a service like Cloudinary
  // or implement your own image optimization service
  return url;
};

// Get device-optimized image dimensions
export const getOptimizedImageDimensions = (
  originalWidth: number,
  originalHeight: number,
  maxWidth?: number,
  maxHeight?: number
) => {
  const screenData = Dimensions.get('window');
  const pixelRatio = PixelRatio.get();
  
  const deviceWidth = screenData.width * pixelRatio;
  const deviceHeight = screenData.height * pixelRatio;
  
  const targetMaxWidth = maxWidth || deviceWidth;
  const targetMaxHeight = maxHeight || deviceHeight;
  
  const aspectRatio = originalWidth / originalHeight;
  
  let optimizedWidth = Math.min(originalWidth, targetMaxWidth);
  let optimizedHeight = optimizedWidth / aspectRatio;
  
  if (optimizedHeight > targetMaxHeight) {
    optimizedHeight = targetMaxHeight;
    optimizedWidth = optimizedHeight * aspectRatio;
  }
  
  return {
    width: Math.round(optimizedWidth),
    height: Math.round(optimizedHeight),
  };
};

// Debounce hook for search and other frequent operations
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = React.useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Throttle hook for scroll events and other high-frequency events
export const useThrottle = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const lastRun = useRef(Date.now());

  return useCallback(
    ((...args: Parameters<T>) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args);
        lastRun.current = Date.now();
      }
    }) as T,
    [callback, delay]
  );
};

// Memoized list item component factory
export const createMemoizedListItem = <T>(
  Component: React.ComponentType<{ item: T; index: number }>
) => {
  return React.memo(Component, (prevProps, nextProps) => {
    return (
      prevProps.item === nextProps.item &&
      prevProps.index === nextProps.index
    );
  });
};

// FlatList optimization helpers
export const getItemLayout = (itemHeight: number) => 
  (data: any, index: number) => ({
    length: itemHeight,
    offset: itemHeight * index,
    index,
  });

export const keyExtractor = (item: any, index: number) => 
  item.id?.toString() || index.toString();

// Memory management utilities
export const useMemoryOptimizedState = <T>(
  initialValue: T,
  maxHistorySize: number = 10
) => {
  const [state, setState] = React.useState<T>(initialValue);
  const history = useRef<T[]>([initialValue]);

  const setOptimizedState = useCallback((newValue: T | ((prev: T) => T)) => {
    setState(prevState => {
      const nextState = typeof newValue === 'function' 
        ? (newValue as (prev: T) => T)(prevState)
        : newValue;
      
      // Manage history size
      history.current.push(nextState);
      if (history.current.length > maxHistorySize) {
        history.current = history.current.slice(-maxHistorySize);
      }
      
      return nextState;
    });
  }, [maxHistorySize]);

  const clearHistory = useCallback(() => {
    history.current = [state];
  }, [state]);

  return [state, setOptimizedState, clearHistory] as const;
};

// Bundle size optimization - lazy loading utilities
export const createLazyComponent = <T extends React.ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) => {
  const LazyComponent = React.lazy(importFunc);

  return (props: React.ComponentProps<T>) =>
    React.createElement(
      React.Suspense,
      { fallback: fallback ? React.createElement(fallback) : null },
      React.createElement(LazyComponent, props)
    );
};

// Performance monitoring utilities
export const measurePerformance = (name: string) => {
  const start = Date.now();
  
  return {
    end: () => {
      const duration = Date.now() - start;
      console.log(`Performance: ${name} took ${duration}ms`);
      return duration;
    },
  };
};

export const usePerformanceMonitor = (componentName: string) => {
  const renderCount = useRef(0);
  const mountTime = useRef(Date.now());

  useEffect(() => {
    renderCount.current += 1;
  });

  useEffect(() => {
    const mountDuration = Date.now() - mountTime.current;
    console.log(`${componentName} mounted in ${mountDuration}ms`);

    return () => {
      console.log(`${componentName} rendered ${renderCount.current} times`);
    };
  }, [componentName]);
};

// Network optimization utilities
export const createOptimizedAxios = (baseURL: string, defaultOptions: any = {}) => {
  const cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  return async (
    endpoint: string,
    options: any & { cache?: boolean; ttl?: number } = {}
  ) => {
    const { cache: useCache = true, ttl = 5 * 60 * 1000, ...axiosOptions } = options;
    const url = `${baseURL}${endpoint}`;
    const cacheKey = `${url}_${JSON.stringify(axiosOptions)}`;

    // Check cache
    if (useCache && cache.has(cacheKey)) {
      const cached = cache.get(cacheKey)!;
      if (Date.now() - cached.timestamp < cached.ttl) {
        return cached.data;
      }
      cache.delete(cacheKey);
    }

    try {
      // Import axios dynamically to avoid circular dependencies
      const { axiosInstance } = await import('../services/config/axios');
      const response = await axiosInstance.request({
        url: endpoint,
        baseURL,
        ...defaultOptions,
        ...axiosOptions,
      });

      // Cache successful responses
      if (useCache) {
        cache.set(cacheKey, {
          data: response.data,
          timestamp: Date.now(),
          ttl,
        });
      }

      return response.data;
    } catch (error) {
      console.error(`Axios error for ${url}:`, error);
      throw error;
    }
  };
};

// List virtualization helpers
export const calculateVisibleRange = (
  scrollOffset: number,
  containerHeight: number,
  itemHeight: number,
  overscan: number = 5
) => {
  const startIndex = Math.max(0, Math.floor(scrollOffset / itemHeight) - overscan);
  const endIndex = Math.min(
    Math.ceil((scrollOffset + containerHeight) / itemHeight) + overscan
  );
  
  return { startIndex, endIndex };
};

// Image caching utilities
export const preloadImages = async (urls: string[]): Promise<void> => {
  const promises = urls.map(url => {
    return new Promise<void>((resolve, reject) => {
      const image = new Image();
      image.onload = () => resolve();
      image.onerror = reject;
      image.src = url;
    });
  });

  try {
    await Promise.all(promises);
  } catch (error) {
    console.warn('Some images failed to preload:', error);
  }
};

// React Query optimization helpers
export const createOptimizedQueryKey = (
  baseKey: string,
  params: Record<string, any>
): [string, Record<string, any>] => {
  const sortedParams = Object.keys(params)
    .sort()
    .reduce((result, key) => {
      result[key] = params[key];
      return result;
    }, {} as Record<string, any>);

  return [baseKey, sortedParams];
};

export const staleTimeConfig = {
  short: 1 * 60 * 1000,      // 1 minute
  medium: 5 * 60 * 1000,     // 5 minutes
  long: 30 * 60 * 1000,      // 30 minutes
  veryLong: 60 * 60 * 1000,  // 1 hour
};

export const cacheTimeConfig = {
  short: 5 * 60 * 1000,      // 5 minutes
  medium: 30 * 60 * 1000,    // 30 minutes
  long: 60 * 60 * 1000,      // 1 hour
  veryLong: 24 * 60 * 60 * 1000, // 24 hours
};
