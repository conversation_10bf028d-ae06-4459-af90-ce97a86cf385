import { useState, useCallback, useEffect } from 'react';
import { Alert } from 'react-native';
import { AppError, errorHandler } from '../utils/errorHandler';
import { errorLogger, addBreadcrumb } from '../utils/errorLogger';
import { networkManager } from '../utils/networkManager';

export interface UseErrorHandlerOptions {
  showAlerts?: boolean;
  logErrors?: boolean;
  context?: string;
  onError?: (error: AppError) => void;
  retryable?: boolean;
}

export interface ErrorState {
  error: AppError | null;
  isRetrying: boolean;
  retryCount: number;
  hasError: boolean;
}

export interface ErrorHandlerActions {
  handleError: (error: unknown, context?: string) => AppError;
  clearError: () => void;
  retry: () => Promise<void>;
  showError: (error: AppError, title?: string) => void;
  showErrorWithRetry: (error: AppError, onRetry: () => void, title?: string) => void;
}

export const useErrorHandler = (
  options: UseErrorHandlerOptions = {}
): [ErrorState, ErrorHandlerActions] => {
  const {
    showAlerts = true,
    logErrors = true,
    context,
    onError,
    retryable = false,
  } = options;

  const [errorState, setErrorState] = useState<ErrorState>({
    error: null,
    isRetrying: false,
    retryCount: 0,
    hasError: false,
  });

  const [retryOperation, setRetryOperation] = useState<(() => Promise<void>) | null>(null);

  const handleError = useCallback((error: unknown, errorContext?: string): AppError => {
    const appError = errorHandler.handleError(error, errorContext || context);
    
    setErrorState(prev => ({
      ...prev,
      error: appError,
      hasError: true,
    }));

    // Log error if enabled
    if (logErrors) {
      errorLogger.logError(appError, errorContext || context);
    }

    // Add breadcrumb
    addBreadcrumb('system', `Error handled: ${appError.message}`, 'error', {
      errorType: appError.type,
      context: errorContext || context,
    });

    // Call custom error handler
    if (onError) {
      onError(appError);
    }

    // Show alert if enabled
    if (showAlerts && appError.type !== 'VALIDATION_ERROR') {
      Alert.alert('Error', appError.userMessage);
    }

    return appError;
  }, [context, logErrors, onError, showAlerts]);

  const clearError = useCallback(() => {
    setErrorState({
      error: null,
      isRetrying: false,
      retryCount: 0,
      hasError: false,
    });
    setRetryOperation(null);
  }, []);

  const retry = useCallback(async () => {
    if (!retryOperation || !errorState.error?.isRetryable) {
      return;
    }

    setErrorState(prev => ({
      ...prev,
      isRetrying: true,
      retryCount: prev.retryCount + 1,
    }));

    try {
      await retryOperation();
      clearError();
      addBreadcrumb('user', 'Error retry successful', 'info');
    } catch (error) {
      const appError = handleError(error, `${context}.retry`);
      setErrorState(prev => ({
        ...prev,
        isRetrying: false,
        error: appError,
      }));
    }
  }, [retryOperation, errorState.error?.isRetryable, context, handleError, clearError]);

  const showError = useCallback((error: AppError, title?: string) => {
    errorHandler.showError(error, title);
  }, []);

  const showErrorWithRetry = useCallback((error: AppError, onRetry: () => void, title?: string) => {
    setRetryOperation(() => onRetry);
    errorHandler.showErrorWithRetry(error, onRetry, title);
  }, []);

  // Auto-clear error after a certain time for non-critical errors
  useEffect(() => {
    if (errorState.error && errorState.error.type !== 'CRITICAL_ERROR') {
      const timer = setTimeout(() => {
        if (errorState.error && !errorState.isRetrying) {
          clearError();
        }
      }, 10000); // Clear after 10 seconds

      return () => clearTimeout(timer);
    }
  }, [errorState.error, errorState.isRetrying, clearError]);

  return [
    errorState,
    {
      handleError,
      clearError,
      retry,
      showError,
      showErrorWithRetry,
    },
  ];
};

// Hook for API error handling
export const useApiErrorHandler = (context?: string) => {
  const [errorState, actions] = useErrorHandler({
    context: context || 'API',
    showAlerts: true,
    logErrors: true,
  });

  const handleApiCall = useCallback(async <T>(
    apiCall: () => Promise<T>,
    options: {
      onSuccess?: (data: T) => void;
      onError?: (error: AppError) => void;
      showSuccessMessage?: string;
      retryable?: boolean;
    } = {}
  ): Promise<T | null> => {
    const { onSuccess, onError, showSuccessMessage, retryable = true } = options;

    try {
      actions.clearError();
      const result = await apiCall();
      
      if (onSuccess) {
        onSuccess(result);
      }

      if (showSuccessMessage) {
        Alert.alert('Success', showSuccessMessage);
      }

      addBreadcrumb('network', 'API call successful', 'info', { context });
      return result;
    } catch (error) {
      const appError = actions.handleError(error, context);
      
      if (onError) {
        onError(appError);
      }

      if (retryable && appError.isRetryable) {
        actions.showErrorWithRetry(appError, () => handleApiCall(apiCall, options));
      }

      return null;
    }
  }, [actions, context]);

  return {
    ...errorState,
    ...actions,
    handleApiCall,
  };
};

// Hook for form error handling
export const useFormErrorHandler = (context?: string) => {
  const [errorState, actions] = useErrorHandler({
    context: context || 'Form',
    showAlerts: false, // Forms usually show inline errors
    logErrors: true,
  });

  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});

  const handleFormError = useCallback((error: unknown, field?: string) => {
    const appError = actions.handleError(error, context);
    
    if (appError.type === 'VALIDATION_ERROR' && field) {
      setFieldErrors(prev => ({
        ...prev,
        [field]: appError.userMessage,
      }));
    }

    return appError;
  }, [actions, context]);

  const clearFieldError = useCallback((field: string) => {
    setFieldErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  const clearAllFieldErrors = useCallback(() => {
    setFieldErrors({});
  }, []);

  const hasFieldError = useCallback((field: string): boolean => {
    return Boolean(fieldErrors[field]);
  }, [fieldErrors]);

  const getFieldError = useCallback((field: string): string | undefined => {
    return fieldErrors[field];
  }, [fieldErrors]);

  return {
    ...errorState,
    ...actions,
    fieldErrors,
    handleFormError,
    clearFieldError,
    clearAllFieldErrors,
    hasFieldError,
    getFieldError,
  };
};

// Hook for network-aware error handling
export const useNetworkErrorHandler = (context?: string) => {
  const [errorState, actions] = useErrorHandler({
    context: context || 'Network',
    showAlerts: true,
    logErrors: true,
  });

  const [networkStatus, setNetworkStatus] = useState(networkManager.getNetworkStatus());

  useEffect(() => {
    const unsubscribe = networkManager.addNetworkListener((status, eventType) => {
      setNetworkStatus(status);
      
      if (eventType === 'connected' && errorState.error?.type === 'NETWORK_ERROR') {
        // Auto-retry network operations when connection is restored
        if (errorState.error.isRetryable) {
          actions.retry();
        }
      }
    });

    return unsubscribe;
  }, [errorState.error, actions]);

  const handleNetworkCall = useCallback(async <T>(
    networkCall: () => Promise<T>,
    options: {
      queueWhenOffline?: boolean;
      priority?: 'low' | 'normal' | 'high';
    } = {}
  ): Promise<T | null> => {
    const { queueWhenOffline = true, priority = 'normal' } = options;

    if (!networkStatus.isConnected && queueWhenOffline) {
      // Queue the request for when network is available
      try {
        await networkManager.queueRequest(
          'queued_operation',
          { method: 'POST' },
          priority
        );
        addBreadcrumb('network', 'Request queued for offline processing', 'info');
        return null;
      } catch (error) {
        actions.handleError(error, `${context}.queueRequest`);
        return null;
      }
    }

    try {
      const result = await networkCall();
      return result;
    } catch (error) {
      actions.handleError(error, context);
      return null;
    }
  }, [networkStatus.isConnected, actions, context]);

  return {
    ...errorState,
    ...actions,
    networkStatus,
    handleNetworkCall,
  };
};

export default useErrorHandler;
