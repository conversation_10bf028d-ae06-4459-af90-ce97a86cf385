import { config } from 'dotenv';

// Load environment variables
config();

export interface DatabaseConfig {
  url: string;
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl: boolean;
  maxConnections: number;
  idleTimeoutMillis: number;
}

export interface RedisConfig {
  url: string;
  host: string;
  port: number;
  password?: string;
  db: number;
  retryDelayOnFailover: number;
  maxRetriesPerRequest: number;
  lazyConnect: boolean;
}

export interface AppConfig {
  nodeEnv: string;
  port: number;
  apiUrl: string;
  appName: string;
  appVersion: string;
  supportEmail: string;
}

export interface AuthConfig {
  jwtSecret: string;
  jwtExpiresIn: string;
  refreshTokenSecret: string;
  refreshTokenExpiresIn: string;
  bcryptRounds: number;
  sessionSecret: string;
  sessionMaxAge: number;
}

export interface PaymentConfig {
  stripe: {
    publishableKey: string;
    secretKey: string;
    webhookSecret: string;
  };
}

export interface ExternalServicesConfig {
  googleMaps: {
    apiKey: string;
  };
  firebase: {
    serverKey: string;
    projectId: string;
  };
  email: {
    apiKey: string;
    fromAddress: string;
  };
  cloudinary: {
    cloudName: string;
    apiKey: string;
    apiSecret: string;
  };
  twilio: {
    accountSid: string;
    authToken: string;
    phoneNumber: string;
  };
}

export interface SecurityConfig {
  cors: {
    origin: string[];
    credentials: boolean;
  };
  rateLimit: {
    windowMs: number;
    maxRequests: number;
  };
  fileUpload: {
    maxFileSize: number;
    allowedTypes: string[];
  };
}

class EnvironmentConfig {
  private static instance: EnvironmentConfig;

  public readonly app: AppConfig;
  public readonly database: DatabaseConfig;
  public readonly redis: RedisConfig;
  public readonly auth: AuthConfig;
  public readonly payment: PaymentConfig;
  public readonly externalServices: ExternalServicesConfig;
  public readonly security: SecurityConfig;

  private constructor() {
    this.app = this.getAppConfig();
    this.database = this.getDatabaseConfig();
    this.redis = this.getRedisConfig();
    this.auth = this.getAuthConfig();
    this.payment = this.getPaymentConfig();
    this.externalServices = this.getExternalServicesConfig();
    this.security = this.getSecurityConfig();
  }

  public static getInstance(): EnvironmentConfig {
    if (!EnvironmentConfig.instance) {
      EnvironmentConfig.instance = new EnvironmentConfig();
    }
    return EnvironmentConfig.instance;
  }

  private getAppConfig(): AppConfig {
    return {
      nodeEnv: process.env.NODE_ENV || 'development',
      port: parseInt(process.env.PORT || '3000', 10),
      apiUrl: process.env.EXPO_PUBLIC_API_URL || 'https://backend-production-f106.up.railway.app/api/v1',
      appName: process.env.APP_NAME || 'FoodWay',
      appVersion: process.env.APP_VERSION || '1.0.0',
      supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
    };
  }

  private getDatabaseConfig(): DatabaseConfig {
    const databaseUrl = process.env.DATABASE_URL;
    
    if (databaseUrl) {
      // Parse Railway DATABASE_URL
      const url = new URL(databaseUrl);
      return {
        url: databaseUrl,
        host: url.hostname,
        port: parseInt(url.port || '5432', 10),
        database: url.pathname.slice(1),
        username: url.username,
        password: url.password,
        ssl: this.app.nodeEnv === 'production',
        maxConnections: 20,
        idleTimeoutMillis: 30000,
      };
    }

    // Fallback to individual environment variables
    return {
      url: '',
      host: process.env.POSTGRES_HOST || 'localhost',
      port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
      database: process.env.POSTGRES_DB || 'foodway_db',
      username: process.env.POSTGRES_USER || 'foodway_user',
      password: process.env.POSTGRES_PASSWORD || '',
      ssl: this.app.nodeEnv === 'production',
      maxConnections: 20,
      idleTimeoutMillis: 30000,
    };
  }

  private getRedisConfig(): RedisConfig {
    const redisUrl = process.env.REDIS_URL;
    
    if (redisUrl) {
      // Parse Railway REDIS_URL
      const url = new URL(redisUrl);
      return {
        url: redisUrl,
        host: url.hostname,
        port: parseInt(url.port || '6379', 10),
        password: url.password || undefined,
        db: 0,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
      };
    }

    // Fallback to individual environment variables
    return {
      url: '',
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379', 10),
      password: process.env.REDIS_PASSWORD || undefined,
      db: 0,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
    };
  }

  private getAuthConfig(): AuthConfig {
    return {
      jwtSecret: process.env.JWT_SECRET || 'your-jwt-secret',
      jwtExpiresIn: process.env.JWT_EXPIRES_IN || '7d',
      refreshTokenSecret: process.env.REFRESH_TOKEN_SECRET || 'your-refresh-secret',
      refreshTokenExpiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN || '30d',
      bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12', 10),
      sessionSecret: process.env.SESSION_SECRET || 'your-session-secret',
      sessionMaxAge: parseInt(process.env.SESSION_MAX_AGE || '86400000', 10),
    };
  }

  private getPaymentConfig(): PaymentConfig {
    return {
      stripe: {
        publishableKey: process.env.STRIPE_PUBLISHABLE_KEY || '',
        secretKey: process.env.STRIPE_SECRET_KEY || '',
        webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || '',
      },
    };
  }

  private getExternalServicesConfig(): ExternalServicesConfig {
    return {
      googleMaps: {
        apiKey: process.env.GOOGLE_MAPS_API_KEY || '',
      },
      firebase: {
        serverKey: process.env.FIREBASE_SERVER_KEY || '',
        projectId: process.env.FIREBASE_PROJECT_ID || '',
      },
      email: {
        apiKey: process.env.EMAIL_SERVICE_API_KEY || '',
        fromAddress: process.env.EMAIL_FROM || '<EMAIL>',
      },
      cloudinary: {
        cloudName: process.env.CLOUDINARY_CLOUD_NAME || '',
        apiKey: process.env.CLOUDINARY_API_KEY || '',
        apiSecret: process.env.CLOUDINARY_API_SECRET || '',
      },
      twilio: {
        accountSid: process.env.TWILIO_ACCOUNT_SID || '',
        authToken: process.env.TWILIO_AUTH_TOKEN || '',
        phoneNumber: process.env.TWILIO_PHONE_NUMBER || '',
      },
    };
  }

  private getSecurityConfig(): SecurityConfig {
    return {
      cors: {
        origin: (process.env.CORS_ORIGIN || 'https://backend-production-f106.up.railway.app,http://localhost:3000').split(','),
        credentials: process.env.CORS_CREDENTIALS === 'true',
      },
      rateLimit: {
        windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10),
        maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
      },
      fileUpload: {
        maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '********', 10),
        allowedTypes: (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/gif').split(','),
      },
    };
  }

  public isProduction(): boolean {
    return this.app.nodeEnv === 'production';
  }

  public isDevelopment(): boolean {
    return this.app.nodeEnv === 'development';
  }

  public isStaging(): boolean {
    return this.app.nodeEnv === 'staging';
  }
}

// Export singleton instance
export const env = EnvironmentConfig.getInstance();

// Export individual configs for convenience
export const {
  app: appConfig,
  database: databaseConfig,
  redis: redisConfig,
  auth: authConfig,
  payment: paymentConfig,
  externalServices: externalServicesConfig,
  security: securityConfig,
} = env;
