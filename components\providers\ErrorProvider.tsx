import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { Alert, AppState, AppStateStatus } from 'react-native';
import { AppError, errorHandler } from '../../utils/errorHandler';
import { addBreadcrumb, errorLogger } from '../../utils/errorLogger';
import { networkManager } from '../../utils/networkManager';
import { ErrorDashboard } from '../debug/ErrorDashboard';
import { ErrorReportModal } from '../ui/ErrorReportModal';

interface ErrorContextType {
  reportError: (error: AppError) => void;
  showErrorReport: (error: AppError) => void;
  showDebugDashboard: () => void;
  clearAllErrors: () => void;
  isOnline: boolean;
  errorCount: number;
}

const ErrorContext = createContext<ErrorContextType | undefined>(undefined);

interface ErrorProviderProps {
  children: ReactNode;
  enableDebugMode?: boolean;
  enableCrashReporting?: boolean;
  maxErrorsBeforeAlert?: number;
}

export const ErrorProvider: React.FC<ErrorProviderProps> = ({
  children,
  enableDebugMode = __DEV__,
  enableCrashReporting = !__DEV__,
  maxErrorsBeforeAlert = 5,
}) => {
  const [errorReportModal, setErrorReportModal] = useState<{
    visible: boolean;
    error: AppError | null;
  }>({ visible: false, error: null });
  
  const [debugDashboard, setDebugDashboard] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const [errorCount, setErrorCount] = useState(0);
  const [sessionErrors, setSessionErrors] = useState<AppError[]>([]);

  useEffect(() => {
    // Initialize error logging
    addBreadcrumb('system', 'ErrorProvider initialized', 'info');

    // Set up network monitoring
    const unsubscribeNetwork = networkManager.addNetworkListener((status, eventType) => {
      setIsOnline(status.isConnected);
      
      if (eventType === 'connected') {
        addBreadcrumb('network', 'Network connection restored', 'info');
      } else if (eventType === 'disconnected') {
        addBreadcrumb('network', 'Network connection lost', 'warning');
      }
    });

    // Set up app state monitoring
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      addBreadcrumb('system', `App state changed to: ${nextAppState}`, 'info');
      
      if (nextAppState === 'background') {
        // App going to background - save error state
        saveErrorState();
      } else if (nextAppState === 'active') {
        // App coming to foreground - restore error state
        restoreErrorState();
      }
    };

    const appStateSubscription = AppState.addEventListener('change', handleAppStateChange);

    // Set up global error handlers
    setupGlobalErrorHandlers();

    return () => {
      unsubscribeNetwork();
      appStateSubscription?.remove();
    };
  }, []);

  useEffect(() => {
    // Monitor error count and show alert if too many errors
    if (errorCount >= maxErrorsBeforeAlert && enableDebugMode) {
      Alert.alert(
        'Multiple Errors Detected',
        `${errorCount} errors have occurred in this session. Would you like to view the error dashboard?`,
        [
          { text: 'Dismiss', style: 'cancel' },
          { text: 'View Dashboard', onPress: () => setDebugDashboard(true) },
        ]
      );
    }
  }, [errorCount, maxErrorsBeforeAlert, enableDebugMode]);

  const setupGlobalErrorHandlers = () => {
    // Handle unhandled promise rejections
    if (typeof window !== 'undefined') {
      const originalHandler = window.onunhandledrejection;
      
      window.onunhandledrejection = (event) => {
        const error = new AppError(
          'UNHANDLED_PROMISE_REJECTION',
          event.reason?.message || 'Unhandled promise rejection',
          'An unexpected error occurred. The app may not function correctly.'
        );
        
        reportError(error);
        
        if (originalHandler) {
          originalHandler.call(window, event);
        }
      };
    }

    // Handle React Native errors
    const originalConsoleError = console.error;
    console.error = (...args) => {
      const errorMessage = args.join(' ');
      
      // Filter out development warnings
      if (!errorMessage.includes('Warning:') && !errorMessage.includes('[WARN]')) {
        addBreadcrumb('system', `Console error: ${errorMessage}`, 'error');
        
        // If it looks like a critical error, report it
        if (errorMessage.includes('Error:') || errorMessage.includes('Exception:')) {
          const error = new AppError(
            'CONSOLE_ERROR',
            errorMessage,
            'A system error was detected. Please restart the app if you experience issues.'
          );
          reportError(error);
        }
      }
      
      originalConsoleError.apply(console, args);
    };
  };

  const reportError = async (error: AppError) => {
    try {
      // Increment error count
      setErrorCount(prev => prev + 1);
      setSessionErrors(prev => [...prev, error]);

      // Log the error
      await errorLogger.logError(error, 'ErrorProvider.reportError');

      // Add breadcrumb
      addBreadcrumb('system', `Error reported: ${error.type}`, 'error', {
        errorMessage: error.message,
        errorCode: error.code,
      });

      // Send to crash reporting service if enabled
      if (enableCrashReporting && isOnline) {
        sendToCrashReporting(error);
      }

      // Show critical error alerts
      if (error.type === 'CRITICAL_ERROR' || error.message.includes('crash')) {
        Alert.alert(
          'Critical Error',
          'A critical error has occurred. The app may need to be restarted.',
          [
            { text: 'Report Issue', onPress: () => showErrorReport(error) },
            { text: 'Restart App', onPress: () => restartApp() },
          ]
        );
      }
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  const showErrorReport = (error: AppError) => {
    setErrorReportModal({ visible: true, error });
  };

  const showDebugDashboard = () => {
    if (enableDebugMode) {
      setDebugDashboard(true);
    }
  };

  const clearAllErrors = async () => {
    try {
      await errorLogger.clearErrorLogs();
      setErrorCount(0);
      setSessionErrors([]);
      addBreadcrumb('user', 'All errors cleared', 'info');
    } catch (error) {
      console.error('Failed to clear errors:', error);
    }
  };

  const saveErrorState = async () => {
    try {
      const errorState = {
        errorCount,
        sessionErrors: sessionErrors.slice(-10), // Save last 10 errors
        timestamp: new Date().toISOString(),
      };
      
      // Save to storage for recovery
      await require('../../utils/storage').storeData('error_provider_state', errorState);
    } catch (error) {
      console.error('Failed to save error state:', error);
    }
  };

  const restoreErrorState = async () => {
    try {
      const savedState = await require('../../utils/storage').retrieveData('error_provider_state');
      
      if (savedState && savedState.timestamp) {
        const timeDiff = Date.now() - new Date(savedState.timestamp).getTime();
        
        // Only restore if less than 1 hour old
        if (timeDiff < 3600000) {
          setErrorCount(savedState.errorCount || 0);
          setSessionErrors(savedState.sessionErrors || []);
        }
      }
    } catch (error) {
      console.error('Failed to restore error state:', error);
    }
  };

  const sendToCrashReporting = async (error: AppError) => {
    try {
      // Queue crash report for sending
      await networkManager.queueRequest(
        '/api/crash-reports',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          data: {
            error: {
              type: error.type,
              message: error.message,
              stack: error.stack,
              code: error.code,
            },
            timestamp: new Date().toISOString(),
            platform: require('react-native').Platform.OS,
            version: require('../../package.json').version,
          },
        },
        'high'
      );
    } catch (reportingError) {
      console.error('Failed to send crash report:', reportingError);
    }
  };

  const restartApp = () => {
    // In a real app, you might use a library like react-native-restart
    Alert.alert(
      'Restart Required',
      'Please close and reopen the app to continue.',
      [{ text: 'OK' }]
    );
  };

  const handleErrorReportSubmit = (feedbackId: string) => {
    setErrorReportModal({ visible: false, error: null });
    addBreadcrumb('user', `Error feedback submitted: ${feedbackId}`, 'info');
  };

  const contextValue: ErrorContextType = {
    reportError,
    showErrorReport,
    showDebugDashboard,
    clearAllErrors,
    isOnline,
    errorCount,
  };

  return (
    <ErrorContext.Provider value={contextValue}>
      {children}
      
      {/* Error Report Modal */}
      <ErrorReportModal
        visible={errorReportModal.visible}
        error={errorReportModal.error}
        onClose={() => setErrorReportModal({ visible: false, error: null })}
        onSubmit={handleErrorReportSubmit}
      />
      
      {/* Debug Dashboard */}
      {enableDebugMode && (
        <ErrorDashboard
          visible={debugDashboard}
          onClose={() => setDebugDashboard(false)}
        />
      )}
    </ErrorContext.Provider>
  );
};

// Hook to use the error context
export const useErrorContext = (): ErrorContextType => {
  const context = useContext(ErrorContext);
  if (!context) {
    throw new Error('useErrorContext must be used within an ErrorProvider');
  }
  return context;
};

// HOC to wrap components with error reporting
export const withErrorReporting = <P extends object>(
  Component: React.ComponentType<P>
) => {
  return React.forwardRef<any, P>((props, ref) => {
    const { reportError } = useErrorContext();
    
    const handleError = (error: Error, errorInfo?: any) => {
      const appError = errorHandler.handleError(error, `${Component.displayName || Component.name}.render`);
      reportError(appError);
    };

    return (
      <ErrorBoundary onError={handleError}>
        <Component {...props} ref={ref} />
      </ErrorBoundary>
    );
  });
};

// Simple error boundary for the HOC
class ErrorBoundary extends React.Component<
  { children: React.ReactNode; onError: (error: Error, errorInfo: any) => void },
  { hasError: boolean }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    this.props.onError(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return null; // or a fallback UI
    }

    return this.props.children;
  }
}

export default ErrorProvider;
