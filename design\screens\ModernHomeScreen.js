// Modern Home Screen Design for FoodWay App
// Optimized for performance and visual appeal

import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  Animated,
  RefreshControl,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import DesignSystem from '../DesignSystem';
import { RestaurantCard, CategoryChip, FoodCard } from '../ModernComponents';

const { Colors, Typography, Spacing, BorderRadius, Shadows, Layout } = DesignSystem;

const ModernHomeScreen = ({ navigation }) => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [refreshing, setRefreshing] = useState(false);
  const [currentLocation, setCurrentLocation] = useState('Downtown, Karachi');
  const scrollY = useRef(new Animated.Value(0)).current;

  // Sample data - replace with actual API data
  const categories = [
    { id: 'all', name: 'All', icon: 'https://via.placeholder.com/40x40' },
    { id: 'pizza', name: 'Pizza', icon: 'https://via.placeholder.com/40x40' },
    { id: 'burger', name: 'Burgers', icon: 'https://via.placeholder.com/40x40' },
    { id: 'chinese', name: 'Chinese', icon: 'https://via.placeholder.com/40x40' },
    { id: 'dessert', name: 'Desserts', icon: 'https://via.placeholder.com/40x40' },
    { id: 'drinks', name: 'Drinks', icon: 'https://via.placeholder.com/40x40' },
  ];

  const featuredRestaurants = [
    {
      id: '1',
      name: 'Sushi Zen',
      cuisine: 'Japanese • Sushi',
      rating: 4.8,
      reviews: 1200,
      deliveryTime: '25-35 min',
      deliveryFee: 0,
      image: 'https://via.placeholder.com/300x180',
      isPromoted: true,
    },
    {
      id: '2',
      name: 'Pizza Palace',
      cuisine: 'Italian • Pizza',
      rating: 4.6,
      reviews: 890,
      deliveryTime: '30-40 min',
      deliveryFee: 50,
      image: 'https://via.placeholder.com/300x180',
      isPromoted: false,
    },
  ];

  const trendingItems = [
    {
      id: '1',
      name: 'Chicken Biryani',
      description: 'Aromatic basmati rice with tender chicken',
      price: 450,
      originalPrice: 500,
      rating: 4.7,
      image: 'https://via.placeholder.com/200x160',
      discount: 10,
      isVeg: false,
    },
    {
      id: '2',
      name: 'Margherita Pizza',
      description: 'Fresh mozzarella, tomatoes, and basil',
      price: 650,
      rating: 4.5,
      image: 'https://via.placeholder.com/200x160',
      isVeg: true,
    },
  ];

  const nearbyRestaurants = [
    {
      id: '3',
      name: 'Burger House',
      cuisine: 'American • Fast Food',
      rating: 4.4,
      reviews: 567,
      deliveryTime: '20-30 min',
      deliveryFee: 30,
      image: 'https://via.placeholder.com/300x180',
    },
    {
      id: '4',
      name: 'Desi Dhaba',
      cuisine: 'Pakistani • Traditional',
      rating: 4.7,
      reviews: 1450,
      deliveryTime: '35-45 min',
      deliveryFee: 0,
      image: 'https://via.placeholder.com/300x180',
    },
  ];

  const onRefresh = () => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  };

  const handleCategoryPress = (categoryId) => {
    setSelectedCategory(categoryId);
    // Filter restaurants/items based on category
  };

  const handleRestaurantPress = (restaurant) => {
    navigation.navigate('Restaurant', { restaurant });
  };

  const handleFoodPress = (item) => {
    navigation.navigate('FoodDetail', { item });
  };

  const handleAddToCart = (item) => {
    // Add to cart logic
    console.log('Added to cart:', item);
  };

  const headerOpacity = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
      
      {/* Animated Header */}
      <Animated.View style={[styles.header, { opacity: headerOpacity }]}>
        <LinearGradient
          colors={[Colors.background, Colors.surface]}
          style={styles.headerGradient}
        />
      </Animated.View>

      {/* Fixed Top Bar */}
      <View style={styles.topBar}>
        <TouchableOpacity 
          style={styles.locationContainer}
          onPress={() => navigation.navigate('LocationPicker')}
        >
          <Ionicons name="location" size={20} color={Colors.primary} />
          <View style={styles.locationTextContainer}>
            <Text style={styles.locationLabel}>Deliver to</Text>
            <Text style={styles.locationText} numberOfLines={1}>
              {currentLocation}
            </Text>
          </View>
          <Ionicons name="chevron-down" size={16} color={Colors.textSecondary} />
        </TouchableOpacity>

        <View style={styles.topBarActions}>
          <TouchableOpacity 
            style={styles.searchButton}
            onPress={() => navigation.navigate('Search')}
          >
            <Ionicons name="search" size={20} color={Colors.textSecondary} />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.cartButton}
            onPress={() => navigation.navigate('Cart')}
          >
            <Ionicons name="bag" size={20} color={Colors.textSecondary} />
            <View style={styles.cartBadge}>
              <Text style={styles.cartBadgeText}>3</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>

      <Animated.ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[Colors.primary]}
            tintColor={Colors.primary}
          />
        }
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: false }
        )}
        scrollEventThrottle={16}
      >
        {/* Welcome Section */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeText}>Good afternoon! 👋</Text>
          <Text style={styles.welcomeSubtext}>What would you like to eat today?</Text>
        </View>

        {/* Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Categories</Text>
          <FlatList
            data={categories}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.categoriesContainer}
            renderItem={({ item }) => (
              <CategoryChip
                category={item}
                isSelected={selectedCategory === item.id}
                onPress={() => handleCategoryPress(item.id)}
              />
            )}
          />
        </View>

        {/* Featured Restaurants */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Featured Restaurants</Text>
            <TouchableOpacity onPress={() => navigation.navigate('AllRestaurants')}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={featuredRestaurants}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.horizontalList}
            renderItem={({ item }) => (
              <RestaurantCard
                restaurant={item}
                onPress={() => handleRestaurantPress(item)}
                style={styles.featuredRestaurantCard}
              />
            )}
          />
        </View>

        {/* Trending Now */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Trending Now 🔥</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Trending')}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={trendingItems}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.horizontalList}
            renderItem={({ item }) => (
              <FoodCard
                item={item}
                onPress={() => handleFoodPress(item)}
                onAddToCart={handleAddToCart}
                style={styles.trendingFoodCard}
              />
            )}
          />
        </View>

        {/* Nearby Restaurants */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Nearby Restaurants</Text>
            <TouchableOpacity onPress={() => navigation.navigate('AllRestaurants')}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          
          {nearbyRestaurants.map((restaurant) => (
            <RestaurantCard
              key={restaurant.id}
              restaurant={restaurant}
              onPress={() => handleRestaurantPress(restaurant)}
              style={styles.nearbyRestaurantCard}
            />
          ))}
        </View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </Animated.ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: Layout.statusBarHeight + Layout.headerHeight,
    zIndex: 10,
  },
  headerGradient: {
    flex: 1,
  },
  topBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.base,
    paddingTop: Layout.statusBarHeight,
    paddingBottom: Spacing.base,
    backgroundColor: Colors.background,
    ...Shadows.sm,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: Spacing.base,
  },
  locationTextContainer: {
    marginLeft: Spacing.sm,
    flex: 1,
  },
  locationLabel: {
    fontSize: Typography.fontSize.xs,
    color: Colors.textSecondary,
  },
  locationText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.textPrimary,
  },
  topBarActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.sm,
  },
  cartButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  cartBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartBadgeText: {
    fontSize: Typography.fontSize.xs,
    color: Colors.textWhite,
    fontWeight: Typography.fontWeight.bold,
  },
  scrollView: {
    flex: 1,
  },
  welcomeSection: {
    paddingHorizontal: Spacing.base,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.base,
  },
  welcomeText: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  welcomeSubtext: {
    fontSize: Typography.fontSize.base,
    color: Colors.textSecondary,
  },
  section: {
    marginBottom: Spacing.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.base,
    marginBottom: Spacing.base,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.textPrimary,
  },
  seeAllText: {
    fontSize: Typography.fontSize.base,
    color: Colors.primary,
    fontWeight: Typography.fontWeight.semibold,
  },
  categoriesContainer: {
    paddingHorizontal: Spacing.base,
  },
  horizontalList: {
    paddingHorizontal: Spacing.base,
  },
  featuredRestaurantCard: {
    width: 280,
    marginRight: Spacing.base,
  },
  trendingFoodCard: {
    width: 200,
    marginRight: Spacing.base,
  },
  nearbyRestaurantCard: {
    marginHorizontal: Spacing.base,
  },
  bottomSpacing: {
    height: Layout.tabBarHeight + Spacing.base,
  },
});

export default ModernHomeScreen;
