import { Restaurant, MenuItem, MenuCategory } from '../../types';

// Mock Menu Categories
export const mockMenuCategories: MenuCategory[] = [
  { id: 'cat1', name: 'Pizza', restaurantId: '1' },
  { id: 'cat2', name: 'Appetizers', restaurantId: '1' },
  { id: 'cat3', name: '<PERSON><PERSON><PERSON>', restaurantId: '1' },
  { id: 'cat4', name: 'Burgers', restaurantId: '2' },
  { id: 'cat5', name: 'Sides', restaurantId: '2' },
  { id: 'cat6', name: 'Sushi Rolls', restaurantId: '3' },
  { id: 'cat7', name: '<PERSON><PERSON><PERSON>', restaurantId: '3' },
  { id: 'cat8', name: 'Tacos', restaurantId: '4' },
  { id: 'cat9', name: '<PERSON><PERSON><PERSON>', restaurantId: '4' },
];

// Mock Menu Items
export const mockMenuItems: MenuItem[] = [
  // <PERSON>'s Pizza Palace
  {
    id: 'item1',
    restaurantId: '1',
    categoryId: 'cat1',
    name: 'Margherita Pizza',
    description: 'Classic pizza with fresh mozzarella, tomato sauce, and basil',
    image: 'https://images.unsplash.com/photo-1604382354936-07c5d9983bd3?w=300&h=200&fit=crop',
    price: 16.99,
    isAvailable: true,
    isVegetarian: true,
    isVegan: false,
    isGlutenFree: false,
    isSpicy: false,
    calories: 280,
    preparationTime: '15-20 min',
    customizations: [
      {
        id: 'size',
        name: 'Size',
        type: 'single',
        required: true,
        options: [
          { id: 'small', name: 'Small (10")', price: 0, isDefault: true },
          { id: 'medium', name: 'Medium (12")', price: 3 },
          { id: 'large', name: 'Large (14")', price: 6 },
        ],
      },
      {
        id: 'crust',
        name: 'Crust Type',
        type: 'single',
        required: true,
        options: [
          { id: 'thin', name: 'Thin Crust', price: 0, isDefault: true },
          { id: 'thick', name: 'Thick Crust', price: 2 },
          { id: 'stuffed', name: 'Stuffed Crust', price: 4 },
        ],
      },
    ],
    addOns: [
      { id: 'extra-cheese', name: 'Extra Cheese', price: 2.50 },
      { id: 'pepperoni', name: 'Pepperoni', price: 3.00 },
      { id: 'mushrooms', name: 'Mushrooms', price: 1.50 },
    ],
    tags: ['Popular', 'Vegetarian'],
  },
  {
    id: 'item2',
    restaurantId: '1',
    categoryId: 'cat2',
    name: 'Garlic Bread',
    description: 'Crispy bread with garlic butter and herbs',
    image: 'https://images.unsplash.com/photo-1573140247632-f8fd74997d5c?w=300&h=200&fit=crop',
    price: 6.99,
    isAvailable: true,
    isVegetarian: true,
    isVegan: false,
    isGlutenFree: false,
    isSpicy: false,
    calories: 180,
    preparationTime: '8-10 min',
    customizations: [],
    addOns: [
      { id: 'cheese-dip', name: 'Cheese Dip', price: 1.50 },
      { id: 'marinara', name: 'Marinara Sauce', price: 1.00 },
    ],
    tags: ['Appetizer'],
  },
  // Burger Junction
  {
    id: 'item3',
    restaurantId: '2',
    categoryId: 'cat4',
    name: 'Classic Cheeseburger',
    description: 'Juicy beef patty with cheese, lettuce, tomato, and special sauce',
    image: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=300&h=200&fit=crop',
    price: 12.99,
    isAvailable: true,
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: false,
    isSpicy: false,
    calories: 520,
    preparationTime: '12-15 min',
    customizations: [
      {
        id: 'patty',
        name: 'Patty',
        type: 'single',
        required: true,
        options: [
          { id: 'beef', name: 'Beef Patty', price: 0, isDefault: true },
          { id: 'chicken', name: 'Chicken Patty', price: 1 },
          { id: 'veggie', name: 'Veggie Patty', price: 0 },
        ],
      },
    ],
    addOns: [
      { id: 'bacon', name: 'Bacon', price: 2.50 },
      { id: 'avocado', name: 'Avocado', price: 2.00 },
      { id: 'extra-patty', name: 'Extra Patty', price: 4.00 },
    ],
    tags: ['Popular', 'Bestseller'],
  },
  // Sakura Sushi
  {
    id: 'item4',
    restaurantId: '3',
    categoryId: 'cat6',
    name: 'California Roll',
    description: 'Crab, avocado, and cucumber with sesame seeds',
    image: 'https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=300&h=200&fit=crop',
    price: 8.99,
    isAvailable: true,
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: true,
    isSpicy: false,
    calories: 255,
    preparationTime: '10-12 min',
    customizations: [],
    addOns: [
      { id: 'wasabi', name: 'Extra Wasabi', price: 0.50 },
      { id: 'ginger', name: 'Pickled Ginger', price: 0.50 },
    ],
    tags: ['Gluten-Free', 'Fresh'],
  },
  // Taco Fiesta
  {
    id: 'item5',
    restaurantId: '4',
    categoryId: 'cat8',
    name: 'Chicken Tacos',
    description: 'Grilled chicken with onions, cilantro, and lime',
    image: 'https://images.unsplash.com/photo-1565299507177-b0ac66763828?w=300&h=200&fit=crop',
    price: 9.99,
    isAvailable: true,
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: true,
    isSpicy: true,
    calories: 320,
    preparationTime: '8-10 min',
    customizations: [
      {
        id: 'spice-level',
        name: 'Spice Level',
        type: 'single',
        required: false,
        options: [
          { id: 'mild', name: 'Mild', price: 0, isDefault: true },
          { id: 'medium', name: 'Medium', price: 0 },
          { id: 'hot', name: 'Hot', price: 0 },
        ],
      },
    ],
    addOns: [
      { id: 'guacamole', name: 'Guacamole', price: 2.00 },
      { id: 'sour-cream', name: 'Sour Cream', price: 1.00 },
    ],
    tags: ['Spicy', 'Gluten-Free'],
  },
];

// Mock Restaurants
export const mockRestaurants: Restaurant[] = [
  {
    id: '1',
    name: 'Mario\'s Pizza Palace',
    description: 'Authentic Italian pizza made with fresh ingredients and traditional recipes passed down through generations.',
    image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop',
    coverImage: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400&h=200&fit=crop',
    cuisine: ['Italian', 'Pizza'],
    rating: 4.5,
    reviewCount: 324,
    deliveryTime: '25-35 min',
    deliveryFee: 2.99,
    minimumOrder: 15,
    isOpen: true,
    address: '123 Main St, Downtown',
    latitude: 37.7749,
    longitude: -122.4194,
    phone: '******-0123',
    categories: mockMenuCategories.filter(cat => cat.restaurantId === '1'),
    featured: true,
    promoted: false,
    tags: ['Popular', 'Fast Delivery'],
  },
  {
    id: '2',
    name: 'Burger Junction',
    description: 'Gourmet burgers and crispy fries made with premium ingredients',
    image: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=400&h=300&fit=crop',
    coverImage: 'https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=400&h=200&fit=crop',
    cuisine: ['American', 'Burgers'],
    rating: 4.2,
    reviewCount: 189,
    deliveryTime: '20-30 min',
    deliveryFee: 1.99,
    minimumOrder: 12,
    isOpen: true,
    address: '456 Oak Ave, Midtown',
    latitude: 37.7849,
    longitude: -122.4094,
    phone: '******-0124',
    categories: mockMenuCategories.filter(cat => cat.restaurantId === '2'),
    featured: false,
    promoted: true,
    tags: ['Bestseller', 'Quick Service'],
  },
  {
    id: '3',
    name: 'Sakura Sushi',
    description: 'Fresh sushi and Japanese cuisine prepared by expert chefs',
    image: 'https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=400&h=300&fit=crop',
    coverImage: 'https://images.unsplash.com/photo-1553621042-f6e147245754?w=400&h=200&fit=crop',
    cuisine: ['Japanese', 'Sushi'],
    rating: 4.7,
    reviewCount: 267,
    deliveryTime: '30-40 min',
    deliveryFee: 3.99,
    minimumOrder: 20,
    isOpen: true,
    address: '789 Pine St, Uptown',
    latitude: 37.7649,
    longitude: -122.3994,
    phone: '******-0125',
    categories: mockMenuCategories.filter(cat => cat.restaurantId === '3'),
    featured: true,
    promoted: false,
    tags: ['Premium', 'Fresh'],
  },
  {
    id: '4',
    name: 'Taco Fiesta',
    description: 'Authentic Mexican tacos and burritos with bold flavors',
    image: 'https://images.unsplash.com/photo-1565299507177-b0ac66763828?w=400&h=300&fit=crop',
    coverImage: 'https://images.unsplash.com/photo-1552332386-f8dd00dc2f85?w=400&h=200&fit=crop',
    cuisine: ['Mexican', 'Tacos'],
    rating: 4.3,
    reviewCount: 156,
    deliveryTime: '15-25 min',
    deliveryFee: 2.49,
    minimumOrder: 10,
    isOpen: false,
    address: '321 Elm St, Downtown',
    latitude: 37.7549,
    longitude: -122.4294,
    phone: '******-0126',
    categories: mockMenuCategories.filter(cat => cat.restaurantId === '4'),
    featured: false,
    promoted: false,
    tags: ['Spicy', 'Authentic'],
  },
];

// Utility functions
export const getRestaurantById = (id: string): Restaurant | undefined => {
  return mockRestaurants.find(restaurant => restaurant.id === id);
};

export const getMenuItemsByRestaurant = (restaurantId: string): MenuItem[] => {
  return mockMenuItems.filter(item => item.restaurantId === restaurantId);
};

export const getMenuItemById = (id: string): MenuItem | undefined => {
  return mockMenuItems.filter(item => item.id === id)[0];
};

export const searchRestaurants = (query: string): Restaurant[] => {
  const lowercaseQuery = query.toLowerCase();
  return mockRestaurants.filter(restaurant =>
    restaurant.name.toLowerCase().includes(lowercaseQuery) ||
    restaurant.description.toLowerCase().includes(lowercaseQuery) ||
    restaurant.cuisine.some(c => c.toLowerCase().includes(lowercaseQuery)) ||
    restaurant.tags.some(t => t.toLowerCase().includes(lowercaseQuery))
  );
};

export const getRestaurantsByCategory = (categoryName: string): Restaurant[] => {
  return mockRestaurants.filter(restaurant =>
    restaurant.cuisine.some(c => c.toLowerCase() === categoryName.toLowerCase())
  );
};

export const getFeaturedRestaurants = (): Restaurant[] => {
  return mockRestaurants.filter(restaurant => restaurant.featured);
};

export const getPromotedRestaurants = (): Restaurant[] => {
  return mockRestaurants.filter(restaurant => restaurant.promoted);
};
