// UX Enhancements for FoodWay App
// Animations, Transitions, and Interactive Elements

import React, { useRef, useEffect } from 'react';
import {
  Animated,
  Easing,
  PanGestureHandler,
  State,
  TouchableWithoutFeedback,
  Vibration,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import DesignSystem from './DesignSystem';

const { Animations } = DesignSystem;

// 🎭 ANIMATION HOOKS

// Fade In Animation Hook
export const useFadeIn = (duration = Animations.timing.normal, delay = 0) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const timer = setTimeout(() => {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration,
        useNativeDriver: true,
        easing: Easing.out(Easing.quad),
      }).start();
    }, delay);

    return () => clearTimeout(timer);
  }, []);

  return fadeAnim;
};

// Slide Up Animation Hook
export const useSlideUp = (duration = Animations.timing.normal, delay = 0) => {
  const slideAnim = useRef(new Animated.Value(50)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const timer = setTimeout(() => {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration,
          useNativeDriver: true,
          easing: Easing.out(Easing.back(1.2)),
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration,
          useNativeDriver: true,
          easing: Easing.out(Easing.quad),
        }),
      ]).start();
    }, delay);

    return () => clearTimeout(timer);
  }, []);

  return {
    transform: [{ translateY: slideAnim }],
    opacity: opacityAnim,
  };
};

// Scale Animation Hook
export const useScale = (duration = Animations.timing.normal, delay = 0) => {
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const timer = setTimeout(() => {
      Animated.parallel([
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
          ...Animations.spring,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration,
          useNativeDriver: true,
        }),
      ]).start();
    }, delay);

    return () => clearTimeout(timer);
  }, []);

  return {
    transform: [{ scale: scaleAnim }],
    opacity: opacityAnim,
  };
};

// Stagger Animation Hook for Lists
export const useStaggerAnimation = (items, duration = Animations.timing.normal) => {
  const animations = useRef(
    items.map(() => new Animated.Value(0))
  ).current;

  useEffect(() => {
    const staggerAnimations = animations.map((anim, index) =>
      Animated.timing(anim, {
        toValue: 1,
        duration,
        delay: index * 100, // 100ms stagger
        useNativeDriver: true,
        easing: Easing.out(Easing.quad),
      })
    );

    Animated.stagger(50, staggerAnimations).start();
  }, [items.length]);

  return animations;
};

// 🎯 INTERACTIVE COMPONENTS

// Pressable with Feedback Animation
export const AnimatedPressable = ({
  children,
  onPress,
  style,
  scaleValue = 0.95,
  hapticFeedback = true,
  ...props
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    if (hapticFeedback) {
      Vibration.vibrate(10); // Light haptic feedback
    }
    
    Animated.spring(scaleAnim, {
      toValue: scaleValue,
      useNativeDriver: true,
      ...Animations.spring,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      ...Animations.spring,
    }).start();
  };

  return (
    <TouchableWithoutFeedback
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      onPress={onPress}
      {...props}
    >
      <Animated.View
        style={[
          style,
          {
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        {children}
      </Animated.View>
    </TouchableWithoutFeedback>
  );
};

// Shimmer Loading Effect
export const ShimmerEffect = ({ width, height, borderRadius = 8, style }) => {
  const shimmerAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const shimmerAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(shimmerAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
          easing: Easing.linear,
        }),
        Animated.timing(shimmerAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
          easing: Easing.linear,
        }),
      ])
    );

    shimmerAnimation.start();

    return () => shimmerAnimation.stop();
  }, []);

  const translateX = shimmerAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [-width, width],
  });

  return (
    <Animated.View
      style={[
        {
          width,
          height,
          borderRadius,
          backgroundColor: '#E1E9EE',
          overflow: 'hidden',
        },
        style,
      ]}
    >
      <Animated.View
        style={{
          width: '100%',
          height: '100%',
          backgroundColor: 'rgba(255, 255, 255, 0.6)',
          transform: [{ translateX }],
        }}
      />
    </Animated.View>
  );
};

// Pull to Refresh Animation
export const usePullToRefresh = (onRefresh) => {
  const pullAnim = useRef(new Animated.Value(0)).current;
  const [refreshing, setRefreshing] = React.useState(false);

  const handleRefresh = async () => {
    setRefreshing(true);
    
    Animated.timing(pullAnim, {
      toValue: 1,
      duration: 200,
      useNativeDriver: true,
    }).start();

    try {
      await onRefresh();
    } finally {
      Animated.timing(pullAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setRefreshing(false);
      });
    }
  };

  const rotateInterpolate = pullAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return {
    refreshing,
    onRefresh: handleRefresh,
    pullAnimation: {
      transform: [{ rotate: rotateInterpolate }],
    },
  };
};

// Toast Notification Animation
export const useToast = () => {
  const toastAnim = useRef(new Animated.Value(-100)).current;
  const [toastVisible, setToastVisible] = React.useState(false);
  const [toastMessage, setToastMessage] = React.useState('');
  const [toastType, setToastType] = React.useState('success');

  const showToast = (message, type = 'success', duration = 3000) => {
    setToastMessage(message);
    setToastType(type);
    setToastVisible(true);

    Animated.sequence([
      Animated.spring(toastAnim, {
        toValue: 0,
        useNativeDriver: true,
        ...Animations.spring,
      }),
      Animated.delay(duration),
      Animated.timing(toastAnim, {
        toValue: -100,
        duration: Animations.timing.fast,
        useNativeDriver: true,
        easing: Easing.in(Easing.quad),
      }),
    ]).start(() => {
      setToastVisible(false);
    });
  };

  return {
    toastVisible,
    toastMessage,
    toastType,
    toastAnimation: {
      transform: [{ translateY: toastAnim }],
    },
    showToast,
  };
};

// Floating Action Button Animation
export const useFloatingButton = () => {
  const fabAnim = useRef(new Animated.Value(0)).current;
  const [fabVisible, setFabVisible] = React.useState(true);

  const showFab = () => {
    setFabVisible(true);
    Animated.spring(fabAnim, {
      toValue: 1,
      useNativeDriver: true,
      ...Animations.spring,
    }).start();
  };

  const hideFab = () => {
    Animated.timing(fabAnim, {
      toValue: 0,
      duration: Animations.timing.fast,
      useNativeDriver: true,
      easing: Easing.in(Easing.quad),
    }).start(() => {
      setFabVisible(false);
    });
  };

  const scaleInterpolate = fabAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  return {
    fabVisible,
    showFab,
    hideFab,
    fabAnimation: {
      transform: [{ scale: scaleInterpolate }],
      opacity: fabAnim,
    },
  };
};

// Screen Transition Hook
export const useScreenTransition = () => {
  const transitionAnim = useRef(new Animated.Value(0)).current;

  useFocusEffect(
    React.useCallback(() => {
      Animated.timing(transitionAnim, {
        toValue: 1,
        duration: Animations.timing.normal,
        useNativeDriver: true,
        easing: Easing.out(Easing.quad),
      }).start();

      return () => {
        transitionAnim.setValue(0);
      };
    }, [])
  );

  const slideFromRight = {
    transform: [
      {
        translateX: transitionAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [50, 0],
        }),
      },
    ],
    opacity: transitionAnim,
  };

  const slideFromBottom = {
    transform: [
      {
        translateY: transitionAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [30, 0],
        }),
      },
    ],
    opacity: transitionAnim,
  };

  const fadeIn = {
    opacity: transitionAnim,
  };

  return {
    slideFromRight,
    slideFromBottom,
    fadeIn,
  };
};

// Loading State Animation
export const useLoadingState = () => {
  const loadingAnim = useRef(new Animated.Value(0)).current;
  const [loading, setLoading] = React.useState(false);

  const startLoading = () => {
    setLoading(true);
    const loopAnimation = Animated.loop(
      Animated.timing(loadingAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
        easing: Easing.linear,
      })
    );
    loopAnimation.start();
  };

  const stopLoading = () => {
    setLoading(false);
    loadingAnim.stopAnimation();
    loadingAnim.setValue(0);
  };

  const rotateInterpolate = loadingAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return {
    loading,
    startLoading,
    stopLoading,
    loadingAnimation: {
      transform: [{ rotate: rotateInterpolate }],
    },
  };
};

// Cart Badge Animation
export const useCartBadge = (itemCount) => {
  const badgeAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (itemCount > 0) {
      Animated.sequence([
        Animated.spring(badgeAnim, {
          toValue: 1.3,
          useNativeDriver: true,
          ...Animations.spring,
        }),
        Animated.spring(badgeAnim, {
          toValue: 1,
          useNativeDriver: true,
          ...Animations.spring,
        }),
      ]).start();
    }
  }, [itemCount]);

  return {
    transform: [{ scale: badgeAnim }],
  };
};

export default {
  useFadeIn,
  useSlideUp,
  useScale,
  useStaggerAnimation,
  AnimatedPressable,
  ShimmerEffect,
  usePullToRefresh,
  useToast,
  useFloatingButton,
  useScreenTransition,
  useLoadingState,
  useCartBadge,
};
