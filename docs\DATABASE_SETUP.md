# Database Setup Guide - PostgreSQL & Redis on Railway

This guide covers setting up and managing PostgreSQL and Redis databases for the FoodWay Customer App on Railway.

## Table of Contents

1. [PostgreSQL Setup](#postgresql-setup)
2. [Redis Setup](#redis-setup)
3. [Database Schema](#database-schema)
4. [Migrations](#migrations)
5. [Connection Management](#connection-management)
6. [Performance Optimization](#performance-optimization)
7. [Backup and Recovery](#backup-and-recovery)
8. [Monitoring](#monitoring)

## PostgreSQL Setup

### Railway PostgreSQL Service

Railway provides managed PostgreSQL with:
- **Version:** PostgreSQL 15 (latest stable)
- **Storage:** SSD-backed storage with automatic scaling
- **Backups:** Daily automated backups with point-in-time recovery
- **Security:** SSL encryption and network isolation
- **Monitoring:** Built-in performance metrics

### Connection Configuration

Railway automatically provides the `DATABASE_URL` environment variable:

```
postgresql://username:password@host:port/database
```

### Connection Pool Settings

Our PostgreSQL service uses these connection pool settings:

```typescript
{
  max: 20,                    // Maximum connections
  idleTimeoutMillis: 30000,   // 30 seconds idle timeout
  connectionTimeoutMillis: 10000, // 10 seconds connection timeout
  statement_timeout: 30000,   // 30 seconds statement timeout
  query_timeout: 30000        // 30 seconds query timeout
}
```

### SSL Configuration

Production connections use SSL with `rejectUnauthorized: false` for Railway compatibility.

## Redis Setup

### Railway Redis Service

Railway provides managed Redis with:
- **Version:** Redis 7 (latest stable)
- **Persistence:** RDB and AOF persistence enabled
- **Memory:** Optimized memory usage with eviction policies
- **Security:** Password authentication and network isolation
- **Monitoring:** Built-in performance metrics

### Connection Configuration

Railway automatically provides the `REDIS_URL` environment variable:

```
redis://username:password@host:port
```

### Redis Configuration

Our Redis service uses these settings:

```typescript
{
  db: 0,                      // Database number
  retryDelayOnFailover: 100,  // Retry delay in ms
  maxRetriesPerRequest: 3,    // Maximum retry attempts
  lazyConnect: true,          // Connect on first command
  connectTimeout: 10000,      // 10 seconds connection timeout
  commandTimeout: 5000        // 5 seconds command timeout
}
```

## Database Schema

### Core Tables

#### Users and Authentication
```sql
-- Users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  phone VARCHAR(20),
  avatar_url TEXT,
  email_verified BOOLEAN DEFAULT FALSE,
  phone_verified BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User addresses
CREATE TABLE user_addresses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  type VARCHAR(20) NOT NULL DEFAULT 'other',
  label VARCHAR(100),
  street_address TEXT NOT NULL,
  city VARCHAR(100) NOT NULL,
  state VARCHAR(100) NOT NULL,
  postal_code VARCHAR(20) NOT NULL,
  country VARCHAR(100) NOT NULL DEFAULT 'US',
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  is_default BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Payment methods
CREATE TABLE user_payment_methods (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  type VARCHAR(20) NOT NULL,
  stripe_payment_method_id VARCHAR(255),
  last_four VARCHAR(4),
  brand VARCHAR(50),
  exp_month INTEGER,
  exp_year INTEGER,
  is_default BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Restaurants and Menu
```sql
-- Restaurants
CREATE TABLE restaurants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  cuisine_type VARCHAR(100),
  phone VARCHAR(20),
  email VARCHAR(255),
  website_url TEXT,
  logo_url TEXT,
  cover_image_url TEXT,
  street_address TEXT NOT NULL,
  city VARCHAR(100) NOT NULL,
  state VARCHAR(100) NOT NULL,
  postal_code VARCHAR(20) NOT NULL,
  country VARCHAR(100) NOT NULL DEFAULT 'US',
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  rating DECIMAL(3, 2) DEFAULT 0,
  review_count INTEGER DEFAULT 0,
  price_range INTEGER DEFAULT 2,
  delivery_fee DECIMAL(10, 2) DEFAULT 0,
  minimum_order DECIMAL(10, 2) DEFAULT 0,
  delivery_time_min INTEGER DEFAULT 30,
  delivery_time_max INTEGER DEFAULT 60,
  is_active BOOLEAN DEFAULT TRUE,
  is_featured BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Menu categories
CREATE TABLE menu_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  restaurant_id UUID NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Menu items
CREATE TABLE menu_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  restaurant_id UUID NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
  category_id UUID REFERENCES menu_categories(id) ON DELETE SET NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  price DECIMAL(10, 2) NOT NULL,
  image_url TEXT,
  is_vegetarian BOOLEAN DEFAULT FALSE,
  is_vegan BOOLEAN DEFAULT FALSE,
  is_gluten_free BOOLEAN DEFAULT FALSE,
  allergens TEXT[],
  calories INTEGER,
  prep_time INTEGER,
  is_available BOOLEAN DEFAULT TRUE,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Orders and Tracking
```sql
-- Orders
CREATE TABLE orders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  restaurant_id UUID NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
  order_number VARCHAR(50) UNIQUE NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  subtotal DECIMAL(10, 2) NOT NULL,
  tax_amount DECIMAL(10, 2) NOT NULL DEFAULT 0,
  delivery_fee DECIMAL(10, 2) NOT NULL DEFAULT 0,
  tip_amount DECIMAL(10, 2) NOT NULL DEFAULT 0,
  total_amount DECIMAL(10, 2) NOT NULL,
  payment_method VARCHAR(50),
  payment_intent_id VARCHAR(255),
  delivery_address JSONB NOT NULL,
  special_instructions TEXT,
  estimated_delivery_time TIMESTAMP WITH TIME ZONE,
  delivered_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Order items
CREATE TABLE order_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  menu_item_id UUID NOT NULL REFERENCES menu_items(id),
  quantity INTEGER NOT NULL DEFAULT 1,
  unit_price DECIMAL(10, 2) NOT NULL,
  total_price DECIMAL(10, 2) NOT NULL,
  special_instructions TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Order tracking
CREATE TABLE order_tracking (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  status VARCHAR(20) NOT NULL,
  message TEXT,
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Indexes for Performance

```sql
-- User indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_user_addresses_user_id ON user_addresses(user_id);
CREATE INDEX idx_user_payment_methods_user_id ON user_payment_methods(user_id);

-- Restaurant indexes
CREATE INDEX idx_restaurants_location ON restaurants(latitude, longitude);
CREATE INDEX idx_restaurants_cuisine ON restaurants(cuisine_type);
CREATE INDEX idx_restaurants_rating ON restaurants(rating DESC);
CREATE INDEX idx_restaurants_active ON restaurants(is_active);
CREATE INDEX idx_menu_items_restaurant_id ON menu_items(restaurant_id);
CREATE INDEX idx_menu_items_category_id ON menu_items(category_id);

-- Order indexes
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_restaurant_id ON orders(restaurant_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at DESC);
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_tracking_order_id ON order_tracking(order_id);

-- Review indexes
CREATE INDEX idx_reviews_restaurant_id ON reviews(restaurant_id);
CREATE INDEX idx_reviews_user_id ON reviews(user_id);
CREATE INDEX idx_reviews_rating ON reviews(rating DESC);
```

## Migrations

### Running Migrations

Use the database migration system:

```javascript
// Run all migrations
const { DatabaseMigrations } = require('./services/database/index');
await DatabaseMigrations.runAllMigrations();

// Run specific migration
await DatabaseMigrations.createUserTables();
await DatabaseMigrations.createRestaurantTables();
await DatabaseMigrations.createOrderTables();
```

### Migration Commands

```bash
# Using Railway CLI
railway run node -e "require('./services/database/index.js').DatabaseMigrations.runAllMigrations()"

# Using deployment script
node scripts/railway-deploy.js

# Manual migration
node -e "require('./services/database/index.js').DatabaseMigrations.runAllMigrations()"
```

## Connection Management

### PostgreSQL Connection

```typescript
import { db } from './services/database';

// Basic query
const users = await db.query('SELECT * FROM users WHERE is_active = $1', [true]);

// Transaction
await db.transaction(async (client) => {
  await client.query('INSERT INTO users (...) VALUES (...)', [...]);
  await client.query('INSERT INTO user_addresses (...) VALUES (...)', [...]);
});

// Utility methods
const user = await db.findById('users', userId);
const users = await db.findMany('users', { is_active: true });
const newUser = await db.create('users', userData);
```

### Redis Connection

```typescript
import { redis } from './services/database';

// Basic operations
await redis.set('key', 'value', 3600); // TTL in seconds
const value = await redis.get('key');
await redis.del('key');

// JSON operations
await redis.setJSON('user:123', userObject, 3600);
const user = await redis.getJSON('user:123');

// Cache pattern
const restaurants = await redis.cache('restaurants:nearby', async () => {
  return await db.findMany('restaurants', { is_active: true });
}, { ttl: 1800 }); // 30 minutes
```

## Performance Optimization

### Database Optimization

1. **Use Indexes:** Create indexes on frequently queried columns
2. **Connection Pooling:** Reuse database connections
3. **Query Optimization:** Use EXPLAIN to analyze query performance
4. **Batch Operations:** Group multiple operations together

### Redis Optimization

1. **Set TTL:** Always set expiration times for cache entries
2. **Use Appropriate Data Types:** Choose the right Redis data structure
3. **Pipeline Operations:** Batch multiple Redis commands
4. **Memory Management:** Monitor memory usage and set eviction policies

### Cache Strategy

```typescript
// Cache frequently accessed data
const CacheKeys = {
  USER_PROFILE: (userId) => `user:profile:${userId}`,
  RESTAURANT_MENU: (restaurantId) => `restaurant:menu:${restaurantId}`,
  POPULAR_RESTAURANTS: 'restaurants:popular',
};

// Cache with fallback
async function getRestaurantMenu(restaurantId) {
  const cacheKey = CacheKeys.RESTAURANT_MENU(restaurantId);
  
  // Try cache first
  let menu = await redis.getJSON(cacheKey);
  if (menu) return menu;
  
  // Fallback to database
  menu = await db.findMany('menu_items', { restaurant_id: restaurantId });
  
  // Cache for 1 hour
  await redis.setJSON(cacheKey, menu, 3600);
  
  return menu;
}
```

## Backup and Recovery

### Automated Backups

Railway provides:
- **Daily backups** of PostgreSQL
- **Point-in-time recovery** up to 7 days
- **Cross-region replication** for disaster recovery

### Manual Backup

```bash
# Backup database
railway connect postgresql -- pg_dump > backup.sql

# Restore database
railway connect postgresql -- psql < backup.sql
```

### Redis Persistence

Redis uses both RDB and AOF persistence:
- **RDB:** Point-in-time snapshots
- **AOF:** Append-only file for durability

## Monitoring

### Health Checks

```typescript
// Check database health
const health = await db.healthCheck();
console.log('Database status:', health.status);
console.log('Latency:', health.latency, 'ms');

// Check Redis health
const redisHealth = await redis.healthCheck();
console.log('Redis status:', redisHealth.status);
console.log('Latency:', redisHealth.latency, 'ms');
```

### Performance Monitoring

1. **Query Performance:** Monitor slow queries
2. **Connection Pool:** Monitor connection usage
3. **Cache Hit Rate:** Monitor Redis cache effectiveness
4. **Memory Usage:** Monitor database and Redis memory

### Logging

Enable query logging for debugging:

```typescript
// Log slow queries
const result = await db.query(sql, params);
console.log('Query executed in:', Date.now() - start, 'ms');
```

## Best Practices

1. **Use Transactions:** For multi-table operations
2. **Validate Input:** Always validate and sanitize user input
3. **Use Prepared Statements:** Prevent SQL injection
4. **Monitor Performance:** Regular performance audits
5. **Cache Strategically:** Cache frequently accessed data
6. **Handle Errors:** Implement proper error handling
7. **Use Connection Pooling:** Efficient connection management
8. **Regular Backups:** Ensure data safety

For more information, refer to the [PostgreSQL Documentation](https://www.postgresql.org/docs/) and [Redis Documentation](https://redis.io/documentation).
