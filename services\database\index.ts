// Database services for Railway PostgreSQL and Redis integration
export { db, type DatabaseConnection, type QueryOptions } from './postgresql';
export { redis, type RedisConnection, type CacheOptions } from './redis';

import { db } from './postgresql';
import { redis } from './redis';

// Combined health check for both services
export async function healthCheck() {
  const [dbHealth, redisHealth] = await Promise.all([
    db.healthCheck(),
    redis.healthCheck(),
  ]);

  return {
    database: dbHealth,
    redis: redisHealth,
    overall: dbHealth.status === 'connected' && redisHealth.status === 'connected' 
      ? 'healthy' 
      : 'unhealthy',
    timestamp: new Date().toISOString(),
  };
}

// Graceful shutdown for both services
export async function closeConnections() {
  await Promise.all([
    db.close(),
    redis.close(),
  ]);
}

// Database migration utilities
export class DatabaseMigrations {
  static async createUserTables() {
    await db.createTable('users', `
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      email VARCHAR(255) UNIQUE NOT NULL,
      password_hash VARCHAR(255) NOT NULL,
      first_name VARCHAR(100) NOT NULL,
      last_name VARCHAR(100) NOT NULL,
      phone VARCHAR(20),
      avatar_url TEXT,
      email_verified BOOLEAN DEFAULT FALSE,
      phone_verified BOOLEAN DEFAULT FALSE,
      is_active BOOLEAN DEFAULT TRUE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    `);

    await db.createTable('user_addresses', `
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      type VARCHAR(20) NOT NULL DEFAULT 'other',
      label VARCHAR(100),
      street_address TEXT NOT NULL,
      city VARCHAR(100) NOT NULL,
      state VARCHAR(100) NOT NULL,
      postal_code VARCHAR(20) NOT NULL,
      country VARCHAR(100) NOT NULL DEFAULT 'US',
      latitude DECIMAL(10, 8),
      longitude DECIMAL(11, 8),
      is_default BOOLEAN DEFAULT FALSE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    `);

    await db.createTable('user_payment_methods', `
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      type VARCHAR(20) NOT NULL,
      stripe_payment_method_id VARCHAR(255),
      last_four VARCHAR(4),
      brand VARCHAR(50),
      exp_month INTEGER,
      exp_year INTEGER,
      is_default BOOLEAN DEFAULT FALSE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    `);
  }

  static async createRestaurantTables() {
    await db.createTable('restaurants', `
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      name VARCHAR(255) NOT NULL,
      description TEXT,
      cuisine_type VARCHAR(100),
      phone VARCHAR(20),
      email VARCHAR(255),
      website_url TEXT,
      logo_url TEXT,
      cover_image_url TEXT,
      street_address TEXT NOT NULL,
      city VARCHAR(100) NOT NULL,
      state VARCHAR(100) NOT NULL,
      postal_code VARCHAR(20) NOT NULL,
      country VARCHAR(100) NOT NULL DEFAULT 'US',
      latitude DECIMAL(10, 8),
      longitude DECIMAL(11, 8),
      rating DECIMAL(3, 2) DEFAULT 0,
      review_count INTEGER DEFAULT 0,
      price_range INTEGER DEFAULT 2,
      delivery_fee DECIMAL(10, 2) DEFAULT 0,
      minimum_order DECIMAL(10, 2) DEFAULT 0,
      delivery_time_min INTEGER DEFAULT 30,
      delivery_time_max INTEGER DEFAULT 60,
      is_active BOOLEAN DEFAULT TRUE,
      is_featured BOOLEAN DEFAULT FALSE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    `);

    await db.createTable('menu_categories', `
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      restaurant_id UUID NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
      name VARCHAR(255) NOT NULL,
      description TEXT,
      sort_order INTEGER DEFAULT 0,
      is_active BOOLEAN DEFAULT TRUE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    `);

    await db.createTable('menu_items', `
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      restaurant_id UUID NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
      category_id UUID REFERENCES menu_categories(id) ON DELETE SET NULL,
      name VARCHAR(255) NOT NULL,
      description TEXT,
      price DECIMAL(10, 2) NOT NULL,
      image_url TEXT,
      is_vegetarian BOOLEAN DEFAULT FALSE,
      is_vegan BOOLEAN DEFAULT FALSE,
      is_gluten_free BOOLEAN DEFAULT FALSE,
      allergens TEXT[],
      calories INTEGER,
      prep_time INTEGER,
      is_available BOOLEAN DEFAULT TRUE,
      sort_order INTEGER DEFAULT 0,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    `);
  }

  static async createOrderTables() {
    await db.createTable('orders', `
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      restaurant_id UUID NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
      order_number VARCHAR(50) UNIQUE NOT NULL,
      status VARCHAR(20) NOT NULL DEFAULT 'pending',
      subtotal DECIMAL(10, 2) NOT NULL,
      tax_amount DECIMAL(10, 2) NOT NULL DEFAULT 0,
      delivery_fee DECIMAL(10, 2) NOT NULL DEFAULT 0,
      tip_amount DECIMAL(10, 2) NOT NULL DEFAULT 0,
      total_amount DECIMAL(10, 2) NOT NULL,
      payment_method VARCHAR(50),
      payment_intent_id VARCHAR(255),
      delivery_address JSONB NOT NULL,
      special_instructions TEXT,
      estimated_delivery_time TIMESTAMP WITH TIME ZONE,
      delivered_at TIMESTAMP WITH TIME ZONE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    `);

    await db.createTable('order_items', `
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
      menu_item_id UUID NOT NULL REFERENCES menu_items(id),
      quantity INTEGER NOT NULL DEFAULT 1,
      unit_price DECIMAL(10, 2) NOT NULL,
      total_price DECIMAL(10, 2) NOT NULL,
      special_instructions TEXT,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    `);

    await db.createTable('order_tracking', `
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
      status VARCHAR(20) NOT NULL,
      message TEXT,
      latitude DECIMAL(10, 8),
      longitude DECIMAL(11, 8),
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    `);
  }

  static async createReviewTables() {
    await db.createTable('reviews', `
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      restaurant_id UUID NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
      order_id UUID REFERENCES orders(id) ON DELETE SET NULL,
      rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
      comment TEXT,
      images TEXT[],
      is_verified BOOLEAN DEFAULT FALSE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      UNIQUE(user_id, order_id)
    `);
  }

  static async createNotificationTables() {
    await db.createTable('notifications', `
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      type VARCHAR(20) NOT NULL,
      title VARCHAR(255) NOT NULL,
      message TEXT NOT NULL,
      data JSONB,
      is_read BOOLEAN DEFAULT FALSE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    `);

    await db.createTable('push_tokens', `
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      token VARCHAR(255) NOT NULL,
      platform VARCHAR(20) NOT NULL,
      is_active BOOLEAN DEFAULT TRUE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      UNIQUE(user_id, token)
    `);
  }

  static async runAllMigrations() {
    console.log('Running database migrations...');
    
    try {
      await this.createUserTables();
      console.log('✓ User tables created');
      
      await this.createRestaurantTables();
      console.log('✓ Restaurant tables created');
      
      await this.createOrderTables();
      console.log('✓ Order tables created');
      
      await this.createReviewTables();
      console.log('✓ Review tables created');
      
      await this.createNotificationTables();
      console.log('✓ Notification tables created');
      
      console.log('✅ All migrations completed successfully');
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }
}

// Cache keys for Redis
export const CacheKeys = {
  USER_PROFILE: (userId: string) => `user:profile:${userId}`,
  USER_ADDRESSES: (userId: string) => `user:addresses:${userId}`,
  USER_PAYMENT_METHODS: (userId: string) => `user:payment_methods:${userId}`,
  RESTAURANT_DETAILS: (restaurantId: string) => `restaurant:details:${restaurantId}`,
  RESTAURANT_MENU: (restaurantId: string) => `restaurant:menu:${restaurantId}`,
  RESTAURANTS_NEARBY: (lat: number, lng: number, radius: number) => 
    `restaurants:nearby:${lat}:${lng}:${radius}`,
  ORDER_DETAILS: (orderId: string) => `order:details:${orderId}`,
  ORDER_TRACKING: (orderId: string) => `order:tracking:${orderId}`,
  SEARCH_RESULTS: (query: string, filters: string) => `search:${query}:${filters}`,
  POPULAR_RESTAURANTS: 'restaurants:popular',
  FEATURED_RESTAURANTS: 'restaurants:featured',
  CATEGORIES: 'categories',
} as const;
