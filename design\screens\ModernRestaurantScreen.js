// Modern Restaurant/Menu Screen Design for FoodWay App
// Optimized for performance and visual appeal

import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useRef, useState } from 'react';
import {
    Animated,
    FlatList,
    Image,
    Modal,
    SafeAreaView,
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import DesignSystem from '../DesignSystem';
import { FoodCard, ModernButton } from '../ModernComponents';

const { Colors, Typography, Spacing, BorderRadius, Shadows, Layout } = DesignSystem;

const ModernRestaurantScreen = ({ navigation, route }) => {
  const { restaurant } = route.params;
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const [sortBy, setSortBy] = useState('popular');
  const [filterBy, setFilterBy] = useState({ veg: false, nonVeg: false, spicy: false });
  const scrollY = useRef(new Animated.Value(0)).current;

  // Sample menu data - replace with actual API data
  const menuCategories = [
    { id: 'all', name: 'All Items', count: 24 },
    { id: 'starters', name: 'Starters', count: 8 },
    { id: 'mains', name: 'Main Course', count: 12 },
    { id: 'desserts', name: 'Desserts', count: 4 },
  ];

  const menuItems = [
    {
      id: '1',
      name: 'Chicken Tikka Masala',
      description: 'Tender chicken in creamy tomato curry with aromatic spices',
      price: 650,
      originalPrice: 750,
      rating: 4.8,
      image: 'https://via.placeholder.com/200x160',
      category: 'mains',
      isVeg: false,
      spiceLevel: 2,
      discount: 13,
      isPopular: true,
    },
    {
      id: '2',
      name: 'Paneer Butter Masala',
      description: 'Soft paneer cubes in rich, creamy tomato gravy',
      price: 550,
      rating: 4.6,
      image: 'https://via.placeholder.com/200x160',
      category: 'mains',
      isVeg: true,
      spiceLevel: 1,
      isPopular: true,
    },
    {
      id: '3',
      name: 'Chicken Seekh Kebab',
      description: 'Juicy minced chicken kebabs with traditional spices',
      price: 450,
      rating: 4.7,
      image: 'https://via.placeholder.com/200x160',
      category: 'starters',
      isVeg: false,
      spiceLevel: 3,
    },
    {
      id: '4',
      name: 'Gulab Jamun',
      description: 'Traditional milk dumplings in sweet syrup',
      price: 180,
      rating: 4.5,
      image: 'https://via.placeholder.com/200x160',
      category: 'desserts',
      isVeg: true,
      spiceLevel: 0,
    },
  ];

  const handleCategoryPress = (categoryId) => {
    setSelectedCategory(categoryId);
  };

  const handleAddToCart = (item) => {
    console.log('Added to cart:', item);
  };

  const handleFilterPress = () => {
    setShowFilters(true);
  };

  const applyFilters = () => {
    setShowFilters(false);
    // Apply filter logic here
  };

  const resetFilters = () => {
    setFilterBy({ veg: false, nonVeg: false, spicy: false });
    setSortBy('popular');
  };

  const filteredItems = menuItems.filter(item => {
    if (selectedCategory !== 'all' && item.category !== selectedCategory) {
      return false;
    }
    if (filterBy.veg && !item.isVeg) return false;
    if (filterBy.nonVeg && item.isVeg) return false;
    if (filterBy.spicy && item.spiceLevel < 2) return false;
    return true;
  });

  const headerHeight = scrollY.interpolate({
    inputRange: [0, 200],
    outputRange: [250, 100],
    extrapolate: 'clamp',
  });

  const headerOpacity = scrollY.interpolate({
    inputRange: [0, 150, 200],
    outputRange: [1, 0.5, 0],
    extrapolate: 'clamp',
  });

  const titleOpacity = scrollY.interpolate({
    inputRange: [150, 200],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      
      {/* Animated Header */}
      <Animated.View style={[styles.header, { height: headerHeight }]}>
        <Image
          source={{ uri: restaurant.image }}
          style={styles.headerImage}
          resizeMode="cover"
        />
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.7)']}
          style={styles.headerOverlay}
        />
        
        {/* Header Actions */}
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={Colors.textWhite} />
          </TouchableOpacity>
          
          <View style={styles.headerRightActions}>
            <TouchableOpacity style={styles.headerActionButton}>
              <Ionicons name="heart-outline" size={24} color={Colors.textWhite} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.headerActionButton}>
              <Ionicons name="share-outline" size={24} color={Colors.textWhite} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Restaurant Info */}
        <Animated.View style={[styles.restaurantInfo, { opacity: headerOpacity }]}>
          <Text style={styles.restaurantName}>{restaurant.name}</Text>
          <Text style={styles.restaurantCuisine}>{restaurant.cuisine}</Text>
          
          <View style={styles.restaurantStats}>
            <View style={styles.statItem}>
              <Ionicons name="star" size={16} color={Colors.rating} />
              <Text style={styles.statText}>{restaurant.rating}</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Ionicons name="time-outline" size={16} color={Colors.textWhite} />
              <Text style={styles.statText}>{restaurant.deliveryTime}</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Ionicons name="bicycle-outline" size={16} color={Colors.textWhite} />
              <Text style={styles.statText}>
                {restaurant.deliveryFee === 0 ? 'Free' : `₨${restaurant.deliveryFee}`}
              </Text>
            </View>
          </View>
        </Animated.View>
      </Animated.View>

      {/* Sticky Title Bar */}
      <Animated.View style={[styles.stickyHeader, { opacity: titleOpacity }]}>
        <Text style={styles.stickyTitle}>{restaurant.name}</Text>
        <TouchableOpacity
          style={styles.cartButton}
          onPress={() => navigation.navigate('Cart')}
        >
          <Ionicons name="bag" size={20} color={Colors.textWhite} />
          <View style={styles.cartBadge}>
            <Text style={styles.cartBadgeText}>3</Text>
          </View>
        </TouchableOpacity>
      </Animated.View>

      {/* Menu Categories */}
      <View style={styles.categoriesContainer}>
        <FlatList
          data={menuCategories}
          horizontal
          showsHorizontalScrollIndicator={false}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.categoriesList}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[
                styles.categoryTab,
                selectedCategory === item.id && styles.categoryTabSelected,
              ]}
              onPress={() => handleCategoryPress(item.id)}
            >
              <Text
                style={[
                  styles.categoryTabText,
                  selectedCategory === item.id && styles.categoryTabTextSelected,
                ]}
              >
                {item.name} ({item.count})
              </Text>
            </TouchableOpacity>
          )}
        />
      </View>

      {/* Menu Items */}
      <Animated.ScrollView
        style={styles.menuContainer}
        showsVerticalScrollIndicator={false}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: false }
        )}
        scrollEventThrottle={16}
      >
        <FlatList
          data={filteredItems}
          keyExtractor={(item) => item.id}
          numColumns={2}
          columnWrapperStyle={styles.menuRow}
          contentContainerStyle={styles.menuList}
          renderItem={({ item }) => (
            <FoodCard
              item={item}
              onPress={() => navigation.navigate('FoodDetail', { item })}
              onAddToCart={handleAddToCart}
              style={styles.menuItem}
            />
          )}
        />
        
        <View style={styles.bottomSpacing} />
      </Animated.ScrollView>

      {/* Floating Filter Button */}
      <TouchableOpacity
        style={styles.filterButton}
        onPress={handleFilterPress}
      >
        <Ionicons name="options" size={20} color={Colors.textWhite} />
        <Text style={styles.filterButtonText}>Filter & Sort</Text>
      </TouchableOpacity>

      {/* Filter Modal */}
      <Modal
        visible={showFilters}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowFilters(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Filter & Sort</Text>
            <TouchableOpacity onPress={() => setShowFilters(false)}>
              <Ionicons name="close" size={24} color={Colors.textPrimary} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            {/* Sort Options */}
            <View style={styles.filterSection}>
              <Text style={styles.filterSectionTitle}>Sort By</Text>
              {['popular', 'price-low', 'price-high', 'rating'].map((option) => (
                <TouchableOpacity
                  key={option}
                  style={styles.filterOption}
                  onPress={() => setSortBy(option)}
                >
                  <Text style={styles.filterOptionText}>
                    {option === 'popular' ? 'Most Popular' :
                     option === 'price-low' ? 'Price: Low to High' :
                     option === 'price-high' ? 'Price: High to Low' :
                     'Highest Rated'}
                  </Text>
                  <Ionicons
                    name={sortBy === option ? 'radio-button-on' : 'radio-button-off'}
                    size={20}
                    color={sortBy === option ? Colors.primary : Colors.textSecondary}
                  />
                </TouchableOpacity>
              ))}
            </View>

            {/* Filter Options */}
            <View style={styles.filterSection}>
              <Text style={styles.filterSectionTitle}>Dietary Preferences</Text>
              
              <TouchableOpacity
                style={styles.filterOption}
                onPress={() => setFilterBy(prev => ({ ...prev, veg: !prev.veg }))}
              >
                <Text style={styles.filterOptionText}>Vegetarian Only</Text>
                <Ionicons
                  name={filterBy.veg ? 'checkbox' : 'checkbox-outline'}
                  size={20}
                  color={filterBy.veg ? Colors.primary : Colors.textSecondary}
                />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.filterOption}
                onPress={() => setFilterBy(prev => ({ ...prev, nonVeg: !prev.nonVeg }))}
              >
                <Text style={styles.filterOptionText}>Non-Vegetarian Only</Text>
                <Ionicons
                  name={filterBy.nonVeg ? 'checkbox' : 'checkbox-outline'}
                  size={20}
                  color={filterBy.nonVeg ? Colors.primary : Colors.textSecondary}
                />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.filterOption}
                onPress={() => setFilterBy(prev => ({ ...prev, spicy: !prev.spicy }))}
              >
                <Text style={styles.filterOptionText}>Spicy Items</Text>
                <Ionicons
                  name={filterBy.spicy ? 'checkbox' : 'checkbox-outline'}
                  size={20}
                  color={filterBy.spicy ? Colors.primary : Colors.textSecondary}
                />
              </TouchableOpacity>
            </View>
          </ScrollView>

          <View style={styles.modalActions}>
            <ModernButton
              title="Reset"
              variant="outline"
              onPress={resetFilters}
              style={styles.modalButton}
            />
            <ModernButton
              title="Apply Filters"
              onPress={applyFilters}
              style={styles.modalButton}
            />
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    position: 'relative',
    overflow: 'hidden',
  },
  headerImage: {
    width: '100%',
    height: '100%',
  },
  headerOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '60%',
  },
  headerActions: {
    position: 'absolute',
    top: Layout.statusBarHeight,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.base,
    paddingTop: Spacing.sm,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerRightActions: {
    flexDirection: 'row',
  },
  headerActionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: Spacing.sm,
  },
  restaurantInfo: {
    position: 'absolute',
    bottom: Spacing.lg,
    left: Spacing.base,
    right: Spacing.base,
  },
  restaurantName: {
    fontSize: Typography.fontSize['3xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.textWhite,
    marginBottom: 4,
  },
  restaurantCuisine: {
    fontSize: Typography.fontSize.lg,
    color: Colors.textWhite,
    marginBottom: Spacing.base,
  },
  restaurantStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textWhite,
    marginLeft: 4,
    fontWeight: Typography.fontWeight.medium,
  },
  statDivider: {
    width: 1,
    height: 16,
    backgroundColor: 'rgba(255,255,255,0.3)',
    marginHorizontal: Spacing.base,
  },
  stickyHeader: {
    position: 'absolute',
    top: Layout.statusBarHeight,
    left: 0,
    right: 0,
    height: Layout.headerHeight,
    backgroundColor: Colors.primary,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.base,
    zIndex: 10,
    ...Shadows.md,
  },
  stickyTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.textWhite,
  },
  cartButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  cartBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: Colors.accent,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartBadgeText: {
    fontSize: Typography.fontSize.xs,
    color: Colors.textWhite,
    fontWeight: Typography.fontWeight.bold,
  },
  categoriesContainer: {
    backgroundColor: Colors.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  categoriesList: {
    paddingHorizontal: Spacing.base,
    paddingVertical: Spacing.sm,
  },
  categoryTab: {
    paddingHorizontal: Spacing.base,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.surface,
    marginRight: Spacing.sm,
  },
  categoryTabSelected: {
    backgroundColor: Colors.primary,
  },
  categoryTabText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
    fontWeight: Typography.fontWeight.medium,
  },
  categoryTabTextSelected: {
    color: Colors.textWhite,
    fontWeight: Typography.fontWeight.semibold,
  },
  menuContainer: {
    flex: 1,
  },
  menuList: {
    padding: Spacing.base,
  },
  menuRow: {
    justifyContent: 'space-between',
  },
  menuItem: {
    width: (Layout.window.width - Spacing.base * 3) / 2,
  },
  filterButton: {
    position: 'absolute',
    bottom: Spacing.xl,
    right: Spacing.base,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.base,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.full,
    ...Shadows.lg,
  },
  filterButtonText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textWhite,
    fontWeight: Typography.fontWeight.semibold,
    marginLeft: Spacing.sm,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.base,
    paddingVertical: Spacing.base,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  modalTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.textPrimary,
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: Spacing.base,
  },
  filterSection: {
    marginVertical: Spacing.lg,
  },
  filterSectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.textPrimary,
    marginBottom: Spacing.base,
  },
  filterOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Spacing.base,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  filterOptionText: {
    fontSize: Typography.fontSize.base,
    color: Colors.textPrimary,
  },
  modalActions: {
    flexDirection: 'row',
    paddingHorizontal: Spacing.base,
    paddingVertical: Spacing.base,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  modalButton: {
    flex: 1,
    marginHorizontal: Spacing.sm,
  },
  bottomSpacing: {
    height: 100,
  },
});
