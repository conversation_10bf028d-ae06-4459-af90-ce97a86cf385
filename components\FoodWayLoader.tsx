import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated, ActivityIndicator } from 'react-native';
import { FoodWayMiniLogo } from './FoodWayLogo';
import { COLORS, SPACING } from '../utils/constants';

interface FoodWayLoaderProps {
  message?: string;
  size?: 'small' | 'medium' | 'large';
  showLogo?: boolean;
  color?: string;
}

export const FoodWayLoader: React.FC<FoodWayLoaderProps> = ({
  message = 'Loading...',
  size = 'medium',
  showLogo = true,
  color = COLORS.primary,
}) => {
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 0.7,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    );

    pulseAnimation.start();

    return () => pulseAnimation.stop();
  }, [pulseAnim]);

  const getLogoSize = () => {
    switch (size) {
      case 'small':
        return 30;
      case 'large':
        return 60;
      default:
        return 40;
    }
  };

  const getSpinnerSize = () => {
    switch (size) {
      case 'small':
        return 'small' as const;
      case 'large':
        return 'large' as const;
      default:
        return 'small' as const;
    }
  };

  return (
    <View style={styles.container}>
      {showLogo && (
        <Animated.View
          style={[
            styles.logoContainer,
            {
              transform: [{ scale: pulseAnim }],
            },
          ]}
        >
          <FoodWayMiniLogo size={getLogoSize()} color={color} />
        </Animated.View>
      )}
      
      <ActivityIndicator
        size={getSpinnerSize()}
        color={color}
        style={styles.spinner}
      />
      
      {message && (
        <Text style={[styles.message, { color }]}>
          {message}
        </Text>
      )}
    </View>
  );
};

// Full screen loader
export const FoodWayFullScreenLoader: React.FC<{
  message?: string;
  visible?: boolean;
}> = ({ message = 'Loading...', visible = true }) => {
  if (!visible) return null;

  return (
    <View style={styles.fullScreenContainer}>
      <View style={styles.fullScreenContent}>
        <FoodWayLoader
          message={message}
          size="large"
          showLogo={true}
          color={COLORS.primary}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.md,
  },
  logoContainer: {
    marginBottom: SPACING.sm,
  },
  spinner: {
    marginVertical: SPACING.sm,
  },
  message: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: SPACING.sm,
    opacity: 0.8,
  },
  fullScreenContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
  },
  fullScreenContent: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.xl,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});

export default FoodWayLoader;
