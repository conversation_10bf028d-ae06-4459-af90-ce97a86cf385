import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, StatusBar } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { FoodWayMiniLogo } from './FoodWayLogo';
import { COLORS, SPACING } from '../utils/constants';

interface FoodWayHeaderProps {
  title?: string;
  subtitle?: string;
  showLogo?: boolean;
  showBackButton?: boolean;
  onBackPress?: () => void;
  rightComponent?: React.ReactNode;
  variant?: 'gradient' | 'solid' | 'transparent';
  backgroundColor?: string;
}

export const FoodWayHeader: React.FC<FoodWayHeaderProps> = ({
  title,
  subtitle,
  showLogo = true,
  showBackButton = false,
  onBackPress,
  rightComponent,
  variant = 'gradient',
  backgroundColor = COLORS.primary,
}) => {
  const insets = useSafeAreaInsets();

  const renderContent = () => (
    <View style={[styles.container, { paddingTop: insets.top + SPACING.sm }]}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      
      <View style={styles.headerRow}>
        {/* Left side */}
        <View style={styles.leftSection}>
          {showBackButton && (
            <TouchableOpacity
              style={styles.backButton}
              onPress={onBackPress}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Ionicons name="arrow-back" size={24} color={COLORS.white} />
            </TouchableOpacity>
          )}
          
          {showLogo && !showBackButton && (
            <FoodWayMiniLogo size={35} color={COLORS.white} />
          )}
        </View>

        {/* Center */}
        <View style={styles.centerSection}>
          {title && (
            <Text style={styles.title} numberOfLines={1}>
              {title}
            </Text>
          )}
          {subtitle && (
            <Text style={styles.subtitle} numberOfLines={1}>
              {subtitle}
            </Text>
          )}
        </View>

        {/* Right side */}
        <View style={styles.rightSection}>
          {rightComponent}
        </View>
      </View>
    </View>
  );

  if (variant === 'gradient') {
    return (
      <LinearGradient
        colors={[COLORS.primary, COLORS.secondary]}
        style={styles.gradientContainer}
      >
        {renderContent()}
      </LinearGradient>
    );
  }

  if (variant === 'solid') {
    return (
      <View style={[styles.solidContainer, { backgroundColor }]}>
        {renderContent()}
      </View>
    );
  }

  // Transparent variant
  return (
    <View style={styles.transparentContainer}>
      {renderContent()}
    </View>
  );
};

// Specialized headers for common use cases
export const FoodWayHomeHeader: React.FC<{
  location?: string;
  userName?: string;
  onLocationPress?: () => void;
}> = ({ location = 'Current Location', userName, onLocationPress }) => {
  const insets = useSafeAreaInsets();

  return (
    <LinearGradient
      colors={[COLORS.primary, COLORS.secondary]}
      style={styles.gradientContainer}
    >
      <View style={[styles.container, { paddingTop: insets.top + SPACING.sm }]}>
        <View style={styles.homeHeaderRow}>
          <FoodWayMiniLogo size={35} color={COLORS.white} />
          
          <TouchableOpacity
            style={styles.locationButton}
            onPress={onLocationPress}
          >
            <View style={styles.locationContainer}>
              <Ionicons name="location" size={16} color={COLORS.white} />
              <View style={styles.locationTextContainer}>
                <Text style={styles.deliverToText}>Deliver to</Text>
                <Text style={styles.locationText} numberOfLines={1}>
                  {location}
                </Text>
              </View>
              <Ionicons name="chevron-down" size={16} color={COLORS.white} />
            </View>
          </TouchableOpacity>
        </View>
        
        {userName && (
          <View style={styles.welcomeContainer}>
            <Text style={styles.welcomeText}>
              Welcome back, {userName}!
            </Text>
          </View>
        )}
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  gradientContainer: {
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  solidContainer: {
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  transparentContainer: {
    backgroundColor: 'transparent',
  },
  container: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.lg,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    minHeight: 44,
  },
  homeHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftSection: {
    flex: 1,
    alignItems: 'flex-start',
  },
  centerSection: {
    flex: 2,
    alignItems: 'center',
  },
  rightSection: {
    flex: 1,
    alignItems: 'flex-end',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.white,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: COLORS.white,
    opacity: 0.9,
    textAlign: 'center',
    marginTop: 2,
  },
  locationButton: {
    flex: 1,
    marginLeft: SPACING.md,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
  locationTextContainer: {
    flex: 1,
  },
  deliverToText: {
    fontSize: 12,
    color: COLORS.white,
    opacity: 0.8,
  },
  locationText: {
    fontSize: 14,
    color: COLORS.white,
    fontWeight: '600',
  },
  welcomeContainer: {
    marginTop: SPACING.sm,
  },
  welcomeText: {
    fontSize: 16,
    color: COLORS.white,
    fontWeight: '600',
  },
});

export default FoodWayHeader;
