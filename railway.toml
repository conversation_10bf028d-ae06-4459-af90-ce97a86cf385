[build]
builder = "NIXPACKS"
buildCommand = "npm install && npm run build"

[deploy]
startCommand = "npm start"
healthcheckPath = "/health"
healthcheckTimeout = 300
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 3

[environments.production]
[environments.production.variables]
NODE_ENV = "production"
PORT = "3000"

[environments.staging]
[environments.staging.variables]
NODE_ENV = "staging"
PORT = "3000"

# Database configuration
[services.postgres]
image = "postgres:15-alpine"
[services.postgres.variables]
POSTGRES_DB = "foodway_db"
POSTGRES_USER = "foodway_user"

# Redis configuration
[services.redis]
image = "redis:7-alpine"
command = "redis-server --requirepass $REDIS_PASSWORD"

# Volume mounts
[[services.postgres.volumes]]
name = "postgres_data"
mountPath = "/var/lib/postgresql/data"

[[services.redis.volumes]]
name = "redis_data"
mountPath = "/data"
