# 🍔 FoodWay Customer App

A modern, feature-rich food delivery customer application built with React Native, Expo, and Railway backend integration.

## 🚀 Features

- **User Authentication** - Secure login/signup with JWT tokens
- **Restaurant Discovery** - Browse restaurants by location, cuisine, and ratings
- **Menu Browsing** - Detailed menu items with images and descriptions
- **Order Management** - Place orders, track delivery, and view order history
- **Payment Integration** - Stripe payment processing with saved payment methods
- **Real-time Tracking** - Live order tracking with GPS integration
- **Push Notifications** - Order updates and promotional notifications
- **Reviews & Ratings** - Rate restaurants and view customer reviews
- **Address Management** - Save multiple delivery addresses
- **Search & Filters** - Advanced search with dietary preferences and filters

## 🛠 Tech Stack

### Frontend
- **React Native** - Cross-platform mobile development
- **Expo** - Development platform and build tools
- **TypeScript** - Type-safe JavaScript
- **Expo Router** - File-based navigation
- **Zustand** - State management
- **React Query** - Server state management
- **React Native Maps** - Location and mapping
- **Stripe React Native** - Payment processing

### Backend & Infrastructure
- **Railway** - Cloud platform for deployment
- **PostgreSQL** - Primary database
- **Redis** - Caching and session management
- **Node.js** - Runtime environment

## 📋 Prerequisites

- Node.js 18+
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)
- Railway CLI (`npm install -g @railway/cli`)
- iOS Simulator (macOS) or Android Emulator
- Railway account for deployment

## 🚀 Quick Start

### 1. Clone and Install

```bash
git clone <repository-url>
cd customer_app
npm install
```

### 2. Environment Setup

```bash
# Copy environment template
cp .env.example .env.local

# Edit .env.local with your configuration
```

### 3. Start Development Server

```bash
# Start Expo development server
npm start

# Or start with specific platform
npm run android  # Android emulator
npm run ios      # iOS simulator
npm run web      # Web browser
```

## 🚂 Railway Deployment

### Quick Setup

```bash
# Run automated setup
node scripts/railway-setup.js

# Deploy to Railway
npm run railway:deploy
```

### Manual Setup

```bash
# Install Railway CLI
npm install -g @railway/cli

# Login to Railway
railway login

# Initialize project
railway init foodway-customer-app

# Add services
railway add postgresql
railway add redis

# Set environment variables
railway variables set NODE_ENV=production
railway variables set JWT_SECRET=your_secret_here

# Deploy
railway up
```

For detailed deployment instructions, see [Railway Deployment Guide](docs/RAILWAY_DEPLOYMENT.md).

## 📁 Project Structure

```
customer_app/
├── app/                    # Expo Router pages
│   ├── (auth)/            # Authentication screens
│   ├── (tabs)/            # Main tab navigation
│   ├── restaurant/        # Restaurant details
│   ├── checkout.tsx       # Checkout process
│   └── order-tracking/    # Order tracking
├── components/            # Reusable UI components
│   └── ui/               # Base UI components
├── services/             # API and external services
│   ├── api/              # API client and endpoints
│   ├── config/           # Configuration management
│   └── database/         # Database services (PostgreSQL, Redis)
├── store/                # State management (Zustand)
├── types/                # TypeScript type definitions
├── utils/                # Utility functions and constants
├── scripts/              # Deployment and utility scripts
├── docs/                 # Documentation
└── tests/                # Test files
```

## 🔧 Development

### Available Scripts

```bash
# Development
npm start                 # Start Expo dev server
npm run android          # Run on Android
npm run ios              # Run on iOS
npm run web              # Run on web

# Code Quality
npm run lint             # Run ESLint
npm run type-check       # TypeScript type checking
npm test                 # Run tests
npm run test:watch       # Run tests in watch mode
npm run test:coverage    # Run tests with coverage

# Building
npm run build:android    # Build Android APK
npm run build:ios        # Build iOS IPA
npm run build:all        # Build for all platforms

# Railway Deployment
npm run railway:deploy   # Deploy to Railway
npm run railway:logs     # View Railway logs
npm run railway:status   # Check Railway status
```

### Environment Variables

Key environment variables for development:

```bash
# App Configuration
EXPO_PUBLIC_API_URL=http://localhost:3000/api
NODE_ENV=development

# Database (Railway provides these automatically)
DATABASE_URL=postgresql://...
REDIS_URL=redis://...

# Authentication
JWT_SECRET=your_jwt_secret
REFRESH_TOKEN_SECRET=your_refresh_secret

# Payment (Stripe)
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...

# External Services
GOOGLE_MAPS_API_KEY=your_google_maps_key
FIREBASE_SERVER_KEY=your_firebase_key
```

### Database Management

```bash
# Run migrations
node -e "require('./services/database/index.js').DatabaseMigrations.runAllMigrations()"

# Health check
node scripts/health-check.js

# Connect to Railway databases
railway connect postgresql
railway connect redis
```

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- components/ui/Button.test.tsx
```

## 📱 Features in Detail

### Authentication
- Email/password registration and login
- JWT token-based authentication
- Secure token storage with Expo SecureStore
- Password reset functionality
- Email verification

### Restaurant Discovery
- Location-based restaurant search
- Filter by cuisine type, rating, delivery time
- Sort by distance, rating, popularity
- Featured and promoted restaurants
- Restaurant details with photos and info

### Menu & Ordering
- Browse restaurant menus by category
- Item details with images, descriptions, allergens
- Customizable items with special instructions
- Shopping cart with quantity management
- Order summary and checkout

### Payment Processing
- Stripe integration for secure payments
- Save multiple payment methods
- Support for cards, Apple Pay, Google Pay
- Order total calculation with taxes and fees
- Payment confirmation and receipts

### Order Tracking
- Real-time order status updates
- GPS tracking for delivery
- Estimated delivery time
- Push notifications for status changes
- Order history and reordering

### User Profile
- Profile management with photo upload
- Multiple delivery addresses
- Saved payment methods
- Order history and favorites
- Notification preferences

## 🔒 Security

- JWT token authentication
- Secure API communication with HTTPS
- Input validation and sanitization
- Rate limiting for API endpoints
- Secure storage of sensitive data
- CORS configuration for web security

## 📊 Performance

- Redis caching for frequently accessed data
- Connection pooling for database efficiency
- Image optimization and lazy loading
- Efficient state management with Zustand
- Query optimization with React Query
- Bundle optimization with Expo

## 🚀 Deployment

### Railway Deployment

1. **Setup Railway project:**
   ```bash
   node scripts/railway-setup.js
   ```

2. **Deploy application:**
   ```bash
   npm run railway:deploy
   ```

3. **Monitor deployment:**
   ```bash
   railway logs --follow
   railway status
   ```

### Mobile App Distribution

1. **Build for production:**
   ```bash
   npm run build:android
   npm run build:ios
   ```

2. **Submit to app stores:**
   ```bash
   npm run submit:android
   npm run submit:ios
   ```

## 📚 Documentation

- [Railway Deployment Guide](docs/RAILWAY_DEPLOYMENT.md)
- [Database Setup Guide](docs/DATABASE_SETUP.md)
- [Architecture Overview](docs/ARCHITECTURE.md)
- [API Documentation](docs/README.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- 📧 Email: <EMAIL>
- 📞 Phone: ******-FOODWAY
- 💬 Discord: [Join our community](https://discord.gg/foodway)
- 📖 Documentation: [docs.foodway.com](https://docs.foodway.com)

## 🙏 Acknowledgments

- [Expo](https://expo.dev) for the amazing development platform
- [Railway](https://railway.app) for seamless deployment and hosting
- [Stripe](https://stripe.com) for secure payment processing
- [React Native](https://reactnative.dev) for cross-platform development
