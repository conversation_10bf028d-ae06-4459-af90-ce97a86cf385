import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    TextInput,
    TouchableOpacity
} from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { RegisterForm } from '@/types';
import { FoodWayMiniLogo } from '../../components/FoodWayLogo';

interface FormData extends RegisterForm {
  confirmPassword: string;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  password?: string;
  confirmPassword?: string;
}

export default function RegisterScreen() {
  const colorScheme = useColorScheme();

  // Import auth store with relative path
  const { useAuthStore } = require('../../store/authStore');
  const { register, isLoading, clearError } = useAuthStore();

  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [agreeToTerms, setAgreeToTerms] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // First Name validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    } else if (formData.firstName.trim().length < 2) {
      newErrors.firstName = 'First name must be at least 2 characters';
    }

    // Last Name validation
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    } else if (formData.lastName.trim().length < 2) {
      newErrors.lastName = 'Last name must be at least 2 characters';
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Phone validation
    const phoneRegex = /^\+?[\d\s\-()]{10,}$/;
    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!phoneRegex.test(formData.phone.replace(/\s/g, ''))) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = 'Password must contain uppercase, lowercase, and number';
    }

    // Confirm Password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    const newFormData = { ...formData, [field]: value };
    setFormData(newFormData);
    
    // Only clear the error for the specific field being edited if it now has valid content
    if (errors[field] && value.trim()) {
      // Perform basic validation for the field to see if we should clear the error
      let shouldClearError = false;
      
      switch (field) {
        case 'firstName':
        case 'lastName':
          shouldClearError = value.trim().length >= 2;
          break;
        case 'email':
          shouldClearError = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
          break;
        case 'phone':
          shouldClearError = /^\+?[\d\s\-()]{10,}$/.test(value.replace(/\s/g, ''));
          break;
        case 'password':
          shouldClearError = value.length >= 8 && /(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value);
          break;
        case 'confirmPassword':
          shouldClearError = value === newFormData.password && value.length > 0;
          break;
        default:
          shouldClearError = value.trim().length > 0;
      }
      
      if (shouldClearError) {
        setErrors(prev => ({ ...prev, [field]: undefined }));
      }
    }
    
    // Special case: if password field is being changed and confirmPassword has an error,
    // check if we should clear the confirmPassword error too
    if (field === 'password' && errors.confirmPassword && formData.confirmPassword) {
      if (value === formData.confirmPassword && value.length > 0) {
        setErrors(prev => ({ ...prev, confirmPassword: undefined }));
      }
    }
  };

  const handleRegister = async () => {
    console.log('Register button clicked');
    console.log('Form data:', formData);

    // Clear any previous errors
    clearError();

    if (!validateForm()) {
      console.log('Form validation failed');
      return;
    }

    if (!agreeToTerms) {
      Alert.alert('Terms Required', 'Please agree to the Terms of Service and Privacy Policy');
      return;
    }

    try {
      console.log('Starting registration...');

      // Prepare registration data (exclude confirmPassword)
      const registrationData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,
        password: formData.password,
      };

      console.log('Sending registration data to backend:', registrationData);
      console.log('API URL:', process.env.EXPO_PUBLIC_API_URL);

      // Use auth store register method instead of direct fetch
      await register(registrationData);

      // ✅ Success!
      console.log("User registered successfully!");
      Alert.alert(
        'Success!',
        'Your account has been created successfully. Welcome to FoodWay!',
        [
          {
            text: 'OK',
            onPress: () => router.replace('/(tabs)'),
          },
        ]
      );
    } catch (error) {
      // ❌ Only runs if the actual network is broken (e.g., no internet)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.error("Registration error:", errorMessage);
      console.error('Error details:', error);

      Alert.alert(
        'Registration Failed',
        errorMessage.includes('Network')
          ? 'Please check your internet connection and try again.'
          : errorMessage
      );
    }
  };

  const renderInput = (
    field: keyof FormData,
    placeholder: string,
    icon: string,
    options?: {
      secureTextEntry?: boolean;
      keyboardType?: 'default' | 'email-address' | 'phone-pad';
      autoCapitalize?: 'none' | 'sentences' | 'words';
      showPasswordToggle?: boolean;
      passwordVisible?: boolean;
      onTogglePassword?: () => void;
    }
  ) => (
    <ThemedView style={styles.inputContainer}>
      <ThemedView style={[
        styles.inputWrapper,
        { 
          backgroundColor: Colors[colorScheme ?? 'light'].background,
          borderColor: errors[field] ? '#FF6B35' : Colors[colorScheme ?? 'light'].border,
        }
      ]}>
        <IconSymbol 
          name={icon as any} 
          size={20} 
          color={errors[field] ? '#FF6B35' : Colors[colorScheme ?? 'light'].icon} 
        />
        <TextInput
          style={[styles.input, { color: Colors[colorScheme ?? 'light'].text }]}
          placeholder={placeholder}
          placeholderTextColor={Colors[colorScheme ?? 'light'].icon}
          value={formData[field]}
          onChangeText={(value) => handleInputChange(field, value)}
          secureTextEntry={options?.secureTextEntry}
          keyboardType={options?.keyboardType || 'default'}
          autoCapitalize={options?.autoCapitalize || 'sentences'}
          autoCorrect={false}
        />
        {options?.showPasswordToggle && (
          <TouchableOpacity onPress={options.onTogglePassword}>
            <IconSymbol
              name={options.passwordVisible ? 'eye.slash' : 'eye'}
              size={20}
              color={Colors[colorScheme ?? 'light'].icon}
            />
          </TouchableOpacity>
        )}
      </ThemedView>
      {errors[field] && (
        <ThemedText style={styles.errorText}>{errors[field]}</ThemedText>
      )}
    </ThemedView>
  );

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Header */}
        <LinearGradient
          colors={['#FF6B35', '#FF8A65']}
          style={styles.header}
        >
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <IconSymbol name="chevron.left" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <ThemedView style={styles.headerContent}>
            <FoodWayMiniLogo size={50} color="#FFFFFF" />
            <ThemedText style={styles.headerTitle}>Create Account</ThemedText>
            <ThemedText style={styles.headerSubtitle}>
              Join FoodWay and discover amazing food
            </ThemedText>
          </ThemedView>
        </LinearGradient>

        {/* Form */}
        <ThemedView style={styles.formContainer}>
          {/* Name Fields */}
          <ThemedView style={styles.nameRow}>
            <ThemedView style={styles.nameField}>
              {renderInput('firstName', 'First Name', 'person', { autoCapitalize: 'words' })}
            </ThemedView>
            <ThemedView style={styles.nameField}>
              {renderInput('lastName', 'Last Name', 'person', { autoCapitalize: 'words' })}
            </ThemedView>
          </ThemedView>

          {/* Email */}
          {renderInput('email', 'Email Address', 'envelope', {
            keyboardType: 'email-address',
            autoCapitalize: 'none',
          })}

          {/* Phone */}
          {renderInput('phone', 'Phone Number', 'phone', {
            keyboardType: 'phone-pad',
          })}

          {/* Password */}
          {renderInput('password', 'Password', 'lock', {
            secureTextEntry: !showPassword,
            showPasswordToggle: true,
            passwordVisible: showPassword,
            onTogglePassword: () => setShowPassword(!showPassword),
            autoCapitalize: 'none',
          })}

          {/* Confirm Password */}
          {renderInput('confirmPassword', 'Confirm Password', 'lock', {
            secureTextEntry: !showConfirmPassword,
            showPasswordToggle: true,
            passwordVisible: showConfirmPassword,
            onTogglePassword: () => setShowConfirmPassword(!showConfirmPassword),
            autoCapitalize: 'none',
          })}

          {/* Password Requirements */}
          <ThemedView style={styles.passwordRequirements}>
            <ThemedText style={styles.requirementsTitle}>Password must contain:</ThemedText>
            <ThemedView style={styles.requirementItem}>
              <IconSymbol
                name={formData.password.length >= 8 ? 'checkmark.circle.fill' : 'circle'}
                size={16}
                color={formData.password.length >= 8 ? '#4CAF50' : Colors[colorScheme ?? 'light'].icon}
              />
              <ThemedText style={styles.requirementText}>At least 8 characters</ThemedText>
            </ThemedView>
            <ThemedView style={styles.requirementItem}>
              <IconSymbol
                name={/(?=.*[a-z])(?=.*[A-Z])/.test(formData.password) ? 'checkmark.circle.fill' : 'circle'}
                size={16}
                color={/(?=.*[a-z])(?=.*[A-Z])/.test(formData.password) ? '#4CAF50' : Colors[colorScheme ?? 'light'].icon}
              />
              <ThemedText style={styles.requirementText}>Upper and lowercase letters</ThemedText>
            </ThemedView>
            <ThemedView style={styles.requirementItem}>
              <IconSymbol
                name={/(?=.*\d)/.test(formData.password) ? 'checkmark.circle.fill' : 'circle'}
                size={16}
                color={/(?=.*\d)/.test(formData.password) ? '#4CAF50' : Colors[colorScheme ?? 'light'].icon}
              />
              <ThemedText style={styles.requirementText}>At least one number</ThemedText>
            </ThemedView>
          </ThemedView>

          {/* Terms and Conditions */}
          <TouchableOpacity
            style={styles.termsContainer}
            onPress={() => setAgreeToTerms(!agreeToTerms)}
          >
            <IconSymbol
              name={agreeToTerms ? 'checkmark.square.fill' : 'square'}
              size={20}
              color={agreeToTerms ? '#4CAF50' : Colors[colorScheme ?? 'light'].icon}
            />
            <ThemedView style={styles.termsTextContainer}>
              <ThemedText style={styles.termsText}>
                I agree to the{' '}
                <ThemedText style={styles.termsLink}>Terms of Service</ThemedText>
                {' '}and{' '}
                <ThemedText style={styles.termsLink}>Privacy Policy</ThemedText>
              </ThemedText>
            </ThemedView>
          </TouchableOpacity>

          {/* Register Button */}
          <TouchableOpacity
            style={[
              styles.registerButton,
              (!agreeToTerms || isLoading) && styles.registerButtonDisabled
            ]}
            onPress={handleRegister}
            disabled={!agreeToTerms || isLoading}
          >
            {isLoading ? (
              <ThemedText style={styles.registerButtonText}>Creating Account...</ThemedText>
            ) : (
              <>
                <ThemedText style={styles.registerButtonText}>Create Account</ThemedText>
                <IconSymbol name="arrow.right" size={20} color="#FFFFFF" />
              </>
            )}
          </TouchableOpacity>

          {/* Social Registration */}
          <ThemedView style={styles.socialSection}>
            <ThemedView style={styles.dividerContainer}>
              <ThemedView style={[styles.divider, { backgroundColor: Colors[colorScheme ?? 'light'].border }]} />
              <ThemedText style={styles.dividerText}>Or continue with</ThemedText>
              <ThemedView style={[styles.divider, { backgroundColor: Colors[colorScheme ?? 'light'].border }]} />
            </ThemedView>

            <ThemedView style={styles.socialButtons}>
              <TouchableOpacity style={[styles.socialButton, { backgroundColor: Colors[colorScheme ?? 'light'].background }]}>
                <IconSymbol name="logo.google" size={24} color="#DB4437" />
                <ThemedText style={styles.socialButtonText}>Google</ThemedText>
              </TouchableOpacity>
              <TouchableOpacity style={[styles.socialButton, { backgroundColor: Colors[colorScheme ?? 'light'].background }]}>
                <IconSymbol name="logo.apple" size={24} color={Colors[colorScheme ?? 'light'].text} />
                <ThemedText style={styles.socialButtonText}>Apple</ThemedText>
              </TouchableOpacity>
            </ThemedView>
          </ThemedView>

          {/* Login Link */}
          <ThemedView style={styles.loginContainer}>
            <ThemedText style={styles.loginText}>Already have an account? </ThemedText>
            <TouchableOpacity onPress={() => router.push('/login')}>
              <ThemedText style={styles.loginLink}>Sign In</ThemedText>
            </TouchableOpacity>
          </ThemedView>


        </ThemedView>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  header: {
    paddingTop: 50,
    paddingHorizontal: 20,
    paddingBottom: 30,
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
  },
  backButton: {
    alignSelf: 'flex-start',
    padding: 8,
    marginBottom: 20,
  },
  headerContent: {
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.9,
    textAlign: 'center',
  },
  formContainer: {
    flex: 1,
    padding: 20,
    marginTop: -15,
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
  },
  nameRow: {
    flexDirection: 'row',
    gap: 15,
    backgroundColor: 'transparent',
  },
  nameField: {
    flex: 1,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 15,
    paddingHorizontal: 15,
    paddingVertical: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  input: {
    flex: 1,
    fontSize: 16,
    marginLeft: 10,
  },
  errorText: {
    color: '#FF6B35',
    fontSize: 12,
    marginTop: 5,
    marginLeft: 15,
  },
  passwordRequirements: {
    marginBottom: 20,
    padding: 15,
    borderRadius: 10,
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
  },
  requirementsTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 10,
    color: '#4CAF50',
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
    backgroundColor: 'transparent',
  },
  requirementText: {
    fontSize: 12,
    marginLeft: 8,
    opacity: 0.8,
  },
  termsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 30,
    paddingHorizontal: 5,
  },
  termsTextContainer: {
    flex: 1,
    marginLeft: 10,
  },
  termsText: {
    fontSize: 14,
    lineHeight: 20,
    opacity: 0.8,
  },
  termsLink: {
    color: '#FF6B35',
    fontWeight: '600',
  },
  registerButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FF6B35',
    paddingVertical: 15,
    borderRadius: 15,
    marginBottom: 30,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  registerButtonDisabled: {
    backgroundColor: '#ccc',
    elevation: 0,
  },
  registerButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginRight: 10,
  },
  socialSection: {
    marginBottom: 30,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    backgroundColor: 'transparent',
  },
  divider: {
    flex: 1,
    height: 1,
  },
  dividerText: {
    fontSize: 14,
    opacity: 0.6,
    marginHorizontal: 15,
  },
  socialButtons: {
    flexDirection: 'row',
    gap: 15,
    backgroundColor: 'transparent',
  },
  socialButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    borderRadius: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  socialButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  loginText: {
    fontSize: 16,
    opacity: 0.7,
  },
  loginLink: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF6B35',
  },
});
