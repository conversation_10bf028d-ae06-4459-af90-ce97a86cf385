import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, SPACING, BORDER_RADIUS, TYPOGRAPHY } from '../../utils/constants';

interface ColorSwatchProps {
  name: string;
  color: string;
  description: string;
  usage: string;
}

const ColorSwatch: React.FC<ColorSwatchProps> = ({ name, color, description, usage }) => (
  <View style={styles.swatchContainer}>
    <View style={[styles.colorSwatch, { backgroundColor: color }]} />
    <View style={styles.swatchInfo}>
      <Text style={styles.colorName}>{name}</Text>
      <Text style={styles.colorCode}>{color}</Text>
      <Text style={styles.colorDescription}>{description}</Text>
      <Text style={styles.colorUsage}>Usage: {usage}</Text>
    </View>
  </View>
);

const ButtonExample: React.FC<{ title: string; style: any; textStyle: any }> = ({ 
  title, 
  style, 
  textStyle 
}) => (
  <TouchableOpacity style={[styles.exampleButton, style]}>
    <Text style={[styles.buttonText, textStyle]}>{title}</Text>
  </TouchableOpacity>
);

export const ColorShowcase: React.FC = () => {
  const colorData = [
    {
      name: 'Primary Red',
      color: COLORS.primary,
      description: 'Bold red for main CTAs',
      usage: 'Order Now buttons, primary actions'
    },
    {
      name: 'Primary Dark',
      color: COLORS.primaryDark,
      description: 'Darker red for pressed states',
      usage: 'Button pressed states, navbar'
    },
    {
      name: 'Primary Light',
      color: COLORS.primaryLight,
      description: 'Light red for highlights',
      usage: 'Badges, offer highlights'
    },
    {
      name: 'Secondary Dark Gray',
      color: COLORS.secondary,
      description: 'Dark gray for headings',
      usage: 'Main headings, important text'
    },
    {
      name: 'Text Primary',
      color: COLORS.text,
      description: 'Primary text color',
      usage: 'Body text, descriptions'
    },
    {
      name: 'Success Green',
      color: COLORS.success,
      description: 'Green for success states',
      usage: 'Order confirmed, delivery status'
    },
    {
      name: 'Warning Orange',
      color: COLORS.warning,
      description: 'Orange for warnings',
      usage: 'Prep time, delays'
    },
    {
      name: 'Background White',
      color: COLORS.background,
      description: 'Pure white background',
      usage: 'Main app background'
    },
  ];

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>🎨 FoodWay Color Scheme</Text>
      <Text style={styles.subtitle}>Red + White + Dark Gray Theme</Text>
      
      {/* Color Swatches */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Color Palette</Text>
        {colorData.map((color, index) => (
          <ColorSwatch key={index} {...color} />
        ))}
      </View>

      {/* Button Examples */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Button Examples</Text>
        
        <ButtonExample
          title="Order Now"
          style={{ backgroundColor: COLORS.ctaButton }}
          textStyle={{ color: COLORS.textOnPrimary }}
        />
        
        <ButtonExample
          title="Secondary Action"
          style={{ 
            backgroundColor: COLORS.secondaryButton,
            marginTop: SPACING.sm 
          }}
          textStyle={{ color: COLORS.textOnPrimary }}
        />
        
        <ButtonExample
          title="Outline Button"
          style={{ 
            backgroundColor: 'transparent',
            borderWidth: 1,
            borderColor: COLORS.border,
            marginTop: SPACING.sm 
          }}
          textStyle={{ color: COLORS.text }}
        />
      </View>

      {/* Text Examples */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Text Hierarchy</Text>
        
        <Text style={[styles.textExample, { color: COLORS.text }]}>
          Primary Text - Restaurant Name
        </Text>
        <Text style={[styles.textExample, { color: COLORS.textSecondary }]}>
          Secondary Text - Description
        </Text>
        <Text style={[styles.textExample, { color: COLORS.textTertiary }]}>
          Tertiary Text - Helper text
        </Text>
        <Text style={[styles.textExample, { color: COLORS.textDisabled }]}>
          Disabled Text - Unavailable
        </Text>
      </View>

      {/* Status Examples */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Status Indicators</Text>
        
        <View style={styles.statusContainer}>
          <Ionicons name="checkmark-circle" size={24} color={COLORS.success} />
          <Text style={[styles.statusText, { color: COLORS.success }]}>
            Order Confirmed
          </Text>
        </View>
        
        <View style={styles.statusContainer}>
          <Ionicons name="time" size={24} color={COLORS.warning} />
          <Text style={[styles.statusText, { color: COLORS.warning }]}>
            Preparing (15-20 min)
          </Text>
        </View>
        
        <View style={styles.statusContainer}>
          <Ionicons name="close-circle" size={24} color={COLORS.error} />
          <Text style={[styles.statusText, { color: COLORS.error }]}>
            Item Unavailable
          </Text>
        </View>
        
        <View style={styles.statusContainer}>
          <Ionicons name="information-circle" size={24} color={COLORS.info} />
          <Text style={[styles.statusText, { color: COLORS.info }]}>
            Delivery Update
          </Text>
        </View>
      </View>

      {/* Card Example */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Card Example</Text>
        <View style={styles.cardExample}>
          <Text style={styles.cardTitle}>Mario's Pizza Palace</Text>
          <Text style={styles.cardDescription}>
            Authentic Italian pizza made with fresh ingredients
          </Text>
          <View style={styles.cardFooter}>
            <Text style={styles.cardRating}>⭐ 4.5 (324 reviews)</Text>
            <Text style={styles.cardDelivery}>25-35 min • $2.99 delivery</Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
    padding: SPACING.lg,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.xs,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },
  section: {
    marginBottom: SPACING.xl,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.md,
  },
  swatchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
    padding: SPACING.sm,
    backgroundColor: COLORS.surface,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  colorSwatch: {
    width: 50,
    height: 50,
    borderRadius: BORDER_RADIUS.sm,
    marginRight: SPACING.md,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  swatchInfo: {
    flex: 1,
  },
  colorName: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.text,
  },
  colorCode: {
    fontSize: 12,
    color: COLORS.textSecondary,
    fontFamily: 'monospace',
  },
  colorDescription: {
    fontSize: 12,
    color: COLORS.textTertiary,
    marginTop: 2,
  },
  colorUsage: {
    fontSize: 11,
    color: COLORS.textTertiary,
    fontStyle: 'italic',
    marginTop: 2,
  },
  exampleButton: {
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    borderRadius: BORDER_RADIUS.md,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  textExample: {
    fontSize: 16,
    marginBottom: SPACING.xs,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  statusText: {
    fontSize: 14,
    marginLeft: SPACING.sm,
    fontWeight: '500',
  },
  cardExample: {
    backgroundColor: COLORS.surface,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  cardDescription: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cardRating: {
    fontSize: 12,
    color: COLORS.textTertiary,
  },
  cardDelivery: {
    fontSize: 12,
    color: COLORS.textTertiary,
  },
});

export default ColorShowcase;
