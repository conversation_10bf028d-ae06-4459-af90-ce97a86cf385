// Comprehensive Backend Endpoint Testing Script for FoodWay Customer App
// Tests all endpoints against the live Railway backend

const axios = require('axios');
const BASE_URL = 'https://backend-production-f106.up.railway.app/api/v1';

// Create axios instance
const apiClient = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Test configuration
const TEST_CONFIG = {
  timeout: 10000,
  testUser: {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    firstName: 'Test',
    lastName: 'User',
    phone: '+**********'
  }
};

let authToken = null;
let refreshToken = null;
let testUserId = null;

// Utility functions
const makeRequest = async (endpoint, options = {}) => {
  const config = {
    url: endpoint,
    timeout: TEST_CONFIG.timeout,
    method: options.method || 'GET',
    data: options.body ? JSON.parse(options.body) : options.data,
    headers: {
      ...options.headers
    },
    ...options
  };

  // Handle special case for health endpoint which is at root level
  if (endpoint.startsWith('/health')) {
    config.baseURL = 'https://backend-production-f106.up.railway.app';
  }

  if (authToken && !options.skipAuth) {
    config.headers.Authorization = `Bearer ${authToken}`;
  }

  console.log(`\n🔄 Testing: ${config.method} ${endpoint}`);

  try {
    const response = await apiClient.request(config);

    const result = {
      status: response.status,
      ok: response.status >= 200 && response.status < 300,
      data: response.data,
      headers: response.headers
    };

    if (result.ok) {
      console.log(`✅ SUCCESS: ${response.status} - ${endpoint}`);
      console.log(`   Response:`, JSON.stringify(response.data, null, 2).substring(0, 200) + '...');
    } else {
      console.log(`❌ FAILED: ${response.status} - ${endpoint}`);
      console.log(`   Error:`, response.data);
    }

    return result;
  } catch (error) {
    const status = error.response?.status || 0;
    const data = error.response?.data || null;

    console.log(`💥 ERROR: ${endpoint} - ${error.message}`);
    if (data) {
      console.log(`   Error Response:`, data);
    }

    return {
      status,
      ok: false,
      error: error.message,
      data
    };
  }
};

// Test categories
const tests = {
  // System/Health endpoints
  system: [
    { endpoint: '/health', method: 'GET', skipAuth: true },
  ],

  // Authentication endpoints
  auth: [
    {
      endpoint: '/auth/register',
      method: 'POST',
      skipAuth: true,
      body: {
        ...TEST_CONFIG.testUser,
        email: `test${Date.now()}@foodway.com` // Use unique email
      }
    },
    {
      endpoint: '/auth/login',
      method: 'POST',
      skipAuth: true,
      body: {
        email: TEST_CONFIG.testUser.email,
        password: TEST_CONFIG.testUser.password
      }
    },
    { endpoint: '/auth/refresh', method: 'POST', skipAuth: true },
    { endpoint: '/auth/logout', method: 'POST' },
    {
      endpoint: '/auth/forgot-password',
      method: 'POST',
      skipAuth: true,
      body: { email: TEST_CONFIG.testUser.email }
    },
    {
      endpoint: '/auth/verify-otp',
      method: 'POST',
      skipAuth: true,
      body: { email: TEST_CONFIG.testUser.email, otp: '123456' }
    }
  ],

  // User endpoints
  user: [
    { endpoint: '/user/profile', method: 'GET' },
    {
      endpoint: '/user/profile',
      method: 'PUT',
      body: { firstName: 'Updated', lastName: 'Name' }
    },
    { endpoint: '/user/addresses', method: 'GET' },
    {
      endpoint: '/user/addresses',
      method: 'POST',
      body: {
        street: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zipCode: '12345',
        country: 'US',
        isDefault: false,
        label: 'Home'
      }
    },
    { endpoint: '/user/payment-methods', method: 'GET' },
    { endpoint: '/user/favorites', method: 'GET' },
    { endpoint: '/user/reviews', method: 'GET' },
    { endpoint: '/user/notification-settings', method: 'GET' }
  ],

  // Restaurant endpoints
  restaurants: [
    { endpoint: '/restaurants', method: 'GET', skipAuth: true },
    { endpoint: '/restaurants/featured', method: 'GET', skipAuth: true, params: { limit: 10 } },
    { endpoint: '/restaurants/nearby', method: 'GET', skipAuth: true, params: { lat: 40.7128, lng: -74.0060, radius: 5000 } },
    { endpoint: '/restaurants/search', method: 'GET', skipAuth: true, params: { q: 'pizza', lat: 40.7128, lng: -74.0060 } },
    { endpoint: '/categories', method: 'GET', skipAuth: true }
  ],

  // Order endpoints
  orders: [
    { endpoint: '/orders', method: 'GET' },
    {
      endpoint: '/orders',
      method: 'POST',
      body: {
        restaurantId: 'test-restaurant-id',
        items: [
          { menuItemId: 'test-item-id', quantity: 2, price: 12.99 }
        ],
        deliveryAddress: {
          street: '123 Test St',
          city: 'Test City',
          state: 'TS',
          zipCode: '12345'
        },
        paymentMethodId: 'test-payment-method'
      }
    }
  ],

  // Review endpoints
  reviews: [
    {
      endpoint: '/reviews',
      method: 'POST',
      body: {
        restaurantId: 'test-restaurant-id',
        orderId: 'test-order-id',
        rating: 5,
        comment: 'Great food!',
        foodRating: 5,
        serviceRating: 4,
        deliveryRating: 5
      }
    }
  ],

  // Notification endpoints
  notifications: [
    { endpoint: '/notifications', method: 'GET' },
    { endpoint: '/notifications/read-all', method: 'PATCH' }
  ],

  // Promotion endpoints
  promotions: [
    { endpoint: '/promotions', method: 'GET', skipAuth: true },
    {
      endpoint: '/promotions/validate',
      method: 'POST',
      skipAuth: true,
      body: { code: 'TESTCODE' }
    }
  ]
};

// Main testing function
const runTests = async () => {
  console.log('🚀 Starting FoodWay Backend API Tests');
  console.log(`📍 Testing against: ${BASE_URL}`);
  console.log('=' * 60);

  const results = {
    total: 0,
    passed: 0,
    failed: 0,
    errors: 0,
    details: {}
  };

  for (const [category, endpoints] of Object.entries(tests)) {
    console.log(`\n📂 Testing ${category.toUpperCase()} endpoints:`);
    console.log('-' * 40);

    results.details[category] = [];

    for (const test of endpoints) {
      results.total++;

      const options = {
        method: test.method,
        skipAuth: test.skipAuth
      };

      if (test.body) {
        options.body = JSON.stringify(test.body);
      }

      if (test.params) {
        const params = new URLSearchParams(test.params);
        test.endpoint += `?${params.toString()}`;
      }

      const result = await makeRequest(test.endpoint, options);

      // Store auth tokens from login
      if (test.endpoint === '/auth/login' && result.ok && result.data?.data) {
        authToken = result.data.data.token;
        refreshToken = result.data.data.refreshToken;
        testUserId = result.data.data.user?.id;
        console.log('🔑 Auth tokens stored for subsequent tests');
      }

      // Update refresh token test with actual token
      if (test.endpoint === '/auth/refresh' && refreshToken) {
        const refreshResult = await makeRequest('/auth/refresh', {
          method: 'POST',
          skipAuth: true,
          body: JSON.stringify({ refreshToken })
        });
        result.status = refreshResult.status;
        result.ok = refreshResult.ok;
        result.data = refreshResult.data;
      }

      if (result.status === 0) {
        results.errors++;
      } else if (result.ok) {
        results.passed++;
      } else {
        results.failed++;
      }

      results.details[category].push({
        endpoint: test.endpoint,
        method: test.method,
        status: result.status,
        ok: result.ok,
        error: result.error,
        message: result.data?.message || result.data?.error?.message
      });

      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  // Print summary
  console.log('\n' + '=' * 60);
  console.log('📊 TEST SUMMARY');
  console.log('=' * 60);
  console.log(`Total Tests: ${results.total}`);
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`💥 Errors: ${results.errors}`);
  console.log(`📈 Success Rate: ${((results.passed / results.total) * 100).toFixed(1)}%`);

  // Detailed breakdown
  console.log('\n📋 DETAILED BREAKDOWN:');
  for (const [category, tests] of Object.entries(results.details)) {
    const categoryPassed = tests.filter(t => t.ok).length;
    const categoryTotal = tests.length;
    console.log(`\n${category.toUpperCase()}: ${categoryPassed}/${categoryTotal} passed`);

    tests.forEach(test => {
      const status = test.status === 0 ? '💥' : test.ok ? '✅' : '❌';
      const message = test.message ? ` - ${test.message}` : '';
      console.log(`  ${status} ${test.method} ${test.endpoint} (${test.status})${message}`);
    });
  }

  return results;
};

// Run the tests
runTests().catch(console.error);
