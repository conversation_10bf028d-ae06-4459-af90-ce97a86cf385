// Test Authentication Flow for FoodWay Backend
const axios = require('axios');

const BASE_URL = 'https://backend-production-f106.up.railway.app/api/v1';

const apiClient = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

async function testAuthFlow() {
  console.log('🔐 Testing FoodWay Authentication Flow');
  console.log('=' * 50);

  try {
    // Step 1: Register a new user
    console.log('\n1️⃣ Testing User Registration...');
    const registerData = {
      email: `testuser${Date.now()}@foodway.com`,
      password: 'TestPassword123!',
      firstName: 'Test',
      lastName: 'User',
      phone: '+1234567890'
    };

    const registerResponse = await apiClient.post('/auth/register', registerData);
    console.log('✅ Registration successful:', registerResponse.status);
    console.log('User ID:', registerResponse.data.data.user.id);
    console.log('Email:', registerResponse.data.data.user.email);

    // Step 2: Try to login with the same credentials
    console.log('\n2️⃣ Testing Login with registered user...');
    const loginData = {
      email: registerData.email,
      password: registerData.password
    };

    const loginResponse = await apiClient.post('/auth/login', loginData);
    console.log('✅ Login successful:', loginResponse.status);
    
    const { token, refreshToken, user } = loginResponse.data.data;
    console.log('🔑 Token received:', token ? 'Yes' : 'No');
    console.log('🔄 Refresh token received:', refreshToken ? 'Yes' : 'No');
    console.log('👤 User data:', user.email);

    // Step 3: Test protected endpoint with token
    console.log('\n3️⃣ Testing Protected Endpoint...');
    const profileResponse = await apiClient.get('/user/profile', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('✅ Profile access successful:', profileResponse.status);
    console.log('Profile data:', profileResponse.data.data);

    // Step 4: Test token refresh
    console.log('\n4️⃣ Testing Token Refresh...');
    const refreshResponse = await apiClient.post('/auth/refresh', {
      refreshToken: refreshToken
    });
    console.log('✅ Token refresh successful:', refreshResponse.status);
    console.log('🔑 New token received:', refreshResponse.data.data.token ? 'Yes' : 'No');

    // Step 5: Test logout
    console.log('\n5️⃣ Testing Logout...');
    const logoutResponse = await apiClient.post('/auth/logout', {}, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('✅ Logout successful:', logoutResponse.status);

    console.log('\n🎉 All authentication tests passed!');
    return true;

  } catch (error) {
    console.log('\n❌ Authentication test failed:');
    console.log('Error:', error.message);
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Response:', error.response.data);
    }
    return false;
  }
}

// Test with existing user credentials
async function testExistingUser() {
  console.log('\n🔄 Testing with existing test user...');
  
  try {
    const loginData = {
      email: '<EMAIL>',
      password: 'TestPassword123!'
    };

    const loginResponse = await apiClient.post('/auth/login', loginData);
    console.log('✅ Existing user login successful:', loginResponse.status);
    return loginResponse.data.data.token;
  } catch (error) {
    console.log('❌ Existing user login failed:', error.response?.status);
    console.log('Error:', error.response?.data);
    return null;
  }
}

// Test all protected endpoints with valid token
async function testProtectedEndpoints(token) {
  console.log('\n🛡️ Testing Protected Endpoints...');
  
  const protectedEndpoints = [
    { method: 'GET', url: '/user/profile', name: 'User Profile' },
    { method: 'GET', url: '/user/addresses', name: 'User Addresses' },
    { method: 'GET', url: '/user/payment-methods', name: 'Payment Methods' },
    { method: 'GET', url: '/orders', name: 'User Orders' },
    { method: 'GET', url: '/notifications', name: 'Notifications' },
  ];

  const results = [];
  
  for (const endpoint of protectedEndpoints) {
    try {
      const response = await apiClient.request({
        method: endpoint.method,
        url: endpoint.url,
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log(`✅ ${endpoint.name}: ${response.status}`);
      results.push({ ...endpoint, status: response.status, success: true });
    } catch (error) {
      console.log(`❌ ${endpoint.name}: ${error.response?.status || 'ERROR'}`);
      results.push({ 
        ...endpoint, 
        status: error.response?.status || 0, 
        success: false,
        error: error.response?.data?.error?.message || error.message
      });
    }
  }
  
  return results;
}

// Main execution
async function main() {
  console.log('🚀 Starting Comprehensive Authentication Tests\n');
  
  // Test 1: Full auth flow with new user
  const authFlowSuccess = await testAuthFlow();
  
  // Test 2: Try with existing user
  const existingUserToken = await testExistingUser();
  
  // Test 3: If we have a token, test protected endpoints
  if (existingUserToken) {
    await testProtectedEndpoints(existingUserToken);
  }
  
  console.log('\n📊 Test Summary:');
  console.log('Auth Flow:', authFlowSuccess ? '✅ PASS' : '❌ FAIL');
  console.log('Existing User:', existingUserToken ? '✅ PASS' : '❌ FAIL');
  
  if (!authFlowSuccess && !existingUserToken) {
    console.log('\n🔴 CRITICAL: Authentication system is not working properly');
    console.log('This prevents testing of all protected endpoints');
  }
}

main().catch(console.error);
