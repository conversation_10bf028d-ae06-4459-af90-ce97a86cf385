import React from 'react';
import { View, Text } from 'react-native';

// Web-specific Stripe provider that doesn't use native modules
export const StripeProvider: React.FC<{ children: React.ReactNode; publishableKey: string }> = ({ 
  children, 
  publishableKey 
}) => {
  return (
    <View>
      {children}
    </View>
  );
};

// Mock <PERSON>ield for web
export const CardField: React.FC<any> = (props) => {
  return (
    <View style={{ 
      height: 50, 
      borderWidth: 1, 
      borderColor: '#ccc', 
      borderRadius: 8, 
      padding: 10,
      justifyContent: 'center'
    }}>
      <Text style={{ color: '#666' }}>
        Card input not available on web. Please use mobile app for payments.
      </Text>
    </View>
  );
};

// Mock useStripe hook
export const useStripe = () => {
  return {
    confirmPayment: async () => {
      throw new Error('Payment not available on web platform');
    },
    createPaymentMethod: async () => {
      throw new Error('Payment not available on web platform');
    },
  };
};

// Mock useConfirmPayment hook
export const useConfirmPayment = () => {
  return {
    confirmPayment: async () => {
      throw new Error('Payment not available on web platform');
    },
  };
};
