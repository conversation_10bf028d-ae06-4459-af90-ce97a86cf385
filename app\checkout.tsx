import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import { useAuthStore } from '../store/authStore';
import { useCartStore } from '../store/cartStore';
import { Address, PaymentMethod } from '../types';
import { BORDER_RADIUS, COLORS, SPACING, TYPOGRAPHY } from '../utils/constants';

// Mock data
const mockAddresses: Address[] = [
  {
    id: 'addr1',
    userId: 'user1',
    type: 'home',
    label: 'Home',
    street: '123 Main St',
    city: 'San Francisco',
    state: 'CA',
    zipCode: '94102',
    country: 'US',
    latitude: 37.7749,
    longitude: -122.4194,
    isDefault: true,
    instructions: 'Ring doorbell twice',
  },
  {
    id: 'addr2',
    userId: 'user1',
    type: 'work',
    label: 'Office',
    street: '456 Business Ave',
    city: 'San Francisco',
    state: 'CA',
    zipCode: '94105',
    country: 'US',
    latitude: 37.7849,
    longitude: -122.4094,
    isDefault: false,
    instructions: 'Leave at reception',
  },
];

const mockPaymentMethods: PaymentMethod[] = [
  {
    id: 'pm1',
    type: 'card',
    last4: '4242',
    brand: 'visa',
    expiryMonth: 12,
    expiryYear: 2025,
    isDefault: true,
  },
  {
    id: 'pm2',
    type: 'card',
    last4: '5555',
    brand: 'mastercard',
    expiryMonth: 8,
    expiryYear: 2026,
    isDefault: false,
  },
];

export default function CheckoutScreen() {
  const { items, restaurant, subtotal, deliveryFee, tax, total, setTip, tip, clearCart } = useCartStore();
  const { user } = useAuthStore();
  const [selectedAddress, setSelectedAddress] = useState<Address>(mockAddresses[0]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>(mockPaymentMethods[0]);
  const [deliveryInstructions, setDeliveryInstructions] = useState('');
  const [customTip, setCustomTip] = useState('');
  const [isPlacingOrder, setIsPlacingOrder] = useState(false);

  const tipOptions = [
    { label: '15%', value: subtotal * 0.15 },
    { label: '18%', value: subtotal * 0.18 },
    { label: '20%', value: subtotal * 0.20 },
    { label: 'Custom', value: 'custom' },
  ];

  const handleTipSelection = (tipValue: number | string) => {
    if (tipValue === 'custom') {
      // Handle custom tip input
      return;
    }
    setTip(tipValue as number);
    setCustomTip('');
  };

  const handleCustomTipChange = (value: string) => {
    setCustomTip(value);
    const numericValue = parseFloat(value) || 0;
    setTip(numericValue);
  };

  const handlePlaceOrder = async () => {
    if (items.length === 0) {
      Alert.alert('Empty Cart', 'Please add items to your cart before placing an order.');
      return;
    }

    if (!selectedAddress) {
      Alert.alert('Delivery Address', 'Please select a delivery address.');
      return;
    }

    if (!selectedPaymentMethod) {
      Alert.alert('Payment Method', 'Please select a payment method.');
      return;
    }

    setIsPlacingOrder(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Create order object
      const orderData = {
        restaurantId: restaurant?.id,
        items: items.map(item => ({
          menuItemId: item.menuItem.id,
          quantity: item.quantity,
          customizations: item.customizations,
          addOns: item.addOns,
          specialInstructions: item.specialInstructions,
        })),
        deliveryAddress: selectedAddress,
        paymentMethod: selectedPaymentMethod,
        subtotal,
        deliveryFee,
        tax,
        tip,
        total,
        specialInstructions: deliveryInstructions,
      };

      console.log('Order placed:', orderData);

      // Clear cart and navigate to order confirmation
      clearCart();

      Alert.alert(
        'Order Placed!',
        'Your order has been placed successfully. You will receive a confirmation shortly.',
        [
          {
            text: 'Track Order',
            onPress: () => router.replace('/order-tracking/mock-order-id'),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Order Failed', 'There was an error placing your order. Please try again.');
    } finally {
      setIsPlacingOrder(false);
    }
  };

  const renderAddressCard = (address: Address) => (
    <TouchableOpacity
      key={address.id}
      onPress={() => setSelectedAddress(address)}
    >
      <Card style={[
        styles.addressCard,
        selectedAddress.id === address.id && styles.selectedCard,
      ]}>
        <View style={styles.addressHeader}>
          <View style={styles.addressInfo}>
            <Text style={styles.addressLabel}>{address.label}</Text>
            <Text style={styles.addressText}>
              {address.street}, {address.city}, {address.state} {address.zipCode}
            </Text>
            {address.instructions && (
              <Text style={styles.addressInstructions}>{address.instructions}</Text>
            )}
          </View>
          <View style={[
            styles.radioButton,
            selectedAddress.id === address.id && styles.radioButtonSelected,
          ]} />
        </View>
      </Card>
    </TouchableOpacity>
  );

  const renderPaymentMethodCard = (paymentMethod: PaymentMethod) => (
    <TouchableOpacity
      key={paymentMethod.id}
      onPress={() => setSelectedPaymentMethod(paymentMethod)}
    >
      <Card style={[
        styles.paymentCard,
        selectedPaymentMethod.id === paymentMethod.id && styles.selectedCard,
      ]}>
        <View style={styles.paymentHeader}>
          <View style={styles.paymentInfo}>
            <View style={styles.cardInfo}>
              <Ionicons
                name="card"
                size={24}
                color={COLORS.primary}
                style={styles.cardIcon}
              />
              <View>
                <Text style={styles.cardBrand}>
                  {paymentMethod.brand?.toUpperCase()} •••• {paymentMethod.last4}
                </Text>
                {paymentMethod.expiryMonth && paymentMethod.expiryYear && (
                  <Text style={styles.cardExpiry}>
                    Expires {paymentMethod.expiryMonth}/{paymentMethod.expiryYear}
                  </Text>
                )}
              </View>
            </View>
          </View>
          <View style={[
            styles.radioButton,
            selectedPaymentMethod.id === paymentMethod.id && styles.radioButtonSelected,
          ]} />
        </View>
      </Card>
    </TouchableOpacity>
  );

  if (items.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.emptyContainer}>
          <Ionicons name="bag-outline" size={64} color={COLORS.gray[400]} />
          <Text style={styles.emptyTitle}>Your cart is empty</Text>
          <Text style={styles.emptySubtitle}>Add some items to proceed with checkout</Text>
          <Button
            title="Browse Restaurants"
            onPress={() => router.back()}
            style={styles.browseButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Order Summary */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Order Summary</Text>
          <View style={styles.restaurantInfo}>
            <Text style={styles.restaurantName}>{restaurant?.name}</Text>
            <Text style={styles.itemCount}>{items.length} items</Text>
          </View>
        </Card>

        {/* Delivery Address */}
        <Card style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Delivery Address</Text>
            <TouchableOpacity>
              <Text style={styles.addButton}>Add New</Text>
            </TouchableOpacity>
          </View>
          {mockAddresses.map(renderAddressCard)}
        </Card>

        {/* Payment Method */}
        <Card style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Payment Method</Text>
            <TouchableOpacity>
              <Text style={styles.addButton}>Add New</Text>
            </TouchableOpacity>
          </View>
          {mockPaymentMethods.map(renderPaymentMethodCard)}
        </Card>

        {/* Tip */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Add Tip</Text>
          <View style={styles.tipOptions}>
            {tipOptions.map((option, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.tipOption,
                  (option.value === tip || (option.value === 'custom' && customTip)) && styles.tipOptionSelected,
                ]}
                onPress={() => handleTipSelection(option.value)}
              >
                <Text style={[
                  styles.tipOptionText,
                  (option.value === tip || (option.value === 'custom' && customTip)) && styles.tipOptionTextSelected,
                ]}>
                  {option.label}
                </Text>
                {option.value !== 'custom' && (
                  <Text style={[
                    styles.tipAmount,
                    option.value === tip && styles.tipAmountSelected,
                  ]}>
                    ${(option.value as number).toFixed(2)}
                  </Text>
                )}
              </TouchableOpacity>
            ))}
          </View>
          {customTip !== '' && (
            <TextInput
              style={styles.customTipInput}
              placeholder="Enter custom tip amount"
              value={customTip}
              onChangeText={handleCustomTipChange}
              keyboardType="numeric"
            />
          )}
        </Card>

        {/* Delivery Instructions */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Delivery Instructions</Text>
          <TextInput
            style={styles.instructionsInput}
            placeholder="Add delivery instructions (optional)"
            value={deliveryInstructions}
            onChangeText={setDeliveryInstructions}
            multiline
            numberOfLines={3}
          />
        </Card>

        {/* Order Total */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Order Total</Text>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Subtotal</Text>
            <Text style={styles.totalValue}>${subtotal.toFixed(2)}</Text>
          </View>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Delivery Fee</Text>
            <Text style={styles.totalValue}>${deliveryFee.toFixed(2)}</Text>
          </View>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Tax</Text>
            <Text style={styles.totalValue}>${tax.toFixed(2)}</Text>
          </View>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Tip</Text>
            <Text style={styles.totalValue}>${tip.toFixed(2)}</Text>
          </View>
          <View style={[styles.totalRow, styles.grandTotalRow]}>
            <Text style={styles.grandTotalLabel}>Total</Text>
            <Text style={styles.grandTotalValue}>${total.toFixed(2)}</Text>
          </View>
        </Card>
      </ScrollView>

      {/* Place Order Button */}
      <View style={styles.footer}>
        <Button
          title={isPlacingOrder ? 'Placing Order...' : `Place Order • $${total.toFixed(2)}`}
          onPress={handlePlaceOrder}
          loading={isPlacingOrder}
          style={styles.placeOrderButton}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    margin: SPACING.md,
    marginBottom: SPACING.sm,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  addButton: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.primary,
    fontWeight: '600',
  },
  restaurantInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  restaurantName: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  itemCount: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
  },
  addressCard: {
    marginBottom: SPACING.sm,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  selectedCard: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.primary + '10',
  },
  addressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  addressInfo: {
    flex: 1,
  },
  addressLabel: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  addressText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  addressInstructions: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    fontStyle: 'italic',
  },
  paymentCard: {
    marginBottom: SPACING.sm,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  paymentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  paymentInfo: {
    flex: 1,
  },
  cardInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardIcon: {
    marginRight: SPACING.sm,
  },
  cardBrand: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  cardExpiry: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: COLORS.border,
  },
  radioButtonSelected: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.primary,
  },
  tipOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  tipOption: {
    flex: 1,
    minWidth: '45%',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    borderColor: COLORS.border,
    alignItems: 'center',
  },
  tipOptionSelected: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.primary + '20',
  },
  tipOptionText: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  tipOptionTextSelected: {
    color: COLORS.primary,
  },
  tipAmount: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
  },
  tipAmountSelected: {
    color: COLORS.primary,
  },
  customTipInput: {
    marginTop: SPACING.md,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    borderColor: COLORS.border,
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textPrimary,
  },
  instructionsInput: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    borderColor: COLORS.border,
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textPrimary,
    textAlignVertical: 'top',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  totalLabel: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
  },
  totalValue: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textPrimary,
  },
  grandTotalRow: {
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    paddingTop: SPACING.sm,
    marginTop: SPACING.sm,
  },
  grandTotalLabel: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
  },
  grandTotalValue: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  footer: {
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
  placeOrderButton: {
    marginTop: 0,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
  },
  emptyTitle: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
  },
  emptySubtitle: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },
  browseButton: {
    paddingHorizontal: SPACING.xl,
  },
});
