# FoodWay Customer App

A comprehensive food delivery customer application built with React Native, Expo, and TypeScript. This app provides a complete food ordering experience with advanced features like real-time tracking, payment integration, and user management.

## 🚀 Features

### Core Features
- **User Authentication**: Email/phone registration, login, OTP verification, password reset
- **Restaurant Discovery**: Location-based search, category filtering, ratings, and reviews
- **Menu Browsing**: Interactive menus with customizations and add-ons
- **Shopping Cart**: Advanced cart management with item customization
- **Order Management**: Real-time order tracking, history, and status updates
- **Payment Integration**: Multiple payment methods with Stripe integration
- **User Profile**: Profile management, addresses, payment methods, and preferences

### Advanced Features
- **Real-time Tracking**: Live order tracking with map integration
- **Push Notifications**: Order updates and promotional notifications
- **Offline Support**: Basic offline functionality with data caching
- **Social Features**: Restaurant reviews, ratings, and photo sharing
- **Loyalty Program**: Points system and referral rewards
- **Smart Recommendations**: AI-powered food suggestions

## 🏗️ Architecture

### Technology Stack
- **Frontend**: React Native with Expo SDK 53
- **Navigation**: Expo Router (File-based routing)
- **Language**: TypeScript
- **State Management**: Zustand + React Query
- **Backend**: Railway (PostgreSQL)
- **Payments**: Stripe React Native
- **Maps**: React Native Maps
- **Notifications**: Expo Notifications

### Project Structure
```
foodway-customer-app/
├── app/                    # Expo Router screens
│   ├── (auth)/            # Authentication screens
│   ├── (tabs)/            # Main app tabs
│   ├── restaurant/        # Restaurant screens
│   └── order/             # Order management screens
├── components/            # Reusable UI components
│   ├── ui/               # Basic UI components
│   ├── forms/            # Form components
│   └── cards/            # Card components
├── services/             # API and external services
│   ├── api/              # API client and endpoints
│   ├── auth/             # Authentication service
│   └── notifications/    # Push notifications
├── store/                # State management (Zustand)
├── utils/                # Utility functions and constants
├── hooks/                # Custom React hooks
├── types/                # TypeScript type definitions
├── tests/                # Test files
└── docs/                 # Documentation
```

## 🛠️ Installation & Setup

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn
- Expo CLI
- iOS Simulator (for iOS development)
- Android Studio (for Android development)

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd foodway-customer-app

# Install dependencies
npm install

# Start the development server
npm start
```

### Environment Variables
Create a `.env` file in the root directory:
```env
EXPO_PUBLIC_API_URL=https://your-railway-app.railway.app/api
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_key
```

## 📱 Development

### Available Scripts
```bash
npm start          # Start Expo development server
npm run android    # Run on Android
npm run ios        # Run on iOS
npm run web        # Run on web
npm test           # Run tests
npm run lint       # Run ESLint
npm run build      # Build for production
```

### Development Workflow
1. **Start Development Server**: `npm start`
2. **Choose Platform**: Press `a` for Android, `i` for iOS, or `w` for web
3. **Live Reload**: Changes are automatically reflected in the app
4. **Debugging**: Use Expo DevTools for debugging

## 🧪 Testing

### Testing Strategy
- **Unit Tests**: Components, utilities, and hooks
- **Integration Tests**: API services and data flow
- **E2E Tests**: Complete user workflows
- **Performance Tests**: Large datasets and rapid interactions
- **Accessibility Tests**: Screen reader compatibility

### Running Tests
```bash
npm test                    # Run all tests
npm test -- --watch        # Run tests in watch mode
npm test -- --coverage     # Run tests with coverage
npm run test:e2e           # Run E2E tests
```

### Test Structure
```
tests/
├── components/            # Component tests
├── services/             # Service tests
├── utils/                # Utility tests
├── integration/          # Integration tests
├── e2e/                  # End-to-end tests
└── __mocks__/            # Mock files
```

## 🔧 Configuration

### App Configuration
The app configuration is managed through:
- `app.json`: Expo configuration
- `utils/constants.ts`: App constants and settings
- Environment variables for sensitive data

### Key Configuration Files
- **app.json**: Expo app configuration
- **tsconfig.json**: TypeScript configuration
- **package.json**: Dependencies and scripts
- **metro.config.js**: Metro bundler configuration

## 🎨 UI/UX Design

### Design System
- **Colors**: Consistent color palette with primary, secondary, and neutral colors
- **Typography**: Standardized font sizes and weights
- **Spacing**: Consistent spacing system
- **Components**: Reusable UI components with variants

### Theme Support
- Light and dark mode support
- Automatic theme switching based on system preferences
- Customizable theme colors and settings

## 🔐 Security

### Security Measures
- **JWT Authentication**: Secure token-based authentication
- **Secure Storage**: Sensitive data stored securely
- **API Security**: Request encryption and validation
- **Input Validation**: Client and server-side validation
- **Biometric Authentication**: Optional biometric login

### Best Practices
- Never store sensitive data in plain text
- Use HTTPS for all API communications
- Implement proper error handling
- Regular security audits and updates

## 🚀 Deployment

### Build Process
```bash
# Install EAS CLI
npm install -g @expo/eas-cli

# Configure EAS
eas build:configure

# Build for iOS
eas build --platform ios

# Build for Android
eas build --platform android

# Submit to app stores
eas submit --platform ios
eas submit --platform android
```

### Deployment Checklist
- [ ] Update app version in `app.json`
- [ ] Test on physical devices
- [ ] Update environment variables
- [ ] Generate production builds
- [ ] Test production builds
- [ ] Submit to app stores

## 📊 Performance

### Optimization Strategies
- **Image Optimization**: Lazy loading and caching
- **List Virtualization**: Efficient rendering of large lists
- **Code Splitting**: Lazy loading of screens and components
- **Caching**: API response caching with React Query
- **Bundle Optimization**: Tree shaking and minification

### Performance Monitoring
- Bundle size analysis
- Runtime performance monitoring
- Memory usage tracking
- Network request optimization

## 🤝 Contributing

### Development Guidelines
1. Follow TypeScript best practices
2. Write comprehensive tests
3. Use consistent code formatting
4. Document new features and APIs
5. Follow the established project structure

### Code Style
- Use ESLint and Prettier for code formatting
- Follow React Native best practices
- Use meaningful variable and function names
- Write clear and concise comments

## 📚 API Documentation

### Authentication Endpoints
- `POST /auth/login` - User login
- `POST /auth/register` - User registration
- `POST /auth/refresh` - Token refresh
- `POST /auth/logout` - User logout

### Restaurant Endpoints
- `GET /restaurants` - Get all restaurants
- `GET /restaurants/:id` - Get restaurant details
- `GET /restaurants/:id/menu` - Get restaurant menu
- `GET /restaurants/search` - Search restaurants

### Order Endpoints
- `POST /orders` - Create new order
- `GET /orders` - Get user orders
- `GET /orders/:id` - Get order details
- `GET /orders/:id/tracking` - Get order tracking

## 🐛 Troubleshooting

### Common Issues
1. **Metro bundler issues**: Clear cache with `npx expo start --clear`
2. **iOS build issues**: Clean build folder and rebuild
3. **Android build issues**: Check Android SDK configuration
4. **Network issues**: Verify API endpoints and network connectivity

### Debug Tools
- Expo DevTools
- React Native Debugger
- Flipper (for advanced debugging)
- Chrome DevTools (for web)

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions:
- Email: <EMAIL>
- Documentation: [Link to docs]
- Issues: [GitHub Issues]

---

**FoodWay Customer App v1.0.0**
Built with ❤️ using React Native and Expo
