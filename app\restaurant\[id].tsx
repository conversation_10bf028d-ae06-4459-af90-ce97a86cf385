import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useLocalSearchParams } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
    Alert,
    Animated,
    FlatList,
    Image,
    Modal,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Button from '../../components/ui/Button';
import { useCartStore } from '../../store/cartStore';
import { Category, MenuItem, Restaurant, SelectedAddOn, SelectedCustomization } from '../../types';
import { BORDER_RADIUS, COLORS, SPACING, TYPOGRAPHY } from '../../utils/constants';

// Mock restaurant data
const mockRestaurant: Restaurant = {
  id: '1',
  name: '<PERSON>\'s Pizza Palace',
  description: 'Authentic Italian pizza made with fresh ingredients and traditional recipes passed down through generations.',
  image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop',
  coverImage: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400&h=200&fit=crop',
  cuisine: ['Italian', 'Pizza'],
  rating: 4.5,
  reviewCount: 324,
  deliveryTime: '25-35 min',
  deliveryFee: 2.99,
  minimumOrder: 15,
  isOpen: true,
  address: '123 Main St, Downtown',
  latitude: 37.7749,
  longitude: -122.4194,
  phone: '******-0123',
  categories: [
    { id: 'cat1', name: 'Pizza', restaurantId: '1' },
    { id: 'cat2', name: 'Appetizers', restaurantId: '1' },
    { id: 'cat3', name: 'Salads', restaurantId: '1' },
    { id: 'cat4', name: 'Desserts', restaurantId: '1' },
  ],
  featured: true,
  promoted: false,
  tags: ['Popular', 'Fast Delivery'],
};

const mockMenuItems: MenuItem[] = [
  {
    id: 'item1',
    restaurantId: '1',
    categoryId: 'cat1',
    name: 'Margherita Pizza',
    description: 'Classic pizza with fresh mozzarella, tomato sauce, and basil',
    image: 'https://images.unsplash.com/photo-1604382354936-07c5d9983bd3?w=300&h=200&fit=crop',
    price: 16.99,
    isAvailable: true,
    isVegetarian: true,
    isVegan: false,
    isGlutenFree: false,
    isSpicy: false,
    calories: 280,
    preparationTime: '15-20 min',
    customizations: [
      {
        id: 'size',
        name: 'Size',
        type: 'single',
        required: true,
        options: [
          { id: 'small', name: 'Small (10")', price: 0, isDefault: true },
          { id: 'medium', name: 'Medium (12")', price: 3 },
          { id: 'large', name: 'Large (14")', price: 6 },
        ],
      },
      {
        id: 'crust',
        name: 'Crust Type',
        type: 'single',
        required: true,
        options: [
          { id: 'thin', name: 'Thin Crust', price: 0, isDefault: true },
          { id: 'thick', name: 'Thick Crust', price: 2 },
          { id: 'stuffed', name: 'Stuffed Crust', price: 4 },
        ],
      },
    ],
    addOns: [
      { id: 'extra-cheese', name: 'Extra Cheese', price: 2.50 },
      { id: 'pepperoni', name: 'Pepperoni', price: 3.00 },
      { id: 'mushrooms', name: 'Mushrooms', price: 1.50 },
    ],
    tags: ['Popular', 'Vegetarian'],
  },
  {
    id: 'item2',
    restaurantId: '1',
    categoryId: 'cat2',
    name: 'Garlic Bread',
    description: 'Crispy bread with garlic butter and herbs',
    image: 'https://images.unsplash.com/photo-1573140247632-f8fd74997d5c?w=300&h=200&fit=crop',
    price: 6.99,
    isAvailable: true,
    isVegetarian: true,
    isVegan: false,
    isGlutenFree: false,
    isSpicy: false,
    calories: 180,
    preparationTime: '8-10 min',
    customizations: [],
    addOns: [
      { id: 'cheese-dip', name: 'Cheese Dip', price: 1.50 },
      { id: 'marinara', name: 'Marinara Sauce', price: 1.00 },
    ],
    tags: ['Appetizer'],
  },
];

export default function RestaurantScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [restaurant] = useState<Restaurant>(mockRestaurant);
  const [menuItems] = useState<MenuItem[]>(mockMenuItems);
  const [selectedCategory, setSelectedCategory] = useState<string>('cat1');
  const [selectedItem, setSelectedItem] = useState<MenuItem | null>(null);
  const [showItemModal, setShowItemModal] = useState(false);
  const [customizations, setCustomizations] = useState<SelectedCustomization[]>([]);
  const [addOns, setAddOns] = useState<SelectedAddOn[]>([]);
  const [quantity, setQuantity] = useState(1);
  const [specialInstructions, setSpecialInstructions] = useState('');

  const { addItem, setRestaurant } = useCartStore();

  // Modern animations
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const headerOpacity = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  useEffect(() => {
    setRestaurant(restaurant);
  }, [restaurant, setRestaurant]);

  const filteredMenuItems = menuItems.filter(item => item.categoryId === selectedCategory);

  const handleItemPress = (item: MenuItem) => {
    setSelectedItem(item);
    setCustomizations([]);
    setAddOns([]);
    setQuantity(1);
    setSpecialInstructions('');
    setShowItemModal(true);
  };

  const handleAddToCart = () => {
    if (!selectedItem) return;

    // Validate required customizations
    const requiredCustomizations = selectedItem.customizations.filter(c => c.required);
    const selectedCustomizationIds = customizations.map(c => c.customizationId);
    
    for (const required of requiredCustomizations) {
      if (!selectedCustomizationIds.includes(required.id)) {
        Alert.alert('Required Selection', `Please select ${required.name}`);
        return;
      }
    }

    for (let i = 0; i < quantity; i++) {
      addItem(selectedItem, customizations, addOns, specialInstructions);
    }

    setShowItemModal(false);
    Alert.alert('Added to Cart', `${selectedItem.name} has been added to your cart`);
  };

  const renderCategoryTab = (category: Category) => (
    <TouchableOpacity
      key={category.id}
      style={[
        modernStyles.categoryTab,
        selectedCategory === category.id && modernStyles.categoryTabActive,
      ]}
      onPress={() => setSelectedCategory(category.id)}
      activeOpacity={0.8}
    >
      <Text
        style={[
          modernStyles.categoryTabText,
          selectedCategory === category.id && modernStyles.categoryTabTextActive,
        ]}
      >
        {category.name}
      </Text>
    </TouchableOpacity>
  );

  const renderMenuItem = ({ item, index }: { item: MenuItem; index: number }) => {
    const itemAnimatedStyle = {
      opacity: fadeAnim,
      transform: [
        {
          translateY: slideAnim.interpolate({
            inputRange: [0, 30],
            outputRange: [0, 30 + index * 5],
          }),
        },
      ],
    };

    return (
      <Animated.View style={itemAnimatedStyle}>
        <TouchableOpacity
          onPress={() => handleItemPress(item)}
          activeOpacity={0.95}
        >
          <View style={modernStyles.menuItem}>
            <Image source={{ uri: item.image }} style={modernStyles.menuItemImage} />
            <View style={modernStyles.menuItemContent}>
              <Text style={modernStyles.menuItemName}>{item.name}</Text>
              <Text style={modernStyles.menuItemDescription} numberOfLines={2}>
                {item.description}
              </Text>

              <View style={modernStyles.menuItemTags}>
                {item.isVegetarian && (
                  <View style={modernStyles.vegetarianTag}>
                    <Ionicons name="leaf-outline" size={12} color="#4CAF50" />
                    <Text style={modernStyles.tagText}>Veg</Text>
                  </View>
                )}
                {item.isSpicy && (
                  <View style={modernStyles.spicyTag}>
                    <Ionicons name="flame-outline" size={12} color="#FF5722" />
                    <Text style={modernStyles.tagText}>Spicy</Text>
                  </View>
                )}
                {item.preparationTime && (
                  <View style={modernStyles.timeTag}>
                    <Ionicons name="time-outline" size={12} color={COLORS.textSecondary} />
                    <Text style={modernStyles.tagText}>{item.preparationTime}</Text>
                  </View>
                )}
              </View>

              <View style={modernStyles.menuItemFooter}>
                <Text style={modernStyles.menuItemPrice}>${item.price.toFixed(2)}</Text>
                <TouchableOpacity style={modernStyles.addButton}>
                  <Ionicons name="add" size={18} color={COLORS.white} />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  return (
    <SafeAreaView style={modernStyles.container}>
      <ScrollView
        style={modernStyles.scrollView}
        showsVerticalScrollIndicator={false}
        scrollEventThrottle={16}
      >
        {/* Modern Restaurant Header */}
        <View style={modernStyles.header}>
          <View style={modernStyles.coverImageContainer}>
            <Image source={{ uri: restaurant.coverImage }} style={modernStyles.coverImage} />
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.7)']}
              style={modernStyles.coverGradient}
            />
            <View style={modernStyles.headerOverlay}>
              <TouchableOpacity style={modernStyles.backButton}>
                <Ionicons name="arrow-back" size={24} color={COLORS.white} />
              </TouchableOpacity>
              <TouchableOpacity style={modernStyles.favoriteButton}>
                <Ionicons name="heart-outline" size={24} color={COLORS.white} />
              </TouchableOpacity>
            </View>
          </View>

          <Animated.View style={[modernStyles.restaurantInfo, { opacity: fadeAnim }]}>
            <View style={modernStyles.restaurantHeader}>
              <Text style={modernStyles.restaurantName}>{restaurant.name}</Text>
              <View style={modernStyles.ratingBadge}>
                <Ionicons name="star" size={14} color="#FFB400" />
                <Text style={modernStyles.ratingText}>{restaurant.rating}</Text>
              </View>
            </View>

            <Text style={modernStyles.restaurantDescription}>{restaurant.description}</Text>

            <View style={modernStyles.restaurantMeta}>
              <View style={modernStyles.metaItem}>
                <Ionicons name="time-outline" size={16} color={COLORS.primary} />
                <Text style={modernStyles.metaText}>{restaurant.deliveryTime}</Text>
              </View>
              <View style={modernStyles.metaItem}>
                <Ionicons name="bicycle-outline" size={16} color={COLORS.primary} />
                <Text style={modernStyles.metaText}>${restaurant.deliveryFee.toFixed(2)}</Text>
              </View>
              <View style={modernStyles.metaItem}>
                <Ionicons name="wallet-outline" size={16} color={COLORS.primary} />
                <Text style={modernStyles.metaText}>Min. ${restaurant.minimumOrder}</Text>
              </View>
            </View>

            <View style={modernStyles.cuisineTags}>
              {restaurant.cuisine.map((cuisine, index) => (
                <View key={index} style={modernStyles.cuisineTag}>
                  <Text style={modernStyles.cuisineTagText}>{cuisine}</Text>
                </View>
              ))}
            </View>
          </Animated.View>
        </View>

        {/* Modern Category Tabs */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={modernStyles.categoryTabs}
          contentContainerStyle={modernStyles.categoryTabsContent}
        >
          {restaurant.categories.map(renderCategoryTab)}
        </ScrollView>

        {/* Modern Menu Items */}
        <Animated.View style={[modernStyles.menuSection, { opacity: fadeAnim }]}>
          <FlatList
            data={filteredMenuItems}
            renderItem={renderMenuItem}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            contentContainerStyle={modernStyles.menuList}
            scrollEventThrottle={16}
          />
        </Animated.View>
      </ScrollView>

      {/* Item Customization Modal */}
      <Modal
        visible={showItemModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowItemModal(false)}>
              <Ionicons name="close" size={24} color={COLORS.textPrimary} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Customize Item</Text>
            <View style={{ width: 24 }} />
          </View>

          {selectedItem && (
            <ScrollView style={styles.modalContent}>
              <Image source={{ uri: selectedItem.image }} style={styles.modalItemImage} />
              <Text style={styles.modalItemName}>{selectedItem.name}</Text>
              <Text style={styles.modalItemDescription}>{selectedItem.description}</Text>
              <Text style={styles.modalItemPrice}>${selectedItem.price.toFixed(2)}</Text>

              {/* Customizations */}
              {selectedItem.customizations.map((customization) => (
                <View key={customization.id} style={styles.customizationSection}>
                  <Text style={styles.customizationTitle}>
                    {customization.name} {customization.required && '*'}
                  </Text>
                  {customization.options.map((option) => (
                    <TouchableOpacity
                      key={option.id}
                      style={styles.customizationOption}
                      onPress={() => {
                        if (customization.type === 'single') {
                          setCustomizations(prev => [
                            ...prev.filter(c => c.customizationId !== customization.id),
                            { customizationId: customization.id, optionIds: [option.id] }
                          ]);
                        }
                      }}
                    >
                      <View style={styles.optionLeft}>
                        <View style={[
                          styles.radioButton,
                          customizations.some(c => 
                            c.customizationId === customization.id && 
                            c.optionIds.includes(option.id)
                          ) && styles.radioButtonSelected
                        ]} />
                        <Text style={styles.optionName}>{option.name}</Text>
                      </View>
                      {option.price > 0 && (
                        <Text style={styles.optionPrice}>+${option.price.toFixed(2)}</Text>
                      )}
                    </TouchableOpacity>
                  ))}
                </View>
              ))}

              {/* Add-ons */}
              {selectedItem.addOns.length > 0 && (
                <View style={styles.addOnsSection}>
                  <Text style={styles.addOnsTitle}>Add-ons</Text>
                  {selectedItem.addOns.map((addOn) => (
                    <TouchableOpacity
                      key={addOn.id}
                      style={styles.addOnOption}
                      onPress={() => {
                        const existing = addOns.find(a => a.addOnId === addOn.id);
                        if (existing) {
                          setAddOns(prev => prev.filter(a => a.addOnId !== addOn.id));
                        } else {
                          setAddOns(prev => [...prev, { addOnId: addOn.id, quantity: 1 }]);
                        }
                      }}
                    >
                      <View style={styles.optionLeft}>
                        <View style={[
                          styles.checkbox,
                          addOns.some(a => a.addOnId === addOn.id) && styles.checkboxSelected
                        ]}>
                          {addOns.some(a => a.addOnId === addOn.id) && (
                            <Ionicons name="checkmark" size={16} color={COLORS.white} />
                          )}
                        </View>
                        <Text style={styles.optionName}>{addOn.name}</Text>
                      </View>
                      <Text style={styles.optionPrice}>+${addOn.price.toFixed(2)}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              )}

              {/* Quantity */}
              <View style={styles.quantitySection}>
                <Text style={styles.quantityTitle}>Quantity</Text>
                <View style={styles.quantityControls}>
                  <TouchableOpacity
                    style={styles.quantityButton}
                    onPress={() => setQuantity(Math.max(1, quantity - 1))}
                  >
                    <Ionicons name="remove" size={20} color={COLORS.primary} />
                  </TouchableOpacity>
                  <Text style={styles.quantityText}>{quantity}</Text>
                  <TouchableOpacity
                    style={styles.quantityButton}
                    onPress={() => setQuantity(quantity + 1)}
                  >
                    <Ionicons name="add" size={20} color={COLORS.primary} />
                  </TouchableOpacity>
                </View>
              </View>
            </ScrollView>
          )}

          <View style={styles.modalFooter}>
            <Button
              title="Add to Cart"
              onPress={handleAddToCart}
              style={styles.addToCartButton}
            />
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

// Modern styles for restaurant screen
const modernStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: COLORS.white,
    marginBottom: SPACING.sm,
  },
  coverImageContainer: {
    position: 'relative',
    height: 250,
  },
  coverImage: {
    width: '100%',
    height: '100%',
  },
  coverGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 100,
  },
  headerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: SPACING.md,
    paddingTop: SPACING.lg,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  favoriteButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  restaurantInfo: {
    padding: SPACING.md,
  },
  restaurantHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.sm,
  },
  restaurantName: {
    fontSize: TYPOGRAPHY.fontSize['2xl'],
    fontWeight: '700',
    color: COLORS.textPrimary,
    flex: 1,
    marginRight: SPACING.sm,
  },
  ratingBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF8E1',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.md,
  },
  ratingText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '700',
    color: '#F57C00',
    marginLeft: 4,
  },
  restaurantDescription: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
    lineHeight: 22,
    marginBottom: SPACING.md,
  },
  restaurantMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.md,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  metaText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    fontWeight: '600',
    marginLeft: 4,
  },
  cuisineTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  cuisineTag: {
    backgroundColor: 'rgba(255, 107, 53, 0.1)',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.md,
    marginRight: SPACING.xs,
    marginBottom: SPACING.xs,
  },
  cuisineTagText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.primary,
    fontWeight: '600',
  },
  categoryTabs: {
    backgroundColor: COLORS.white,
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  categoryTabsContent: {
    paddingHorizontal: SPACING.md,
  },
  categoryTab: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    marginRight: SPACING.sm,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: '#F8F9FA',
  },
  categoryTabActive: {
    backgroundColor: COLORS.primary,
  },
  categoryTabText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '600',
    color: COLORS.textSecondary,
  },
  categoryTabTextActive: {
    color: COLORS.white,
  },
  menuSection: {
    backgroundColor: COLORS.white,
    paddingTop: SPACING.md,
  },
  menuItem: {
    flexDirection: 'row',
    padding: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: '#F8F9FA',
  },
  menuItemImage: {
    width: 80,
    height: 80,
    borderRadius: BORDER_RADIUS.md,
    marginRight: SPACING.md,
  },
  menuItemContent: {
    flex: 1,
  },
  menuItemName: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '700',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  menuItemDescription: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
    lineHeight: 18,
  },
  menuItemPrice: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '700',
    color: COLORS.primary,
  },
  menuItemTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: SPACING.sm,
  },
  vegetarianTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8F5E8',
    paddingHorizontal: SPACING.xs,
    paddingVertical: 2,
    borderRadius: BORDER_RADIUS.sm,
    marginRight: SPACING.xs,
    marginBottom: 4,
  },
  spicyTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFEBEE',
    paddingHorizontal: SPACING.xs,
    paddingVertical: 2,
    borderRadius: BORDER_RADIUS.sm,
    marginRight: SPACING.xs,
    marginBottom: 4,
  },
  timeTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    paddingHorizontal: SPACING.xs,
    paddingVertical: 2,
    borderRadius: BORDER_RADIUS.sm,
    marginRight: SPACING.xs,
    marginBottom: 4,
  },
  tagText: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    fontWeight: '600',
    marginLeft: 2,
  },
  menuItemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  addButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.primary,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: COLORS.white,
  },
  coverImage: {
    width: '100%',
    height: 200,
  },
  restaurantInfo: {
    padding: SPACING.md,
  },
  restaurantName: {
    fontSize: TYPOGRAPHY.fontSize['2xl'],
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  restaurantDescription: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
    lineHeight: 22,
    marginBottom: SPACING.md,
  },
  restaurantMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginLeft: SPACING.xs,
  },
  reviewCount: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
  },
  deliveryTime: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
  },
  deliveryInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  deliveryFee: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
  },
  minimumOrder: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
  },
  categoryTabs: {
    backgroundColor: COLORS.white,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  categoryTabsContent: {
    paddingHorizontal: SPACING.md,
  },
  categoryTab: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    marginRight: SPACING.sm,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  categoryTabActive: {
    borderBottomColor: COLORS.primary,
  },
  categoryTabText: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '500',
    color: COLORS.textSecondary,
  },
  categoryTabTextActive: {
    color: COLORS.primary,
    fontWeight: 'bold',
  },
  menuSection: {
    backgroundColor: COLORS.white,
    marginTop: SPACING.sm,
  },
  menuList: {
    padding: SPACING.md,
  },
  menuItemCard: {
    marginBottom: SPACING.md,
    padding: 0,
    overflow: 'hidden',
  },
  menuItemContent: {
    flexDirection: 'row',
  },
  menuItemImage: {
    width: 100,
    height: 100,
    borderRadius: BORDER_RADIUS.md,
    marginRight: SPACING.md,
  },
  menuItemDetails: {
    flex: 1,
    padding: SPACING.md,
  },
  menuItemName: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  menuItemDescription: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    lineHeight: 20,
    marginBottom: SPACING.sm,
  },
  menuItemMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  menuItemPrice: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  preparationTime: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
  },
  menuItemTags: {
    flexDirection: 'row',
  },
  tag: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.sm,
    marginRight: SPACING.xs,
  },
  vegetarianTag: {
    backgroundColor: COLORS.success + '20',
  },
  spicyTag: {
    backgroundColor: COLORS.error + '20',
  },
  tagText: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    fontWeight: '500',
    color: COLORS.textPrimary,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    backgroundColor: COLORS.white,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  modalTitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
  },
  modalContent: {
    flex: 1,
    backgroundColor: COLORS.white,
    padding: SPACING.md,
  },
  modalItemImage: {
    width: '100%',
    height: 200,
    borderRadius: BORDER_RADIUS.lg,
    marginBottom: SPACING.md,
  },
  modalItemName: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  modalItemDescription: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
    lineHeight: 22,
    marginBottom: SPACING.sm,
  },
  modalItemPrice: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: SPACING.lg,
  },
  customizationSection: {
    marginBottom: SPACING.lg,
  },
  customizationTitle: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
  },
  customizationOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: COLORS.border,
    marginRight: SPACING.sm,
  },
  radioButtonSelected: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.primary,
  },
  optionName: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textPrimary,
    flex: 1,
  },
  optionPrice: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
    color: COLORS.primary,
  },
  addOnsSection: {
    marginBottom: SPACING.lg,
  },
  addOnsTitle: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
  },
  addOnOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: BORDER_RADIUS.sm,
    borderWidth: 2,
    borderColor: COLORS.border,
    marginRight: SPACING.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxSelected: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.primary,
  },
  quantitySection: {
    marginBottom: SPACING.lg,
  },
  quantityTitle: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityButton: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: COLORS.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityText: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginHorizontal: SPACING.lg,
    minWidth: 40,
    textAlign: 'center',
  },
  modalFooter: {
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
  addToCartButton: {
    marginTop: 0,
  },
});
