# FoodWay Customer App - Project Structure Documentation

## Overview
This is an Expo React Native application using Expo Router for file-based navigation, TypeScript for type safety, and modern React Native development practices.

## Project Information
- **Project Name**: customer_app
- **Framework**: Expo SDK ~53.0.13
- **Navigation**: Expo Router (File-based routing)
- **Language**: TypeScript
- **Platform Support**: iOS, Android, Web

## Root Directory Structure

```
customer_app/
├── 📁 app/                          # Main application screens (Expo Router)
├── 📁 assets/                       # Static assets (images, fonts)
├── 📁 components/                   # Reusable React components
├── 📁 constants/                    # App constants and configuration
├── 📁 hooks/                        # Custom React hooks
├── 📁 node_modules/                 # Dependencies (auto-generated)
├── 📁 scripts/                      # Build and utility scripts
├── 📄 app.json                      # Expo configuration
├── 📄 eslint.config.js              # ESLint configuration
├── 📄 expo-env.d.ts                 # Expo TypeScript definitions
├── 📄 package.json                  # Project dependencies and scripts
├── 📄 package-lock.json             # Dependency lock file
├── 📄 README.md                     # Project documentation
└── 📄 tsconfig.json                 # TypeScript configuration
```

## Detailed Directory Structure

### 📁 app/ - Application Screens (Expo Router)
```
app/
├── 📁 (tabs)/                       # Tab navigation group
│   ├── 📄 _layout.tsx               # Tab navigation layout
│   ├── 📄 index.tsx                 # Home screen (default route)
│   └── 📄 explore.tsx               # Explore screen
├── 📄 _layout.tsx                   # Root layout (main entry point)
└── 📄 +not-found.tsx                # 404 error screen
```

**Key Files:**
- `_layout.tsx` - Root layout with theme provider and navigation setup
- `(tabs)/_layout.tsx` - Tab navigation configuration
- `(tabs)/index.tsx` - Home screen (first screen users see)
- `(tabs)/explore.tsx` - Secondary tab screen
- `+not-found.tsx` - Error handling for invalid routes

### 📁 assets/ - Static Assets
```
assets/
├── 📁 fonts/
│   └── 📄 SpaceMono-Regular.ttf     # Custom font
└── 📁 images/
    ├── 📄 adaptive-icon.png         # Android adaptive icon
    ├── 📄 favicon.png               # Web favicon
    ├── 📄 icon.png                  # App icon
    ├── 📄 partial-react-logo.png    # Header image
    ├── 📄 react-logo.png            # React logo (1x)
    ├── 📄 <EMAIL>         # React logo (2x)
    ├── 📄 <EMAIL>         # React logo (3x)
    └── 📄 splash-icon.png           # Splash screen icon
```

### 📁 components/ - Reusable Components
```
components/
├── 📁 ui/                           # Platform-specific UI components
│   ├── 📄 IconSymbol.ios.tsx        # iOS-specific icon component
│   ├── 📄 IconSymbol.tsx            # Default icon component
│   ├── 📄 TabBarBackground.ios.tsx  # iOS tab bar background
│   └── 📄 TabBarBackground.tsx      # Default tab bar background
├── 📄 Collapsible.tsx               # Collapsible content component
├── 📄 ExternalLink.tsx              # External link component
├── 📄 HapticTab.tsx                 # Tab with haptic feedback
├── 📄 HelloWave.tsx                 # Animated wave component
├── 📄 ParallaxScrollView.tsx        # Parallax scroll view
├── 📄 ThemedText.tsx                # Theme-aware text component
└── 📄 ThemedView.tsx                # Theme-aware view component
```

### 📁 constants/ - App Constants
```
constants/
└── 📄 Colors.ts                     # Color scheme definitions
```

### 📁 hooks/ - Custom React Hooks
```
hooks/
├── 📄 useColorScheme.ts             # Color scheme hook (default)
├── 📄 useColorScheme.web.ts         # Web-specific color scheme hook
└── 📄 useThemeColor.ts              # Theme color hook
```

### 📁 scripts/ - Utility Scripts
```
scripts/
└── 📄 reset-project.js              # Project reset utility
```

## Configuration Files

### 📄 package.json - Project Configuration
```json
{
  "name": "customer_app",
  "main": "expo-router/entry",
  "version": "1.0.0",
  "scripts": {
    "start": "expo start",
    "reset-project": "node ./scripts/reset-project.js",
    "android": "expo start --android",
    "ios": "expo start --ios",
    "web": "expo start --web",
    "lint": "expo lint"
  }
}
```

**Key Dependencies:**
- **expo**: ~53.0.13 - Expo SDK
- **react**: 19.0.0 - React library
- **react-native**: 0.79.4 - React Native framework
- **expo-router**: ~5.1.1 - File-based routing
- **typescript**: ~5.8.3 - TypeScript support

### 📄 app.json - Expo Configuration
```json
{
  "expo": {
    "name": "customer_app",
    "slug": "customer_app",
    "version": "1.0.0",
    "orientation": "portrait",
    "scheme": "customerapp",
    "newArchEnabled": true,
    "plugins": ["expo-router", "expo-splash-screen"]
  }
}
```

### 📄 tsconfig.json - TypeScript Configuration
```json
{
  "extends": "expo/tsconfig.base",
  "compilerOptions": {
    "strict": true,
    "paths": {
      "@/*": ["./*"]
    }
  }
}
```

## File-Based Routing (Expo Router)

### Route Structure
```
app/
├── _layout.tsx          → Root layout
├── (tabs)/
│   ├── _layout.tsx      → Tab navigation
│   ├── index.tsx        → / (Home tab)
│   └── explore.tsx      → /explore (Explore tab)
└── +not-found.tsx       → 404 page
```

### Navigation Flow
1. **App Entry**: `expo-router/entry` (defined in package.json)
2. **Root Layout**: `app/_layout.tsx` - Sets up theme and navigation stack
3. **Tab Layout**: `app/(tabs)/_layout.tsx` - Configures bottom tabs
4. **Home Screen**: `app/(tabs)/index.tsx` - Default landing page
5. **Explore Screen**: `app/(tabs)/explore.tsx` - Secondary tab

## Key Features

### 🎨 Theming System
- **Light/Dark Mode**: Automatic theme switching
- **Themed Components**: `ThemedText`, `ThemedView`
- **Color Constants**: Centralized in `constants/Colors.ts`
- **Custom Hooks**: `useColorScheme`, `useThemeColor`

### 📱 Platform Support
- **iOS**: Native iOS components and styling
- **Android**: Material Design components
- **Web**: React Native Web compatibility
- **Platform-specific files**: `.ios.tsx`, `.android.tsx`, `.web.tsx`

### 🧩 Component Architecture
- **Reusable Components**: Modular design in `components/`
- **UI Components**: Platform-specific variants in `components/ui/`
- **Custom Hooks**: Shared logic in `hooks/`
- **Type Safety**: Full TypeScript integration

### 🚀 Development Features
- **Hot Reload**: Instant code updates
- **TypeScript**: Full type checking
- **ESLint**: Code quality enforcement
- **Expo Dev Tools**: Built-in debugging tools

## Getting Started

### Development Commands
```bash
# Start development server
npm start

# Start for specific platform
npm run android    # Android
npm run ios        # iOS
npm run web        # Web browser

# Other commands
npm run lint       # Run ESLint
npm run reset-project  # Reset to clean state
```

### Development URLs
- **Metro Bundler**: http://localhost:8081
- **Web Version**: http://localhost:8081
- **QR Code**: Scan with Expo Go app for mobile testing

## Project Architecture

### Entry Point Flow
1. `package.json` → `"main": "expo-router/entry"`
2. Expo Router → `app/_layout.tsx`
3. Root Layout → `app/(tabs)/_layout.tsx`
4. Tab Layout → `app/(tabs)/index.tsx` (Home)

### Component Hierarchy
```
RootLayout (_layout.tsx)
├── ThemeProvider
├── Stack Navigator
│   ├── (tabs) - Tab Group
│   │   ├── Home (index.tsx)
│   │   └── Explore (explore.tsx)
│   └── +not-found.tsx
└── StatusBar
```

### State Management
- **React Hooks**: Built-in state management
- **Context API**: Theme and color scheme
- **Custom Hooks**: Reusable stateful logic

## Best Practices

### File Organization
- **Screens**: Place in `app/` directory
- **Components**: Reusable UI in `components/`
- **Utilities**: Helper functions in appropriate directories
- **Assets**: Images and fonts in `assets/`

### Naming Conventions
- **Components**: PascalCase (e.g., `ThemedText.tsx`)
- **Hooks**: camelCase with 'use' prefix (e.g., `useColorScheme.ts`)
- **Constants**: PascalCase (e.g., `Colors.ts`)
- **Routes**: lowercase for URLs (e.g., `index.tsx`, `explore.tsx`)

### TypeScript Usage
- **Strict Mode**: Enabled for better type safety
- **Path Aliases**: `@/*` for root directory imports
- **Type Definitions**: Custom types in appropriate files
- **Platform Types**: Expo and React Native type definitions

---

*This documentation reflects the current project structure. Update as the project evolves.*
