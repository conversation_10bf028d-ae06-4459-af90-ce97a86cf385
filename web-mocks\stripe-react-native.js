// Complete mock for @stripe/stripe-react-native package for web compatibility
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

// Mock StripeProvider component
export const StripeProvider = ({ children, publishableKey, merchantIdentifier, urlScheme }) => {
  console.log('StripeProvider initialized for web with key:', publishableKey?.substring(0, 10) + '...');
  return React.createElement(View, null, children);
};

// Mock CardField component
export const CardField = (props) => {
  const {
    style,
    onCardChange,
    onFocus,
    onBlur,
    cardStyle,
    placeholder,
    autofocus,
    ...otherProps
  } = props;

  React.useEffect(() => {
    // Simulate card change event
    if (onCardChange) {
      onCardChange({
        complete: false,
        validNumber: 'Unknown',
        validCVC: 'Unknown',
        validExpiryDate: 'Unknown',
        brand: 'Unknown',
      });
    }
  }, [onCardChange]);

  return React.createElement(
    View,
    {
      style: [
        styles.cardField,
        style,
      ],
      ...otherProps,
    },
    React.createElement(
      Text,
      { style: styles.cardFieldText },
      'Card input not available on web. Please use the mobile app for payments.'
    )
  );
};

// Mock useStripe hook
export const useStripe = () => {
  return {
    confirmPayment: async (paymentIntentClientSecret, data) => {
      console.warn('confirmPayment called on web platform');
      throw new Error('Payment processing is not available on web. Please use the mobile app.');
    },
    handleNextAction: async (paymentIntentClientSecret) => {
      console.warn('handleNextAction called on web platform');
      throw new Error('Payment processing is not available on web. Please use the mobile app.');
    },
    confirmSetupIntent: async (setupIntentClientSecret, data) => {
      console.warn('confirmSetupIntent called on web platform');
      throw new Error('Payment processing is not available on web. Please use the mobile app.');
    },
    createPaymentMethod: async (data) => {
      console.warn('createPaymentMethod called on web platform');
      throw new Error('Payment processing is not available on web. Please use the mobile app.');
    },
    retrievePaymentIntent: async (clientSecret) => {
      console.warn('retrievePaymentIntent called on web platform');
      throw new Error('Payment processing is not available on web. Please use the mobile app.');
    },
    retrieveSetupIntent: async (clientSecret) => {
      console.warn('retrieveSetupIntent called on web platform');
      throw new Error('Payment processing is not available on web. Please use the mobile app.');
    },
    handleURLCallback: async (url) => {
      console.warn('handleURLCallback called on web platform');
      return false;
    },
  };
};

// Mock useConfirmPayment hook
export const useConfirmPayment = () => {
  const { confirmPayment } = useStripe();
  return { confirmPayment };
};

// Mock useConfirmSetupIntent hook
export const useConfirmSetupIntent = () => {
  const { confirmSetupIntent } = useStripe();
  return { confirmSetupIntent };
};

// Mock Apple Pay components
export const ApplePayButton = (props) => {
  return React.createElement(
    View,
    { style: [styles.payButton, props.style] },
    React.createElement(
      Text,
      { style: styles.payButtonText },
      'Apple Pay not available on web'
    )
  );
};

export const useApplePay = () => {
  return {
    isApplePaySupported: false,
    presentApplePay: async () => {
      throw new Error('Apple Pay is not available on web platform');
    },
    confirmApplePayPayment: async () => {
      throw new Error('Apple Pay is not available on web platform');
    },
  };
};

// Mock Google Pay components
export const GooglePayButton = (props) => {
  return React.createElement(
    View,
    { style: [styles.payButton, props.style] },
    React.createElement(
      Text,
      { style: styles.payButtonText },
      'Google Pay not available on web'
    )
  );
};

export const useGooglePay = () => {
  return {
    isGooglePaySupported: false,
    initGooglePay: async () => {
      throw new Error('Google Pay is not available on web platform');
    },
    presentGooglePay: async () => {
      throw new Error('Google Pay is not available on web platform');
    },
    createGooglePayPaymentMethod: async () => {
      throw new Error('Google Pay is not available on web platform');
    },
  };
};

// Mock other Stripe components
export const CardFieldInput = CardField;
export const AuBECSDebitForm = (props) => {
  return React.createElement(
    View,
    { style: [styles.cardField, props.style] },
    React.createElement(
      Text,
      { style: styles.cardFieldText },
      'AU BECS Debit not available on web'
    )
  );
};

// Mock constants
export const CardFieldInputDetails = {
  COMPLETE: 'Complete',
  INCOMPLETE: 'Incomplete',
};

// Mock functions
export const initStripe = async (params) => {
  console.log('Stripe initialized for web with params:', params);
  return Promise.resolve();
};

export const createToken = async (params) => {
  console.warn('createToken called on web platform');
  throw new Error('Token creation is not available on web platform');
};

export const createPaymentMethod = async (params) => {
  console.warn('createPaymentMethod called on web platform');
  throw new Error('Payment method creation is not available on web platform');
};

// Styles for mock components
const styles = StyleSheet.create({
  cardField: {
    height: 50,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 16,
    justifyContent: 'center',
    backgroundColor: '#F8F8F8',
  },
  cardFieldText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
  },
  payButton: {
    height: 50,
    borderRadius: 8,
    backgroundColor: '#000000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  payButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

// Default export
export default {
  StripeProvider,
  CardField,
  useStripe,
  useConfirmPayment,
  useConfirmSetupIntent,
  ApplePayButton,
  useApplePay,
  GooglePayButton,
  useGooglePay,
  CardFieldInput,
  AuBECSDebitForm,
  CardFieldInputDetails,
  initStripe,
  createToken,
  createPaymentMethod,
};
