import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { CartItem, MenuItem, SelectedCustomization, SelectedAddOn, Restaurant } from '../types';
import { STORAGE_KEYS } from '../utils/constants';

interface CartState {
  // State
  items: CartItem[];
  restaurant: Restaurant | null;
  subtotal: number;
  deliveryFee: number;
  tax: number;
  tip: number;
  total: number;
  isLoading: boolean;
  error: string | null;

  // Actions
  addItem: (item: MenuItem, customizations: SelectedCustomization[], addOns: SelectedAddOn[], specialInstructions?: string) => void;
  removeItem: (itemId: string) => void;
  updateItemQuantity: (itemId: string, quantity: number) => void;
  updateItemCustomizations: (itemId: string, customizations: SelectedCustomization[], addOns: SelectedAddOn[]) => void;
  clearCart: () => void;
  setRestaurant: (restaurant: Restaurant) => void;
  setDeliveryFee: (fee: number) => void;
  setTip: (tip: number) => void;
  calculateTotals: () => void;
  
  // Utility functions
  getItemCount: () => number;
  getUniqueItemCount: () => number;
  isItemInCart: (menuItemId: string) => boolean;
  getCartItem: (menuItemId: string) => CartItem | undefined;
}

const calculateItemPrice = (
  menuItem: MenuItem,
  customizations: SelectedCustomization[],
  addOns: SelectedAddOn[]
): number => {
  let price = menuItem.price;

  // Add customization prices
  customizations.forEach(customization => {
    const customizationConfig = menuItem.customizations.find(c => c.id === customization.customizationId);
    if (customizationConfig) {
      customization.optionIds.forEach(optionId => {
        const option = customizationConfig.options.find(o => o.id === optionId);
        if (option) {
          price += option.price;
        }
      });
    }
  });

  // Add add-on prices
  addOns.forEach(addOn => {
    const addOnConfig = menuItem.addOns.find(a => a.id === addOn.addOnId);
    if (addOnConfig) {
      price += addOnConfig.price * addOn.quantity;
    }
  });

  return price;
};

const generateCartItemId = (
  menuItemId: string,
  customizations: SelectedCustomization[],
  addOns: SelectedAddOn[]
): string => {
  const customizationString = customizations
    .map(c => `${c.customizationId}:${c.optionIds.sort().join(',')}`)
    .sort()
    .join('|');
  
  const addOnString = addOns
    .map(a => `${a.addOnId}:${a.quantity}`)
    .sort()
    .join('|');

  return `${menuItemId}_${customizationString}_${addOnString}`;
};

export const useCartStore = create<CartState>()(
  persist(
    (set, get) => ({
      // Initial state
      items: [],
      restaurant: null,
      subtotal: 0,
      deliveryFee: 0,
      tax: 0,
      tip: 0,
      total: 0,
      isLoading: false,
      error: null,

      // Actions
      addItem: (
        menuItem: MenuItem,
        customizations: SelectedCustomization[],
        addOns: SelectedAddOn[],
        specialInstructions?: string
      ) => {
        const state = get();
        const itemPrice = calculateItemPrice(menuItem, customizations, addOns);
        const cartItemId = generateCartItemId(menuItem.id, customizations, addOns);

        // Check if item with same customizations already exists
        const existingItemIndex = state.items.findIndex(item => item.id === cartItemId);

        let newItems: CartItem[];

        if (existingItemIndex >= 0) {
          // Update existing item quantity
          newItems = [...state.items];
          newItems[existingItemIndex] = {
            ...newItems[existingItemIndex],
            quantity: newItems[existingItemIndex].quantity + 1,
            totalPrice: (newItems[existingItemIndex].quantity + 1) * itemPrice,
            specialInstructions: specialInstructions || newItems[existingItemIndex].specialInstructions,
          };
        } else {
          // Add new item
          const newItem: CartItem = {
            id: cartItemId,
            menuItem,
            quantity: 1,
            customizations,
            addOns,
            specialInstructions,
            totalPrice: itemPrice,
          };
          newItems = [...state.items, newItem];
        }

        set({ items: newItems });
        get().calculateTotals();
      },

      removeItem: (itemId: string) => {
        const state = get();
        const newItems = state.items.filter(item => item.id !== itemId);
        set({ items: newItems });
        get().calculateTotals();
      },

      updateItemQuantity: (itemId: string, quantity: number) => {
        const state = get();
        
        if (quantity <= 0) {
          get().removeItem(itemId);
          return;
        }

        const newItems = state.items.map(item => {
          if (item.id === itemId) {
            const itemPrice = calculateItemPrice(item.menuItem, item.customizations, item.addOns);
            return {
              ...item,
              quantity,
              totalPrice: quantity * itemPrice,
            };
          }
          return item;
        });

        set({ items: newItems });
        get().calculateTotals();
      },

      updateItemCustomizations: (
        itemId: string,
        customizations: SelectedCustomization[],
        addOns: SelectedAddOn[]
      ) => {
        const state = get();
        const item = state.items.find(item => item.id === itemId);
        
        if (!item) return;

        const newItemPrice = calculateItemPrice(item.menuItem, customizations, addOns);
        const newCartItemId = generateCartItemId(item.menuItem.id, customizations, addOns);

        // Remove old item
        const itemsWithoutOld = state.items.filter(item => item.id !== itemId);

        // Check if item with new customizations already exists
        const existingItemIndex = itemsWithoutOld.findIndex(item => item.id === newCartItemId);

        let newItems: CartItem[];

        if (existingItemIndex >= 0) {
          // Merge with existing item
          newItems = [...itemsWithoutOld];
          newItems[existingItemIndex] = {
            ...newItems[existingItemIndex],
            quantity: newItems[existingItemIndex].quantity + item.quantity,
            totalPrice: (newItems[existingItemIndex].quantity + item.quantity) * newItemPrice,
          };
        } else {
          // Create updated item
          const updatedItem: CartItem = {
            ...item,
            id: newCartItemId,
            customizations,
            addOns,
            totalPrice: item.quantity * newItemPrice,
          };
          newItems = [...itemsWithoutOld, updatedItem];
        }

        set({ items: newItems });
        get().calculateTotals();
      },

      clearCart: () => {
        set({
          items: [],
          restaurant: null,
          subtotal: 0,
          deliveryFee: 0,
          tax: 0,
          tip: 0,
          total: 0,
        });
      },

      setRestaurant: (restaurant: Restaurant) => {
        const state = get();
        
        // If switching to a different restaurant, clear the cart
        if (state.restaurant && state.restaurant.id !== restaurant.id) {
          set({
            items: [],
            restaurant,
            subtotal: 0,
            deliveryFee: restaurant.deliveryFee,
            tax: 0,
            tip: 0,
            total: 0,
          });
        } else {
          set({
            restaurant,
            deliveryFee: restaurant.deliveryFee,
          });
          get().calculateTotals();
        }
      },

      setDeliveryFee: (fee: number) => {
        set({ deliveryFee: fee });
        get().calculateTotals();
      },

      setTip: (tip: number) => {
        set({ tip });
        get().calculateTotals();
      },

      calculateTotals: () => {
        const state = get();
        const subtotal = state.items.reduce((sum, item) => sum + item.totalPrice, 0);
        const tax = subtotal * 0.08; // 8% tax rate
        const total = subtotal + state.deliveryFee + tax + state.tip;

        set({
          subtotal,
          tax,
          total,
        });
      },

      // Utility functions
      getItemCount: () => {
        const state = get();
        return state.items.reduce((count, item) => count + item.quantity, 0);
      },

      getUniqueItemCount: () => {
        const state = get();
        return state.items.length;
      },

      isItemInCart: (menuItemId: string) => {
        const state = get();
        return state.items.some(item => item.menuItem.id === menuItemId);
      },

      getCartItem: (menuItemId: string) => {
        const state = get();
        return state.items.find(item => item.menuItem.id === menuItemId);
      },
    }),
    {
      name: 'cart-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        items: state.items,
        restaurant: state.restaurant,
        deliveryFee: state.deliveryFee,
        tip: state.tip,
      }),
    }
  )
);
