import React, { Component, ReactNode } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { AppError, errorHandler } from '../../utils/errorHandler';
import { ErrorBoundaryFallback } from './ErrorDisplay';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../utils/constants';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: React.ComponentType<ErrorBoundaryFallbackProps>;
  onError?: (error: AppError, errorInfo: React.ErrorInfo) => void;
  resetOnPropsChange?: boolean;
  resetKeys?: Array<string | number>;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: AppError;
  errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryFallbackProps {
  error?: AppError;
  errorInfo?: React.ErrorInfo;
  resetError: () => void;
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private resetTimeoutId: number | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    const appError = errorHandler.handleError(error, 'ErrorBoundary');
    return {
      hasError: true,
      error: appError,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const appError = errorHandler.handleError(error, `ErrorBoundary: ${errorInfo.componentStack}`);
    
    this.setState({
      error: appError,
      errorInfo,
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(appError, errorInfo);
    }
  }

  componentDidUpdate(prevProps: ErrorBoundaryProps) {
    const { resetOnPropsChange, resetKeys } = this.props;
    const { hasError } = this.state;

    // Reset error state if resetKeys have changed
    if (hasError && resetOnPropsChange && resetKeys) {
      const prevResetKeys = prevProps.resetKeys || [];
      const hasResetKeyChanged = resetKeys.some(
        (key, index) => key !== prevResetKeys[index]
      );

      if (hasResetKeyChanged) {
        this.resetError();
      }
    }
  }

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  resetError = () => {
    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
    });
  };

  render() {
    const { hasError, error, errorInfo } = this.state;
    const { children, fallback: FallbackComponent } = this.props;

    if (hasError) {
      if (FallbackComponent) {
        return (
          <FallbackComponent
            error={error}
            errorInfo={errorInfo}
            resetError={this.resetError}
          />
        );
      }

      return (
        <ErrorBoundaryFallback
          error={error}
          onRetry={this.resetError}
        />
      );
    }

    return children;
  }
}

// Screen-level error boundary
export const ScreenErrorBoundary: React.FC<{
  children: ReactNode;
  screenName?: string;
}> = ({ children, screenName }) => {
  const handleError = (error: AppError, errorInfo: React.ErrorInfo) => {
    console.error(`Screen Error (${screenName}):`, error, errorInfo);
  };

  return (
    <ErrorBoundary
      onError={handleError}
      fallback={ScreenErrorFallback}
    >
      {children}
    </ErrorBoundary>
  );
};

// Component-level error boundary
export const ComponentErrorBoundary: React.FC<{
  children: ReactNode;
  componentName?: string;
  fallback?: React.ComponentType<any>;
}> = ({ children, componentName, fallback }) => {
  const handleError = (error: AppError, errorInfo: React.ErrorInfo) => {
    console.error(`Component Error (${componentName}):`, error, errorInfo);
  };

  return (
    <ErrorBoundary
      onError={handleError}
      fallback={fallback || ComponentErrorFallback}
    >
      {children}
    </ErrorBoundary>
  );
};

// Custom fallback components
const ScreenErrorFallback: React.FC<ErrorBoundaryFallbackProps> = ({
  error,
  resetError,
}) => {
  return (
    <View style={styles.screenFallback}>
      <Ionicons name="warning-outline" size={64} color={COLORS.error} />
      <Text style={styles.screenFallbackTitle}>Oops! Something went wrong</Text>
      <Text style={styles.screenFallbackMessage}>
        {error?.userMessage || 'We encountered an unexpected error. Please try again.'}
      </Text>
      <TouchableOpacity onPress={resetError} style={styles.screenFallbackButton}>
        <Text style={styles.screenFallbackButtonText}>Try Again</Text>
      </TouchableOpacity>
    </View>
  );
};

const ComponentErrorFallback: React.FC<ErrorBoundaryFallbackProps> = ({
  error,
  resetError,
}) => {
  return (
    <View style={styles.componentFallback}>
      <Ionicons name="alert-circle-outline" size={24} color={COLORS.error} />
      <Text style={styles.componentFallbackText}>
        Unable to load this section
      </Text>
      <TouchableOpacity onPress={resetError} style={styles.componentFallbackButton}>
        <Text style={styles.componentFallbackButtonText}>Retry</Text>
      </TouchableOpacity>
    </View>
  );
};

// HOC for wrapping components with error boundary
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Partial<ErrorBoundaryProps>
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

const styles = StyleSheet.create({
  // Screen Error Fallback
  screenFallback: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
    backgroundColor: COLORS.background,
  },
  screenFallbackTitle: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  screenFallbackMessage: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
    lineHeight: 22,
    paddingHorizontal: SPACING.md,
  },
  screenFallbackButton: {
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.lg,
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.md,
  },
  screenFallbackButtonText: {
    color: COLORS.white,
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
  },

  // Component Error Fallback
  componentFallback: {
    alignItems: 'center',
    padding: SPACING.md,
    backgroundColor: COLORS.gray[50],
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    borderColor: COLORS.border,
    margin: SPACING.sm,
  },
  componentFallbackText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  componentFallbackButton: {
    backgroundColor: COLORS.primary + '10',
    borderColor: COLORS.primary,
    borderWidth: 1,
    borderRadius: BORDER_RADIUS.sm,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.xs,
  },
  componentFallbackButtonText: {
    color: COLORS.primary,
    fontSize: TYPOGRAPHY.fontSize.xs,
    fontWeight: '600',
  },
});

export default ErrorBoundary;
