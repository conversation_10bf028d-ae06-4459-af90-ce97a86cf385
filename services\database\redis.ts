import Redis, { RedisOptions } from 'ioredis';
import { redisConfig } from '../config/environment';

export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  prefix?: string;
}

export interface RedisConnection {
  get(key: string): Promise<string | null>;
  set(key: string, value: string, ttl?: number): Promise<void>;
  del(key: string): Promise<number>;
  exists(key: string): Promise<boolean>;
  expire(key: string, ttl: number): Promise<boolean>;
  close(): Promise<void>;
}

class RedisService implements RedisConnection {
  private client: Redis;
  private static instance: RedisService;
  private readonly defaultTTL = 3600; // 1 hour
  private readonly keyPrefix = 'foodway:';

  private constructor() {
    const options: RedisOptions = {
      host: redisConfig.host,
      port: redisConfig.port,
      password: redisConfig.password,
      db: redisConfig.db,
      retryDelayOnFailover: redisConfig.retryDelayOnFailover,
      maxRetriesPerRequest: redisConfig.maxRetriesPerRequest,
      lazyConnect: redisConfig.lazyConnect,
      connectTimeout: 10000,
      commandTimeout: 5000,
      // Connection pool settings
      family: 4,
      keepAlive: true,
      // Retry strategy
      retryStrategy: (times) => {
        const delay = Math.min(times * 50, 2000);
        return delay;
      },
    };

    // Use connection string if available
    if (redisConfig.url) {
      this.client = new Redis(redisConfig.url, options);
    } else {
      this.client = new Redis(options);
    }

    // Handle connection events
    this.client.on('connect', () => {
      console.log('Redis connected');
    });

    this.client.on('ready', () => {
      console.log('Redis ready');
    });

    this.client.on('error', (error) => {
      console.error('Redis error:', error);
    });

    this.client.on('close', () => {
      console.log('Redis connection closed');
    });

    this.client.on('reconnecting', () => {
      console.log('Redis reconnecting');
    });
  }

  public static getInstance(): RedisService {
    if (!RedisService.instance) {
      RedisService.instance = new RedisService();
    }
    return RedisService.instance;
  }

  private getKey(key: string, prefix?: string): string {
    const finalPrefix = prefix || this.keyPrefix;
    return `${finalPrefix}${key}`;
  }

  async get(key: string, prefix?: string): Promise<string | null> {
    try {
      return await this.client.get(this.getKey(key, prefix));
    } catch (error) {
      console.error('Redis GET error:', error);
      return null;
    }
  }

  async set(key: string, value: string, ttl?: number, prefix?: string): Promise<void> {
    try {
      const finalKey = this.getKey(key, prefix);
      const finalTTL = ttl || this.defaultTTL;
      await this.client.setex(finalKey, finalTTL, value);
    } catch (error) {
      console.error('Redis SET error:', error);
      throw error;
    }
  }

  async del(key: string, prefix?: string): Promise<number> {
    try {
      return await this.client.del(this.getKey(key, prefix));
    } catch (error) {
      console.error('Redis DEL error:', error);
      return 0;
    }
  }

  async exists(key: string, prefix?: string): Promise<boolean> {
    try {
      const result = await this.client.exists(this.getKey(key, prefix));
      return result === 1;
    } catch (error) {
      console.error('Redis EXISTS error:', error);
      return false;
    }
  }

  async expire(key: string, ttl: number, prefix?: string): Promise<boolean> {
    try {
      const result = await this.client.expire(this.getKey(key, prefix), ttl);
      return result === 1;
    } catch (error) {
      console.error('Redis EXPIRE error:', error);
      return false;
    }
  }

  async healthCheck(): Promise<{ status: 'connected' | 'disconnected'; latency?: number }> {
    try {
      const start = Date.now();
      await this.client.ping();
      const latency = Date.now() - start;
      return { status: 'connected', latency };
    } catch (error) {
      console.error('Redis health check failed:', error);
      return { status: 'disconnected' };
    }
  }

  async close(): Promise<void> {
    await this.client.quit();
  }

  // JSON operations
  async getJSON<T = any>(key: string, prefix?: string): Promise<T | null> {
    try {
      const value = await this.get(key, prefix);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Redis GET JSON error:', error);
      return null;
    }
  }

  async setJSON<T = any>(key: string, value: T, ttl?: number, prefix?: string): Promise<void> {
    try {
      await this.set(key, JSON.stringify(value), ttl, prefix);
    } catch (error) {
      console.error('Redis SET JSON error:', error);
      throw error;
    }
  }

  // Hash operations
  async hget(key: string, field: string, prefix?: string): Promise<string | null> {
    try {
      return await this.client.hget(this.getKey(key, prefix), field);
    } catch (error) {
      console.error('Redis HGET error:', error);
      return null;
    }
  }

  async hset(key: string, field: string, value: string, prefix?: string): Promise<void> {
    try {
      await this.client.hset(this.getKey(key, prefix), field, value);
    } catch (error) {
      console.error('Redis HSET error:', error);
      throw error;
    }
  }

  async hgetall(key: string, prefix?: string): Promise<Record<string, string>> {
    try {
      return await this.client.hgetall(this.getKey(key, prefix));
    } catch (error) {
      console.error('Redis HGETALL error:', error);
      return {};
    }
  }

  async hdel(key: string, field: string, prefix?: string): Promise<number> {
    try {
      return await this.client.hdel(this.getKey(key, prefix), field);
    } catch (error) {
      console.error('Redis HDEL error:', error);
      return 0;
    }
  }

  // List operations
  async lpush(key: string, value: string, prefix?: string): Promise<number> {
    try {
      return await this.client.lpush(this.getKey(key, prefix), value);
    } catch (error) {
      console.error('Redis LPUSH error:', error);
      return 0;
    }
  }

  async rpush(key: string, value: string, prefix?: string): Promise<number> {
    try {
      return await this.client.rpush(this.getKey(key, prefix), value);
    } catch (error) {
      console.error('Redis RPUSH error:', error);
      return 0;
    }
  }

  async lpop(key: string, prefix?: string): Promise<string | null> {
    try {
      return await this.client.lpop(this.getKey(key, prefix));
    } catch (error) {
      console.error('Redis LPOP error:', error);
      return null;
    }
  }

  async rpop(key: string, prefix?: string): Promise<string | null> {
    try {
      return await this.client.rpop(this.getKey(key, prefix));
    } catch (error) {
      console.error('Redis RPOP error:', error);
      return null;
    }
  }

  async lrange(key: string, start: number, stop: number, prefix?: string): Promise<string[]> {
    try {
      return await this.client.lrange(this.getKey(key, prefix), start, stop);
    } catch (error) {
      console.error('Redis LRANGE error:', error);
      return [];
    }
  }

  // Set operations
  async sadd(key: string, member: string, prefix?: string): Promise<number> {
    try {
      return await this.client.sadd(this.getKey(key, prefix), member);
    } catch (error) {
      console.error('Redis SADD error:', error);
      return 0;
    }
  }

  async srem(key: string, member: string, prefix?: string): Promise<number> {
    try {
      return await this.client.srem(this.getKey(key, prefix), member);
    } catch (error) {
      console.error('Redis SREM error:', error);
      return 0;
    }
  }

  async smembers(key: string, prefix?: string): Promise<string[]> {
    try {
      return await this.client.smembers(this.getKey(key, prefix));
    } catch (error) {
      console.error('Redis SMEMBERS error:', error);
      return [];
    }
  }

  async sismember(key: string, member: string, prefix?: string): Promise<boolean> {
    try {
      const result = await this.client.sismember(this.getKey(key, prefix), member);
      return result === 1;
    } catch (error) {
      console.error('Redis SISMEMBER error:', error);
      return false;
    }
  }

  // Cache utilities
  async cache<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    const { ttl = this.defaultTTL, prefix } = options;
    
    // Try to get from cache first
    const cached = await this.getJSON<T>(key, prefix);
    if (cached !== null) {
      return cached;
    }

    // Fetch fresh data
    const data = await fetchFunction();
    
    // Cache the result
    await this.setJSON(key, data, ttl, prefix);
    
    return data;
  }

  async invalidatePattern(pattern: string): Promise<number> {
    try {
      const keys = await this.client.keys(pattern);
      if (keys.length === 0) return 0;
      return await this.client.del(...keys);
    } catch (error) {
      console.error('Redis invalidate pattern error:', error);
      return 0;
    }
  }
}

// Export singleton instance
export const redis = RedisService.getInstance();

// Export types
export type { Redis, RedisOptions };
