import { VALIDATION } from './constants';
import { ValidationError } from './errorHandler';

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
  message?: string;
}

export interface ValidationSchema {
  [key: string]: ValidationRule;
}

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
  firstError?: string;
}

export class FormValidator {
  public schema: ValidationSchema;

  constructor(schema: ValidationSchema) {
    this.schema = schema;
  }

  validate(data: Record<string, any>): ValidationResult {
    const errors: Record<string, string> = {};

    Object.keys(this.schema).forEach(field => {
      const rule = this.schema[field];
      const value = data[field];
      const error = this.validateField(field, value, rule);
      
      if (error) {
        errors[field] = error;
      }
    });

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      firstError: Object.values(errors)[0],
    };
  }

  validateField(field: string, value: any, rule: ValidationRule): string | null {
    // Required validation
    if (rule.required && (value === undefined || value === null || value === '')) {
      return rule.message || `${this.formatFieldName(field)} is required`;
    }

    // Skip other validations if value is empty and not required
    if (!rule.required && (value === undefined || value === null || value === '')) {
      return null;
    }

    // String validations
    if (typeof value === 'string') {
      // Min length validation
      if (rule.minLength && value.length < rule.minLength) {
        return rule.message || `${this.formatFieldName(field)} must be at least ${rule.minLength} characters`;
      }

      // Max length validation
      if (rule.maxLength && value.length > rule.maxLength) {
        return rule.message || `${this.formatFieldName(field)} must be no more than ${rule.maxLength} characters`;
      }

      // Pattern validation
      if (rule.pattern && !rule.pattern.test(value)) {
        return rule.message || `${this.formatFieldName(field)} format is invalid`;
      }
    }

    // Custom validation
    if (rule.custom) {
      const customError = rule.custom(value);
      if (customError) {
        return customError;
      }
    }

    return null;
  }

  private formatFieldName(field: string): string {
    return field.charAt(0).toUpperCase() + field.slice(1).replace(/([A-Z])/g, ' $1');
  }

  // Validate single field
  validateSingle(field: string, value: any): string | null {
    const rule = this.schema[field];
    if (!rule) return null;
    
    return this.validateField(field, value, rule);
  }

  // Add or update validation rule
  addRule(field: string, rule: ValidationRule): void {
    this.schema[field] = rule;
  }

  // Remove validation rule
  removeRule(field: string): void {
    delete this.schema[field];
  }
}

// Pre-defined validation schemas
export const loginValidationSchema: ValidationSchema = {
  email: {
    required: true,
    pattern: VALIDATION.EMAIL_REGEX,
    message: 'Please enter a valid email address',
  },
  password: {
    required: true,
    minLength: VALIDATION.PASSWORD_MIN_LENGTH,
    message: `Password must be at least ${VALIDATION.PASSWORD_MIN_LENGTH} characters`,
  },
};

export const registerValidationSchema: ValidationSchema = {
  firstName: {
    required: true,
    minLength: VALIDATION.NAME_MIN_LENGTH,
    maxLength: VALIDATION.NAME_MAX_LENGTH,
    pattern: /^[a-zA-Z\s]+$/,
    message: 'First name should only contain letters',
  },
  lastName: {
    required: true,
    minLength: VALIDATION.NAME_MIN_LENGTH,
    maxLength: VALIDATION.NAME_MAX_LENGTH,
    pattern: /^[a-zA-Z\s]+$/,
    message: 'Last name should only contain letters',
  },
  email: {
    required: true,
    pattern: VALIDATION.EMAIL_REGEX,
    message: 'Please enter a valid email address',
  },
  phone: {
    required: true,
    pattern: VALIDATION.PHONE_REGEX,
    message: 'Please enter a valid phone number',
  },
  password: {
    required: true,
    minLength: VALIDATION.PASSWORD_MIN_LENGTH,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    message: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
  },
  confirmPassword: {
    required: true,
    custom: (value, data) => {
      if (value !== data?.password) {
        return 'Passwords do not match';
      }
      return null;
    },
  },
};

export const addressValidationSchema: ValidationSchema = {
  type: {
    required: true,
    message: 'Please select an address type',
  },
  label: {
    required: true,
    minLength: 2,
    maxLength: 50,
    message: 'Address label must be between 2 and 50 characters',
  },
  street: {
    required: true,
    minLength: 5,
    maxLength: 100,
    message: 'Street address must be between 5 and 100 characters',
  },
  city: {
    required: true,
    minLength: 2,
    maxLength: 50,
    pattern: /^[a-zA-Z\s]+$/,
    message: 'City should only contain letters',
  },
  state: {
    required: true,
    minLength: 2,
    maxLength: 50,
    message: 'Please enter a valid state',
  },
  zipCode: {
    required: true,
    pattern: /^\d{5}(-\d{4})?$/,
    message: 'Please enter a valid ZIP code',
  },
  instructions: {
    required: false,
    maxLength: VALIDATION.SPECIAL_INSTRUCTIONS_MAX_LENGTH,
    message: `Instructions must be no more than ${VALIDATION.SPECIAL_INSTRUCTIONS_MAX_LENGTH} characters`,
  },
};

export const reviewValidationSchema: ValidationSchema = {
  rating: {
    required: true,
    custom: (value) => {
      const rating = Number(value);
      if (isNaN(rating) || rating < 1 || rating > 5) {
        return 'Rating must be between 1 and 5';
      }
      return null;
    },
  },
  comment: {
    required: false,
    maxLength: VALIDATION.REVIEW_MAX_LENGTH,
    message: `Review must be no more than ${VALIDATION.REVIEW_MAX_LENGTH} characters`,
  },
};

// Utility functions
export const createValidator = (schema: ValidationSchema): FormValidator => {
  return new FormValidator(schema);
};

export const validateForm = (data: Record<string, any>, schema: ValidationSchema): ValidationResult => {
  const validator = new FormValidator(schema);
  return validator.validate(data);
};

export const validateField = (field: string, value: any, rule: ValidationRule): string | null => {
  const validator = new FormValidator({ [field]: rule });
  return validator.validateSingle(field, value);
};

// Real-time validation hook-like function
export const createFieldValidator = (schema: ValidationSchema) => {
  const validator = new FormValidator(schema);
  
  return {
    validate: (data: Record<string, any>) => validator.validate(data),
    validateField: (field: string, value: any) => validator.validateSingle(field, value),
    addRule: (field: string, rule: ValidationRule) => validator.addRule(field, rule),
    removeRule: (field: string) => validator.removeRule(field),
  };
};

// Validation error thrower for API integration
export const throwValidationError = (errors: Record<string, string>): never => {
  const firstError = Object.values(errors)[0];
  throw new ValidationError(firstError, undefined, errors);
};

// Common validation patterns
export const ValidationPatterns = {
  EMAIL: VALIDATION.EMAIL_REGEX,
  PHONE: VALIDATION.PHONE_REGEX,
  PASSWORD_STRONG: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
  NAME: /^[a-zA-Z\s]+$/,
  ZIP_CODE: /^\d{5}(-\d{4})?$/,
  CREDIT_CARD: /^\d{4}\s?\d{4}\s?\d{4}\s?\d{4}$/,
  CVV: /^\d{3,4}$/,
  EXPIRY_DATE: /^(0[1-9]|1[0-2])\/\d{2}$/,
} as const;

// Validation messages
export const ValidationMessages = {
  REQUIRED: (field: string) => `${field} is required`,
  MIN_LENGTH: (field: string, length: number) => `${field} must be at least ${length} characters`,
  MAX_LENGTH: (field: string, length: number) => `${field} must be no more than ${length} characters`,
  INVALID_FORMAT: (field: string) => `${field} format is invalid`,
  PASSWORDS_DONT_MATCH: 'Passwords do not match',
  INVALID_EMAIL: 'Please enter a valid email address',
  INVALID_PHONE: 'Please enter a valid phone number',
  WEAK_PASSWORD: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
} as const;
