// Core Types for FoodWay Customer App

export interface User {
  id: string;
  email: string;
  phone?: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  dateOfBirth?: string;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Address {
  id: string;
  userId: string;
  type: 'home' | 'work' | 'other';
  label: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  latitude: number;
  longitude: number;
  isDefault: boolean;
  instructions?: string;
}

export interface Restaurant {
  id: string;
  name: string;
  description: string;
  image: string;
  coverImage?: string;
  cuisine: string[];
  rating: number;
  reviewCount: number;
  deliveryTime: string;
  deliveryFee: number;
  minimumOrder: number;
  isOpen: boolean;
  address: string;
  latitude: number;
  longitude: number;
  phone: string;
  categories: Category[];
  featured: boolean;
  promoted: boolean;
  tags: string[];
}

export interface Category {
  id: string;
  name: string;
  image?: string;
  restaurantId?: string;
}

export interface MenuItem {
  id: string;
  restaurantId: string;
  categoryId: string;
  name: string;
  description: string;
  image?: string;
  price: number;
  originalPrice?: number;
  isAvailable: boolean;
  isVegetarian: boolean;
  isVegan: boolean;
  isGlutenFree: boolean;
  isSpicy: boolean;
  spiceLevel?: number;
  calories?: number;
  preparationTime?: string;
  customizations: Customization[];
  addOns: AddOn[];
  tags: string[];
}

export interface Customization {
  id: string;
  name: string;
  type: 'single' | 'multiple';
  required: boolean;
  options: CustomizationOption[];
}

export interface CustomizationOption {
  id: string;
  name: string;
  price: number;
  isDefault?: boolean;
}

export interface AddOn {
  id: string;
  name: string;
  price: number;
  image?: string;
}

export interface CartItem {
  id: string;
  menuItem: MenuItem;
  quantity: number;
  customizations: SelectedCustomization[];
  addOns: SelectedAddOn[];
  specialInstructions?: string;
  totalPrice: number;
}

export interface SelectedCustomization {
  customizationId: string;
  optionIds: string[];
}

export interface SelectedAddOn {
  addOnId: string;
  quantity: number;
}

export interface Order {
  id: string;
  userId: string;
  restaurantId: string;
  restaurant: Restaurant;
  items: OrderItem[];
  status: OrderStatus;
  subtotal: number;
  deliveryFee: number;
  tax: number;
  tip: number;
  total: number;
  paymentMethod: PaymentMethod;
  deliveryAddress: Address;
  estimatedDeliveryTime: string;
  actualDeliveryTime?: string;
  specialInstructions?: string;
  createdAt: string;
  updatedAt: string;
  tracking?: OrderTracking;
}

export interface OrderItem {
  id: string;
  menuItem: MenuItem;
  quantity: number;
  customizations: SelectedCustomization[];
  addOns: SelectedAddOn[];
  specialInstructions?: string;
  price: number;
}

export type OrderStatus = 
  | 'pending'
  | 'confirmed'
  | 'preparing'
  | 'ready'
  | 'picked_up'
  | 'on_the_way'
  | 'delivered'
  | 'cancelled';

export interface OrderTracking {
  orderId: string;
  driverId?: string;
  driverName?: string;
  driverPhone?: string;
  driverLocation?: {
    latitude: number;
    longitude: number;
  };
  estimatedArrival: string;
  status: OrderStatus;
  timeline: TrackingEvent[];
}

export interface TrackingEvent {
  status: OrderStatus;
  timestamp: string;
  message: string;
}

export interface PaymentMethod {
  id: string;
  type: 'card' | 'paypal' | 'apple_pay' | 'google_pay' | 'cash';
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
}

export interface Review {
  id: string;
  userId: string;
  restaurantId: string;
  orderId: string;
  rating: number;
  comment?: string;
  images?: string[];
  createdAt: string;
  user: {
    firstName: string;
    lastName: string;
    avatar?: string;
  };
}

export interface Promotion {
  id: string;
  title: string;
  description: string;
  image?: string;
  type: 'discount' | 'free_delivery' | 'cashback';
  value: number;
  minimumOrder?: number;
  code?: string;
  validFrom: string;
  validTo: string;
  isActive: boolean;
  restaurantIds?: string[];
}

export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'order' | 'promotion' | 'general';
  data?: any;
  isRead: boolean;
  createdAt: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
}

export interface AddressForm {
  type: 'home' | 'work' | 'other';
  label: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  instructions?: string;
}

// Filter Types
export interface RestaurantFilters {
  cuisine?: string[];
  rating?: number;
  deliveryTime?: number;
  priceRange?: [number, number];
  features?: string[];
  sortBy?: 'rating' | 'delivery_time' | 'distance' | 'popularity';
}

export interface SearchFilters extends RestaurantFilters {
  query?: string;
  location?: {
    latitude: number;
    longitude: number;
    radius: number;
  };
}

// Navigation Types
export type RootStackParamList = {
  '(tabs)': undefined;
  '(auth)': undefined;
  'restaurant/[id]': { id: string };
  'order/[id]': { id: string };
  'checkout': undefined;
  'order-tracking/[id]': { id: string };
};

export type TabParamList = {
  'index': undefined;
  'search': undefined;
  'orders': undefined;
  'profile': undefined;
};

export type AuthStackParamList = {
  'login': undefined;
  'register': undefined;
  'forgot-password': undefined;
  'verify-otp': { email: string };
};
