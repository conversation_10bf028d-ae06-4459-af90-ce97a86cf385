import {
  FormValidator,
  ValidationRule,
  ValidationSchema,
  createValidator,
  validateForm,
  validateField,
  loginValidationSchema,
  registerValidationSchema,
  addressValidationSchema,
  ValidationPatterns,
} from '../../utils/validation';

describe('FormValidator', () => {
  describe('Basic Validation', () => {
    const schema: ValidationSchema = {
      email: {
        required: true,
        pattern: ValidationPatterns.EMAIL,
        message: 'Please enter a valid email',
      },
      password: {
        required: true,
        minLength: 8,
        message: 'Password must be at least 8 characters',
      },
      age: {
        required: false,
        custom: (value) => {
          const age = Number(value);
          if (isNaN(age) || age < 0 || age > 120) {
            return 'Please enter a valid age';
          }
          return null;
        },
      },
    };

    let validator: FormValidator;

    beforeEach(() => {
      validator = new FormValidator(schema);
    });

    it('should validate required fields', () => {
      const result = validator.validate({
        email: '',
        password: 'validpassword',
      });

      expect(result.isValid).toBe(false);
      expect(result.errors.email).toBe('Email is required');
      expect(result.errors.password).toBeUndefined();
    });

    it('should validate email pattern', () => {
      const result = validator.validate({
        email: 'invalid-email',
        password: 'validpassword',
      });

      expect(result.isValid).toBe(false);
      expect(result.errors.email).toBe('Please enter a valid email');
    });

    it('should validate minimum length', () => {
      const result = validator.validate({
        email: '<EMAIL>',
        password: 'short',
      });

      expect(result.isValid).toBe(false);
      expect(result.errors.password).toBe('Password must be at least 8 characters');
    });

    it('should validate custom rules', () => {
      const result = validator.validate({
        email: '<EMAIL>',
        password: 'validpassword',
        age: '150',
      });

      expect(result.isValid).toBe(false);
      expect(result.errors.age).toBe('Please enter a valid age');
    });

    it('should pass valid data', () => {
      const result = validator.validate({
        email: '<EMAIL>',
        password: 'validpassword',
        age: '25',
      });

      expect(result.isValid).toBe(true);
      expect(Object.keys(result.errors)).toHaveLength(0);
    });

    it('should handle optional fields', () => {
      const result = validator.validate({
        email: '<EMAIL>',
        password: 'validpassword',
        // age is optional and not provided
      });

      expect(result.isValid).toBe(true);
      expect(result.errors.age).toBeUndefined();
    });

    it('should validate single field', () => {
      const error = validator.validateSingle('email', 'invalid-email');
      expect(error).toBe('Please enter a valid email');

      const noError = validator.validateSingle('email', '<EMAIL>');
      expect(noError).toBeNull();
    });

    it('should return first error', () => {
      const result = validator.validate({
        email: '',
        password: '',
      });

      expect(result.firstError).toBe('Email is required');
    });
  });

  describe('Dynamic Schema Management', () => {
    it('should add validation rules', () => {
      const validator = new FormValidator({});
      
      validator.addRule('username', {
        required: true,
        minLength: 3,
        message: 'Username must be at least 3 characters',
      });

      const result = validator.validate({ username: 'ab' });
      expect(result.isValid).toBe(false);
      expect(result.errors.username).toBe('Username must be at least 3 characters');
    });

    it('should remove validation rules', () => {
      const validator = new FormValidator({
        username: { required: true },
      });

      validator.removeRule('username');
      const result = validator.validate({ username: '' });
      expect(result.isValid).toBe(true);
    });
  });

  describe('Pre-defined Schemas', () => {
    describe('Login Validation', () => {
      it('should validate login form', () => {
        const validator = createValidator(loginValidationSchema);
        
        const result = validator.validate({
          email: '<EMAIL>',
          password: 'validpassword123',
        });

        expect(result.isValid).toBe(true);
      });

      it('should reject invalid login data', () => {
        const validator = createValidator(loginValidationSchema);
        
        const result = validator.validate({
          email: 'invalid-email',
          password: 'short',
        });

        expect(result.isValid).toBe(false);
        expect(result.errors.email).toBeDefined();
        expect(result.errors.password).toBeDefined();
      });
    });

    describe('Register Validation', () => {
      it('should validate registration form', () => {
        const validator = createValidator(registerValidationSchema);
        
        const result = validator.validate({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
          password: 'ValidPass123!',
          confirmPassword: 'ValidPass123!',
        });

        expect(result.isValid).toBe(true);
      });

      it('should reject invalid names', () => {
        const validator = createValidator(registerValidationSchema);
        
        const result = validator.validate({
          firstName: 'J',
          lastName: 'D123',
          email: '<EMAIL>',
          phone: '+1234567890',
          password: 'ValidPass123!',
          confirmPassword: 'ValidPass123!',
        });

        expect(result.isValid).toBe(false);
        expect(result.errors.firstName).toBeDefined();
        expect(result.errors.lastName).toBeDefined();
      });

      it('should validate password confirmation', () => {
        const validator = createValidator(registerValidationSchema);
        
        const result = validator.validate({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
          password: 'ValidPass123!',
          confirmPassword: 'DifferentPass123!',
        });

        expect(result.isValid).toBe(false);
        expect(result.errors.confirmPassword).toBe('Passwords do not match');
      });
    });

    describe('Address Validation', () => {
      it('should validate address form', () => {
        const validator = createValidator(addressValidationSchema);
        
        const result = validator.validate({
          type: 'home',
          label: 'Home Address',
          street: '123 Main Street',
          city: 'San Francisco',
          state: 'California',
          zipCode: '94102',
          instructions: 'Ring doorbell',
        });

        expect(result.isValid).toBe(true);
      });

      it('should reject invalid ZIP codes', () => {
        const validator = createValidator(addressValidationSchema);
        
        const result = validator.validate({
          type: 'home',
          label: 'Home Address',
          street: '123 Main Street',
          city: 'San Francisco',
          state: 'California',
          zipCode: 'invalid',
        });

        expect(result.isValid).toBe(false);
        expect(result.errors.zipCode).toBeDefined();
      });
    });
  });

  describe('Utility Functions', () => {
    it('should validate form using utility function', () => {
      const schema: ValidationSchema = {
        name: { required: true },
      };

      const result = validateForm({ name: 'John' }, schema);
      expect(result.isValid).toBe(true);
    });

    it('should validate field using utility function', () => {
      const rule: ValidationRule = {
        required: true,
        minLength: 3,
      };

      const error = validateField('username', 'ab', rule);
      expect(error).toBeDefined();
    });
  });

  describe('Edge Cases', () => {
    it('should handle null and undefined values', () => {
      const validator = new FormValidator({
        field: { required: false, minLength: 3 },
      });

      const result1 = validator.validate({ field: null });
      const result2 = validator.validate({ field: undefined });
      const result3 = validator.validate({});

      expect(result1.isValid).toBe(true);
      expect(result2.isValid).toBe(true);
      expect(result3.isValid).toBe(true);
    });

    it('should handle empty strings correctly', () => {
      const validator = new FormValidator({
        required_field: { required: true },
        optional_field: { required: false, minLength: 3 },
      });

      const result = validator.validate({
        required_field: '',
        optional_field: '',
      });

      expect(result.isValid).toBe(false);
      expect(result.errors.required_field).toBeDefined();
      expect(result.errors.optional_field).toBeUndefined();
    });

    it('should handle non-string values', () => {
      const validator = new FormValidator({
        number_field: {
          custom: (value) => {
            const num = Number(value);
            return isNaN(num) ? 'Must be a number' : null;
          },
        },
      });

      const result1 = validator.validate({ number_field: 123 });
      const result2 = validator.validate({ number_field: 'abc' });

      expect(result1.isValid).toBe(true);
      expect(result2.isValid).toBe(false);
    });
  });

  describe('Performance', () => {
    it('should validate large forms efficiently', () => {
      const schema: ValidationSchema = {};
      const data: Record<string, any> = {};

      // Create a large form with 100 fields
      for (let i = 0; i < 100; i++) {
        schema[`field${i}`] = {
          required: true,
          minLength: 3,
          maxLength: 50,
        };
        data[`field${i}`] = `value${i}`;
      }

      const validator = new FormValidator(schema);
      const startTime = Date.now();
      
      const result = validator.validate(data);
      
      const endTime = Date.now();
      const processingTime = endTime - startTime;

      expect(result.isValid).toBe(true);
      expect(processingTime).toBeLessThan(100); // Should complete in less than 100ms
    });
  });
});
