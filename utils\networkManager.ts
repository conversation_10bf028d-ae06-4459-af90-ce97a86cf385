// import NetInfo, { NetInfoState } from '@react-native-community/netinfo'; // Temporarily disabled
import React from 'react';
import { axiosInstance, AxiosRequestConfig } from '../services/config/axios';
import { errorHandler, NetworkError } from './errorHandler';
import { storageManager } from './storage';

// Network status types
export interface NetworkStatus {
  isConnected: boolean;
  isInternetReachable: boolean | null;
  type: string;
  details: any;
}

// Queued request interface
export interface QueuedRequest {
  id: string;
  url: string;
  options: AxiosRequestConfig;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
  priority: 'low' | 'normal' | 'high';
}

// Network event types
export type NetworkEventType = 'connected' | 'disconnected' | 'slow' | 'fast';

export interface NetworkEventListener {
  (status: NetworkStatus, eventType: NetworkEventType): void;
}

class NetworkManager {
  private static instance: NetworkManager;
  private isConnected: boolean = true;
  private isInternetReachable: boolean | null = null;
  private networkType: string = 'unknown';
  private listeners: NetworkEventListener[] = [];
  private requestQueue: QueuedRequest[] = [];
  private isProcessingQueue: boolean = false;
  private connectionQuality: 'poor' | 'good' | 'excellent' = 'good';
  private lastConnectionTest: number = 0;
  private connectionTestInterval: number = 30000; // 30 seconds

  private constructor() {
    this.initializeNetworkMonitoring();
  }

  public static getInstance(): NetworkManager {
    if (!NetworkManager.instance) {
      NetworkManager.instance = new NetworkManager();
    }
    return NetworkManager.instance;
  }

  private async initializeNetworkMonitoring(): Promise<void> {
    try {
      // Temporarily disabled NetInfo functionality
      // const state = await NetInfo.fetch();
      // this.updateNetworkStatus(state);
      // NetInfo.addEventListener(this.handleNetworkStateChange.bind(this));

      // Set default connected state
      this.isConnected = true;
      this.connectionQuality = 'good';

      // Start periodic connection quality tests
      this.startConnectionQualityMonitoring();
    } catch (error) {
      errorHandler.handleError(error, 'NetworkManager.initializeNetworkMonitoring');
    }
  }

  private handleNetworkStateChange(state: any): void {
    const wasConnected = this.isConnected;
    this.updateNetworkStatus(state);

    // Determine event type
    let eventType: NetworkEventType;
    if (!wasConnected && this.isConnected) {
      eventType = 'connected';
      this.processQueuedRequests();
    } else if (wasConnected && !this.isConnected) {
      eventType = 'disconnected';
    } else if (this.connectionQuality === 'poor') {
      eventType = 'slow';
    } else {
      eventType = 'fast';
    }

    // Notify listeners
    this.notifyListeners(eventType);
  }

  private updateNetworkStatus(state: any): void {
    this.isConnected = state.isConnected ?? false;
    this.isInternetReachable = state.isInternetReachable;
    this.networkType = state.type;
  }

  private notifyListeners(eventType: NetworkEventType): void {
    const status: NetworkStatus = {
      isConnected: this.isConnected,
      isInternetReachable: this.isInternetReachable,
      type: this.networkType,
      details: {
        connectionQuality: this.connectionQuality,
        queuedRequests: this.requestQueue.length,
      },
    };

    this.listeners.forEach(listener => {
      try {
        listener(status, eventType);
      } catch (error) {
        errorHandler.handleError(error, 'NetworkManager.notifyListeners');
      }
    });
  }

  private startConnectionQualityMonitoring(): void {
    // Temporarily disabled automatic connection quality testing to prevent network errors
    // TODO: Re-enable with proper error handling and configurable endpoints
    console.log('Connection quality monitoring disabled to prevent network errors');

    // setInterval(() => {
    //   if (this.isConnected) {
    //     this.testConnectionQuality();
    //   }
    // }, this.connectionTestInterval);
  }

  private async testConnectionQuality(): Promise<void> {
    if (Date.now() - this.lastConnectionTest < this.connectionTestInterval) {
      return;
    }

    try {
      const startTime = Date.now();
      const response = await fetch('https://www.google.com/favicon.ico', {
        method: 'HEAD',
        cache: 'no-cache',
      });
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      if (response.ok) {
        if (responseTime < 500) {
          this.connectionQuality = 'excellent';
        } else if (responseTime < 1500) {
          this.connectionQuality = 'good';
        } else {
          this.connectionQuality = 'poor';
        }
      } else {
        this.connectionQuality = 'poor';
      }

      this.lastConnectionTest = Date.now();
    } catch (error) {
      this.connectionQuality = 'poor';
      errorHandler.handleError(error, 'NetworkManager.testConnectionQuality');
    }
  }

  // Public methods
  public getNetworkStatus(): NetworkStatus {
    return {
      isConnected: this.isConnected,
      isInternetReachable: this.isInternetReachable,
      type: this.networkType,
      details: {
        connectionQuality: this.connectionQuality,
        queuedRequests: this.requestQueue.length,
      },
    };
  }

  public addNetworkListener(listener: NetworkEventListener): () => void {
    this.listeners.push(listener);

    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  public async queueRequest(
    url: string,
    options: AxiosRequestConfig = {},
    priority: 'low' | 'normal' | 'high' = 'normal',
    maxRetries: number = 3
  ): Promise<string> {
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const queuedRequest: QueuedRequest = {
      id: requestId,
      url,
      options,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries,
      priority,
    };

    // Insert based on priority
    if (priority === 'high') {
      this.requestQueue.unshift(queuedRequest);
    } else {
      this.requestQueue.push(queuedRequest);
    }

    // Save queue to storage for persistence
    await this.saveQueueToStorage();

    // Process immediately if connected
    if (this.isConnected) {
      this.processQueuedRequests();
    }

    return requestId;
  }

  public removeQueuedRequest(requestId: string): void {
    this.requestQueue = this.requestQueue.filter(req => req.id !== requestId);
    this.saveQueueToStorage();
  }

  public getQueuedRequests(): QueuedRequest[] {
    return [...this.requestQueue];
  }

  public clearQueue(): void {
    this.requestQueue = [];
    this.saveQueueToStorage();
  }

  private async processQueuedRequests(): Promise<void> {
    if (this.isProcessingQueue || !this.isConnected || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    try {
      // Process requests in order of priority and timestamp
      const sortedQueue = [...this.requestQueue].sort((a, b) => {
        const priorityOrder = { high: 3, normal: 2, low: 1 };
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
        if (priorityDiff !== 0) return priorityDiff;
        return a.timestamp - b.timestamp;
      });

      for (const request of sortedQueue) {
        if (!this.isConnected) break;

        try {
          await axiosInstance.request({
            url: request.url,
            ...request.options,
          });

          // Remove successful request from queue
          this.removeQueuedRequest(request.id);
        } catch (error) {
          request.retryCount++;

          if (request.retryCount >= request.maxRetries) {
            // Remove failed request after max retries
            this.removeQueuedRequest(request.id);
            errorHandler.handleError(
              new NetworkError(`Request failed after ${request.maxRetries} retries: ${request.url}`),
              'NetworkManager.processQueuedRequests'
            );
          }
        }
      }
    } finally {
      this.isProcessingQueue = false;
      await this.saveQueueToStorage();
    }
  }

  private async saveQueueToStorage(): Promise<void> {
    try {
      await storageManager.storeData('network_request_queue', this.requestQueue);
    } catch (error) {
      errorHandler.handleError(error, 'NetworkManager.saveQueueToStorage');
    }
  }

  private async loadQueueFromStorage(): Promise<void> {
    try {
      const savedQueue = await storageManager.retrieveData<QueuedRequest[]>('network_request_queue');
      if (savedQueue && Array.isArray(savedQueue)) {
        this.requestQueue = savedQueue;
      }
    } catch (error) {
      errorHandler.handleError(error, 'NetworkManager.loadQueueFromStorage');
    }
  }

  // Network-aware axios wrapper
  public async request<T = any>(url: string, options: AxiosRequestConfig = {}): Promise<T> {
    if (!this.isConnected) {
      throw new NetworkError('No internet connection');
    }

    const config: AxiosRequestConfig = {
      url,
      ...options,
    };

    if (this.connectionQuality === 'poor') {
      // Increase timeout for poor connections
      config.timeout = 30000; // 30 seconds
    }

    const response = await axiosInstance.request<T>(config);
    return response.data;
  }

  // Initialize from storage
  public async initialize(): Promise<void> {
    await this.loadQueueFromStorage();
    if (this.isConnected) {
      this.processQueuedRequests();
    }
  }
}

// Create singleton instance
export const networkManager = NetworkManager.getInstance();

// Convenience functions
export const getNetworkStatus = () => networkManager.getNetworkStatus();
export const addNetworkListener = (listener: NetworkEventListener) =>
  networkManager.addNetworkListener(listener);
export const queueRequest = (url: string, options?: AxiosRequestConfig, priority?: 'low' | 'normal' | 'high') =>
  networkManager.queueRequest(url, options, priority);
export const networkAwareRequest = <T = any>(url: string, options?: AxiosRequestConfig) =>
  networkManager.request<T>(url, options);

// React hook for network status
export const useNetworkStatus = () => {
  const [networkStatus, setNetworkStatus] = React.useState<NetworkStatus>(getNetworkStatus());

  React.useEffect(() => {
    const unsubscribe = addNetworkListener((status) => {
      setNetworkStatus(status);
    });

    return unsubscribe;
  }, []);

  return networkStatus;
};

export default networkManager;
