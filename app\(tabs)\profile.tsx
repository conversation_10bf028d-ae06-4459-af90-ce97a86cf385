import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import React, { useEffect, useRef } from 'react';
import {
    Alert,
    Animated,
    Image,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Card from '../../components/ui/Card';
import { useAuthStore } from '../../store/authStore';
import { BORDER_RADIUS, COLORS, SPACING, TYPOGRAPHY } from '../../utils/constants';

const profileMenuItems = [
  {
    id: 'personal-info',
    title: 'Personal Information',
    subtitle: 'Update your details',
    icon: 'person-outline',
    route: '/profile/personal-info',
  },
  {
    id: 'addresses',
    title: 'Delivery Addresses',
    subtitle: 'Manage your addresses',
    icon: 'location-outline',
    route: '/profile/addresses',
  },
  {
    id: 'payment-methods',
    title: 'Payment Methods',
    subtitle: 'Manage cards and payments',
    icon: 'card-outline',
    route: '/profile/payment-methods',
  },
  {
    id: 'notifications',
    title: 'Notifications',
    subtitle: 'Manage your preferences',
    icon: 'notifications-outline',
    route: '/profile/notifications',
  },
  {
    id: 'favorites',
    title: 'Favorite Restaurants',
    subtitle: 'Your saved restaurants',
    icon: 'heart-outline',
    route: '/profile/favorites',
  },
  {
    id: 'help',
    title: 'Help & Support',
    subtitle: 'Get help and contact us',
    icon: 'help-circle-outline',
    route: '/profile/help',
  },
  {
    id: 'about',
    title: 'About',
    subtitle: 'App version and info',
    icon: 'information-circle-outline',
    route: '/profile/about',
  },
];

export default function ProfileScreen() {
  const { user, logout, isAuthenticated } = useAuthStore();

  // Modern animations
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const profileAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Initialize animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(profileAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleLogout = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            await logout();
            router.replace('/(auth)/login');
          },
        },
      ]
    );
  };

  const handleMenuItemPress = (route: string) => {
    router.push(route as any);
  };

  if (!isAuthenticated || !user) {
    return (
      <SafeAreaView style={modernStyles.container}>
        <Animated.View style={[modernStyles.authPrompt, { opacity: fadeAnim }]}>
          <View style={modernStyles.authIcon}>
            <Ionicons name="person-circle-outline" size={40} color={COLORS.textSecondary} />
          </View>
          <Text style={modernStyles.authTitle}>Sign In Required</Text>
          <Text style={modernStyles.authSubtitle}>
            Please sign in to access your profile and manage your account settings
          </Text>
          <TouchableOpacity
            style={modernStyles.authButton}
            onPress={() => router.push('/(auth)/login')}
            activeOpacity={0.8}
          >
            <Text style={modernStyles.authButtonText}>Sign In</Text>
          </TouchableOpacity>
        </Animated.View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={modernStyles.container}>
      {/* Modern Header with Gradient */}
      <Animated.View style={[modernStyles.header, {
        opacity: profileAnim,
        transform: [{ translateY: slideAnim }]
      }]}>
        <LinearGradient
          colors={[COLORS.primary, '#FF8A50']}
          style={modernStyles.headerGradient}
        >
          <Text style={modernStyles.headerTitle}>Profile</Text>
        </LinearGradient>
      </Animated.View>

      <ScrollView style={modernStyles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Modern Profile Header */}
        <Animated.View style={[modernStyles.profileCard, { opacity: fadeAnim }]}>
          <View style={modernStyles.profileInfo}>
            <View style={modernStyles.avatarContainer}>
              {user.avatar ? (
                <Image source={{ uri: user.avatar }} style={modernStyles.avatar} />
              ) : (
                <View style={modernStyles.avatarPlaceholder}>
                  <Ionicons name="person" size={32} color={COLORS.textSecondary} />
                </View>
              )}
              <TouchableOpacity style={modernStyles.editAvatarButton} activeOpacity={0.8}>
                <Ionicons name="camera" size={16} color={COLORS.white} />
              </TouchableOpacity>
            </View>

            <Text style={modernStyles.userName}>
              {user.firstName} {user.lastName}
            </Text>
            <Text style={modernStyles.userEmail}>{user.email}</Text>
            {user.phone && (
              <Text style={modernStyles.userPhone}>{user.phone}</Text>
            )}
            <View style={modernStyles.verificationBadge}>
              <Ionicons
                name={user.isVerified ? 'checkmark-circle' : 'alert-circle'}
                size={16}
                color={user.isVerified ? COLORS.success : COLORS.warning}
              />
              <Text
                style={[
                  modernStyles.verificationText,
                  { color: user.isVerified ? COLORS.success : COLORS.warning },
                ]}
              >
                {user.isVerified ? 'Verified' : 'Not Verified'}
              </Text>
            </View>
          </View>
        </Animated.View>

        {/* Quick Stats */}
        <Card style={styles.statsCard}>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>12</Text>
              <Text style={styles.statLabel}>Orders</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>5</Text>
              <Text style={styles.statLabel}>Favorites</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>4.8</Text>
              <Text style={styles.statLabel}>Rating</Text>
            </View>
          </View>
        </Card>

        {/* Modern Menu Items */}
        <Animated.View style={[modernStyles.menuSection, {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }]}>
          {profileMenuItems.map((item, index) => (
            <TouchableOpacity
              key={item.id}
              style={modernStyles.menuItem}
              onPress={() => handleMenuItemPress(item.route)}
              activeOpacity={0.8}
            >
              <View style={modernStyles.menuItemContent}>
                <View style={modernStyles.menuIcon}>
                  <Ionicons name={item.icon as any} size={20} color={COLORS.primary} />
                </View>
                <View style={modernStyles.menuItemInfo}>
                  <Text style={modernStyles.menuItemTitle}>{item.title}</Text>
                  <Text style={modernStyles.menuItemSubtitle}>{item.subtitle}</Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color={COLORS.textSecondary} />
              </View>
            </TouchableOpacity>
          ))}
        </Animated.View>

        {/* Modern Logout Button */}
        <Animated.View style={{ opacity: fadeAnim }}>
          <TouchableOpacity
            style={modernStyles.logoutButton}
            onPress={handleLogout}
            activeOpacity={0.8}
          >
            <View style={modernStyles.logoutContent}>
              <Ionicons name="log-out-outline" size={20} color="#DC2626" />
              <Text style={modernStyles.logoutText}>Sign Out</Text>
            </View>
          </TouchableOpacity>
        </Animated.View>

        {/* App Version */}
        <Text style={styles.appVersion}>FoodWay v1.0.0</Text>
      </ScrollView>
    </SafeAreaView>
  );
}

// Modern styles for profile screen
const modernStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    backgroundColor: COLORS.white,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerGradient: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.lg,
    paddingTop: SPACING.xl,
  },
  headerTitle: {
    fontSize: TYPOGRAPHY.fontSize['2xl'],
    fontWeight: '700',
    color: COLORS.white,
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  profileCard: {
    margin: SPACING.md,
    marginTop: -SPACING.lg,
    borderRadius: BORDER_RADIUS.xl,
    backgroundColor: COLORS.white,
    padding: SPACING.lg,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  profileInfo: {
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: SPACING.md,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: COLORS.white,
  },
  avatarPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 3,
    borderColor: COLORS.white,
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: COLORS.primary,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: COLORS.white,
  },
  userName: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: '700',
    color: COLORS.textPrimary,
    marginBottom: 4,
  },
  userEmail: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
    marginBottom: 4,
  },
  userPhone: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
  },
  verificationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0FDF4',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.full,
  },
  verificationText: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    fontWeight: '600',
    marginLeft: 4,
  },
  menuSection: {
    margin: SPACING.md,
  },
  menuItem: {
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.lg,
    marginBottom: SPACING.sm,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.md,
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.md,
  },
  menuItemInfo: {
    flex: 1,
  },
  menuItemTitle: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: 2,
  },
  menuItemSubtitle: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
  },
  logoutButton: {
    margin: SPACING.md,
    marginTop: SPACING.lg,
    backgroundColor: '#FEF2F2',
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
  },
  logoutContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.md,
  },
  logoutText: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
    color: '#DC2626',
    marginLeft: SPACING.sm,
  },
  authPrompt: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: SPACING.xl,
  },
  authIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.lg,
  },
  authTitle: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: '700',
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  authSubtitle: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
    lineHeight: 22,
  },
  authButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
  },
  authButtonText: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
    color: COLORS.white,
  },
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollView: {
    flex: 1,
  },
  profileHeader: {
    margin: SPACING.md,
    marginBottom: SPACING.sm,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: SPACING.md,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: BORDER_RADIUS.full,
  },
  avatarPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: COLORS.gray[200],
    alignItems: 'center',
    justifyContent: 'center',
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 28,
    height: 28,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: COLORS.primary,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: COLORS.white,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  userEmail: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  userPhone: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
  },
  verificationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  verificationText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: '500',
    marginLeft: SPACING.xs,
  },
  statsCard: {
    margin: SPACING.md,
    marginTop: 0,
    marginBottom: SPACING.sm,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: TYPOGRAPHY.fontSize['2xl'],
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: SPACING.xs,
  },
  statLabel: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: COLORS.border,
  },
  menuCard: {
    margin: SPACING.md,
    marginTop: 0,
    padding: 0,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  lastMenuItem: {
    borderBottomWidth: 0,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuItemIcon: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: COLORS.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.md,
  },
  menuItemText: {
    flex: 1,
  },
  menuItemTitle: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  menuItemSubtitle: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: SPACING.md,
    marginVertical: SPACING.lg,
    paddingVertical: SPACING.md,
    backgroundColor: COLORS.error + '10',
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 1,
    borderColor: COLORS.error + '30',
  },
  logoutText: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: '600',
    color: COLORS.error,
    marginLeft: SPACING.sm,
  },
  appVersion: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },
  authPrompt: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
  },
  authTitle: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: 'bold',
    color: COLORS.textPrimary,
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
  },
  authSubtitle: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },
  authButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
  },
  authButtonText: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: 'bold',
    color: COLORS.white,
  },
});
