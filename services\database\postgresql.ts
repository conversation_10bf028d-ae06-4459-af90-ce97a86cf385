import { Pool, PoolClient, QueryResult } from 'pg';
import { databaseConfig } from '../config/environment';

export interface DatabaseConnection {
  query<T = any>(text: string, params?: any[]): Promise<QueryResult<T>>;
  transaction<T>(callback: (client: PoolClient) => Promise<T>): Promise<T>;
  close(): Promise<void>;
}

export interface QueryOptions {
  timeout?: number;
  retries?: number;
}

class PostgreSQLService implements DatabaseConnection {
  private pool: Pool;
  private static instance: PostgreSQLService;

  private constructor() {
    this.pool = new Pool({
      connectionString: databaseConfig.url,
      host: databaseConfig.host,
      port: databaseConfig.port,
      database: databaseConfig.database,
      user: databaseConfig.username,
      password: databaseConfig.password,
      ssl: databaseConfig.ssl ? { rejectUnauthorized: false } : false,
      max: databaseConfig.maxConnections,
      idleTimeoutMillis: databaseConfig.idleTimeoutMillis,
      connectionTimeoutMillis: 10000,
      statement_timeout: 30000,
      query_timeout: 30000,
    });

    // Handle pool errors
    this.pool.on('error', (err) => {
      console.error('PostgreSQL pool error:', err);
    });

    // Handle client connection errors
    this.pool.on('connect', (client) => {
      client.on('error', (err) => {
        console.error('PostgreSQL client error:', err);
      });
    });
  }

  public static getInstance(): PostgreSQLService {
    if (!PostgreSQLService.instance) {
      PostgreSQLService.instance = new PostgreSQLService();
    }
    return PostgreSQLService.instance;
  }

  async query<T = any>(text: string, params?: any[]): Promise<QueryResult<T>> {
    const client = await this.pool.connect();
    try {
      const result = await client.query<T>(text, params);
      return result;
    } catch (error) {
      console.error('Database query error:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  async transaction<T>(callback: (client: PoolClient) => Promise<T>): Promise<T> {
    const client = await this.pool.connect();
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Database transaction error:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  async healthCheck(): Promise<{ status: 'connected' | 'disconnected'; latency?: number }> {
    try {
      const start = Date.now();
      await this.query('SELECT 1');
      const latency = Date.now() - start;
      return { status: 'connected', latency };
    } catch (error) {
      console.error('Database health check failed:', error);
      return { status: 'disconnected' };
    }
  }

  async close(): Promise<void> {
    await this.pool.end();
  }

  // Utility methods for common operations
  async findById<T = any>(table: string, id: string | number): Promise<T | null> {
    const result = await this.query<T>(
      `SELECT * FROM ${table} WHERE id = $1`,
      [id]
    );
    return result.rows[0] || null;
  }

  async findMany<T = any>(
    table: string,
    conditions?: Record<string, any>,
    options?: { limit?: number; offset?: number; orderBy?: string }
  ): Promise<T[]> {
    let query = `SELECT * FROM ${table}`;
    const params: any[] = [];
    let paramIndex = 1;

    if (conditions && Object.keys(conditions).length > 0) {
      const whereClause = Object.keys(conditions)
        .map(key => `${key} = $${paramIndex++}`)
        .join(' AND ');
      query += ` WHERE ${whereClause}`;
      params.push(...Object.values(conditions));
    }

    if (options?.orderBy) {
      query += ` ORDER BY ${options.orderBy}`;
    }

    if (options?.limit) {
      query += ` LIMIT $${paramIndex++}`;
      params.push(options.limit);
    }

    if (options?.offset) {
      query += ` OFFSET $${paramIndex++}`;
      params.push(options.offset);
    }

    const result = await this.query<T>(query, params);
    return result.rows;
  }

  async create<T = any>(
    table: string,
    data: Record<string, any>
  ): Promise<T> {
    const keys = Object.keys(data);
    const values = Object.values(data);
    const placeholders = keys.map((_, index) => `$${index + 1}`).join(', ');
    const columns = keys.join(', ');

    const query = `
      INSERT INTO ${table} (${columns})
      VALUES (${placeholders})
      RETURNING *
    `;

    const result = await this.query<T>(query, values);
    return result.rows[0];
  }

  async update<T = any>(
    table: string,
    id: string | number,
    data: Record<string, any>
  ): Promise<T | null> {
    const keys = Object.keys(data);
    const values = Object.values(data);
    const setClause = keys.map((key, index) => `${key} = $${index + 1}`).join(', ');

    const query = `
      UPDATE ${table}
      SET ${setClause}
      WHERE id = $${keys.length + 1}
      RETURNING *
    `;

    const result = await this.query<T>(query, [...values, id]);
    return result.rows[0] || null;
  }

  async delete(table: string, id: string | number): Promise<boolean> {
    const result = await this.query(
      `DELETE FROM ${table} WHERE id = $1`,
      [id]
    );
    return result.rowCount !== null && result.rowCount > 0;
  }

  async count(table: string, conditions?: Record<string, any>): Promise<number> {
    let query = `SELECT COUNT(*) as count FROM ${table}`;
    const params: any[] = [];
    let paramIndex = 1;

    if (conditions && Object.keys(conditions).length > 0) {
      const whereClause = Object.keys(conditions)
        .map(key => `${key} = $${paramIndex++}`)
        .join(' AND ');
      query += ` WHERE ${whereClause}`;
      params.push(...Object.values(conditions));
    }

    const result = await this.query<{ count: string }>(query, params);
    return parseInt(result.rows[0].count, 10);
  }

  async exists(table: string, conditions: Record<string, any>): Promise<boolean> {
    const count = await this.count(table, conditions);
    return count > 0;
  }

  // Migration utilities
  async createTable(tableName: string, schema: string): Promise<void> {
    await this.query(`CREATE TABLE IF NOT EXISTS ${tableName} (${schema})`);
  }

  async dropTable(tableName: string): Promise<void> {
    await this.query(`DROP TABLE IF EXISTS ${tableName}`);
  }

  async addColumn(tableName: string, columnName: string, columnType: string): Promise<void> {
    await this.query(`ALTER TABLE ${tableName} ADD COLUMN IF NOT EXISTS ${columnName} ${columnType}`);
  }

  async dropColumn(tableName: string, columnName: string): Promise<void> {
    await this.query(`ALTER TABLE ${tableName} DROP COLUMN IF EXISTS ${columnName}`);
  }
}

// Export singleton instance
export const db = PostgreSQLService.getInstance();

// Export types
export type { QueryResult, PoolClient };
