import AsyncStorage from '@react-native-async-storage/async-storage';
import { performanceMonitor } from '../utils/performanceMonitor';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  cache?: boolean;
  cacheTTL?: number; // Time to live in milliseconds
  retries?: number;
  timeout?: number;
}

class OptimizedApiService {
  private cache: Map<string, CacheEntry<any>> = new Map();
  private requestQueue: Map<string, Promise<any>> = new Map();
  private baseURL: string;
  private defaultTimeout: number = 10000; // 10 seconds

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.loadCacheFromStorage();
  }

  /**
   * Make an optimized API request with caching and deduplication
   */
  async request<T>(endpoint: string, config: RequestConfig = {}): Promise<T> {
    const {
      method = 'GET',
      headers = {},
      body,
      cache = method === 'GET',
      cacheTTL = 5 * 60 * 1000, // 5 minutes default
      retries = 3,
      timeout = this.defaultTimeout,
    } = config;

    const cacheKey = this.getCacheKey(endpoint, method, body);
    const url = `${this.baseURL}${endpoint}`;

    // Check cache first for GET requests
    if (cache && method === 'GET') {
      const cachedData = this.getFromCache<T>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    }

    // Deduplicate identical requests
    if (this.requestQueue.has(cacheKey)) {
      return this.requestQueue.get(cacheKey);
    }

    // Start performance monitoring
    const apiTimer = performanceMonitor.measureApiCall(endpoint, method, {
      cacheEnabled: cache,
      cacheTTL,
    });
    apiTimer.start();

    // Create the request promise
    const requestPromise = this.executeRequest<T>(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
      body: body ? JSON.stringify(body) : undefined,
      timeout,
      retries,
    });

    // Add to request queue
    this.requestQueue.set(cacheKey, requestPromise);

    try {
      const result = await requestPromise;

      // Cache successful GET requests
      if (cache && method === 'GET') {
        this.setCache(cacheKey, result, cacheTTL);
      }

      apiTimer.end();
      return result;
    } catch (error) {
      apiTimer.end();
      throw error;
    } finally {
      // Remove from request queue
      this.requestQueue.delete(cacheKey);
    }
  }

  /**
   * Execute the actual HTTP request with retry logic
   */
  private async executeRequest<T>(
    url: string,
    config: {
      method: string;
      headers: Record<string, string>;
      body?: string;
      timeout: number;
      retries: number;
    }
  ): Promise<T> {
    const { retries, timeout, ...fetchConfig } = config;
    let lastError: Error;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(url, {
          ...fetchConfig,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        return data;
      } catch (error) {
        lastError = error as Error;
        
        // Don't retry on certain errors
        if (error instanceof Error && (
          error.name === 'AbortError' ||
          error.message.includes('400') ||
          error.message.includes('401') ||
          error.message.includes('403')
        )) {
          break;
        }

        // Wait before retrying (exponential backoff)
        if (attempt < retries) {
          const delay = Math.pow(2, attempt) * 1000; // 1s, 2s, 4s, etc.
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError!;
  }

  /**
   * Batch multiple requests
   */
  async batchRequests<T>(requests: Array<{ endpoint: string; config?: RequestConfig }>): Promise<T[]> {
    const batchTimer = performanceMonitor.measureApiCall('batch_requests', 'BATCH', {
      requestCount: requests.length,
    });
    batchTimer.start();

    try {
      const promises = requests.map(({ endpoint, config }) => 
        this.request<T>(endpoint, config)
      );
      
      const results = await Promise.all(promises);
      batchTimer.end();
      return results;
    } catch (error) {
      batchTimer.end();
      throw error;
    }
  }

  /**
   * Generate cache key
   */
  private getCacheKey(endpoint: string, method: string, body?: any): string {
    const bodyHash = body ? JSON.stringify(body) : '';
    return `${method}_${endpoint}_${bodyHash}`;
  }

  /**
   * Get data from cache
   */
  private getFromCache<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    const now = Date.now();
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * Set data in cache
   */
  private setCache<T>(key: string, data: T, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });

    // Persist to AsyncStorage for important data
    this.persistCacheToStorage();
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
    AsyncStorage.removeItem('api_cache');
  }

  /**
   * Load cache from AsyncStorage
   */
  private async loadCacheFromStorage(): Promise<void> {
    try {
      const cacheData = await AsyncStorage.getItem('api_cache');
      if (cacheData) {
        const parsedCache = JSON.parse(cacheData);
        Object.entries(parsedCache).forEach(([key, value]) => {
          this.cache.set(key, value as CacheEntry<any>);
        });
      }
    } catch (error) {
      console.warn('Failed to load cache from storage:', error);
    }
  }

  /**
   * Persist cache to AsyncStorage
   */
  private async persistCacheToStorage(): Promise<void> {
    try {
      const cacheObject = Object.fromEntries(this.cache.entries());
      await AsyncStorage.setItem('api_cache', JSON.stringify(cacheObject));
    } catch (error) {
      console.warn('Failed to persist cache to storage:', error);
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }

  /**
   * Prefetch data for better UX
   */
  async prefetch(endpoints: string[], config?: RequestConfig): Promise<void> {
    const prefetchTimer = performanceMonitor.measureApiCall('prefetch', 'PREFETCH', {
      endpointCount: endpoints.length,
    });
    prefetchTimer.start();

    try {
      const requests = endpoints.map(endpoint => ({ endpoint, config }));
      await this.batchRequests(requests);
      prefetchTimer.end();
    } catch (error) {
      prefetchTimer.end();
      console.warn('Prefetch failed:', error);
    }
  }
}

// Create optimized API service instance
export const optimizedApiService = new OptimizedApiService(
  process.env.EXPO_PUBLIC_API_URL || 'https://api.foodway.com'
);

export default optimizedApiService;
