import { ERROR_MESSAGES, ERROR_TYPES } from '../../utils/constants';
import {
    AppError,
    AuthenticationError,
    errorHandler,
    NetworkError,
    PaymentError,
    ValidationError,
    withRetry
} from '../../utils/errorHandler';

// Mock <PERSON> for testing
jest.mock('react-native', () => ({
  Alert: {
    alert: jest.fn(),
  },
}));

describe('ErrorHandler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    errorHandler.clearErrorLogs();
  });

  describe('AppError', () => {
    it('should create an AppError with correct properties', () => {
      const error = new AppError(
        ERROR_TYPES.NETWORK_ERROR,
        'Test network error',
        'User friendly message',
        'NET_001',
        500,
        true
      );

      expect(error.type).toBe(ERROR_TYPES.NETWORK_ERROR);
      expect(error.message).toBe('Test network error');
      expect(error.userMessage).toBe('User friendly message');
      expect(error.code).toBe('NET_001');
      expect(error.statusCode).toBe(500);
      expect(error.isRetryable).toBe(true);
    });

    it('should use default user message if not provided', () => {
      const error = new AppError(ERROR_TYPES.NETWORK_ERROR, 'Test error');
      expect(error.userMessage).toBe(ERROR_MESSAGES[ERROR_TYPES.NETWORK_ERROR]);
    });
  });

  describe('NetworkError', () => {
    it('should create a NetworkError with correct properties', () => {
      const error = new NetworkError('Connection failed', 503);
      
      expect(error.type).toBe(ERROR_TYPES.NETWORK_ERROR);
      expect(error.message).toBe('Connection failed');
      expect(error.statusCode).toBe(503);
      expect(error.isRetryable).toBe(true);
    });
  });

  describe('AuthenticationError', () => {
    it('should create an AuthenticationError with correct properties', () => {
      const error = new AuthenticationError('Invalid token', 'AUTH_001');
      
      expect(error.type).toBe(ERROR_TYPES.AUTH_ERROR);
      expect(error.message).toBe('Invalid token');
      expect(error.code).toBe('AUTH_001');
      expect(error.statusCode).toBe(401);
      expect(error.isRetryable).toBe(false);
    });
  });

  describe('ValidationError', () => {
    it('should create a ValidationError with validation errors', () => {
      const validationErrors = { email: 'Invalid email', password: 'Too short' };
      const error = new ValidationError('Validation failed', 'email', validationErrors);
      
      expect(error.type).toBe(ERROR_TYPES.VALIDATION_ERROR);
      expect(error.field).toBe('email');
      expect(error.validationErrors).toEqual(validationErrors);
      expect(error.isRetryable).toBe(false);
    });
  });

  describe('PaymentError', () => {
    it('should create a PaymentError with correct properties', () => {
      const error = new PaymentError('Card declined', 'CARD_DECLINED');
      
      expect(error.type).toBe(ERROR_TYPES.PAYMENT_ERROR);
      expect(error.message).toBe('Card declined');
      expect(error.code).toBe('CARD_DECLINED');
      expect(error.statusCode).toBe(402);
    });
  });

  describe('ErrorHandler.handleError', () => {
    it('should handle AppError instances', () => {
      const originalError = new NetworkError('Test error');
      const handledError = errorHandler.handleError(originalError);
      
      expect(handledError).toBe(originalError);
    });

    it('should convert regular Error to AppError', () => {
      const originalError = new Error('Regular error');
      const handledError = errorHandler.handleError(originalError);
      
      expect(handledError).toBeInstanceOf(AppError);
      expect(handledError.message).toBe('Regular error');
    });

    it('should handle network errors', () => {
      const originalError = new Error('fetch failed');
      const handledError = errorHandler.handleError(originalError);
      
      expect(handledError).toBeInstanceOf(NetworkError);
    });

    it('should handle timeout errors', () => {
      const originalError = new Error('timeout exceeded');
      const handledError = errorHandler.handleError(originalError);
      
      expect(handledError.type).toBe(ERROR_TYPES.TIMEOUT_ERROR);
      expect(handledError.isRetryable).toBe(true);
    });

    it('should handle unknown errors', () => {
      const handledError = errorHandler.handleError('string error');
      
      expect(handledError.type).toBe(ERROR_TYPES.UNKNOWN_ERROR);
    });

    it('should log errors', () => {
      const error = new Error('Test error');
      errorHandler.handleError(error, 'test context');
      
      const logs = errorHandler.getErrorLogs();
      expect(logs).toHaveLength(1);
    });
  });

  describe('withRetry', () => {
    it('should succeed on first attempt', async () => {
      const operation = jest.fn().mockResolvedValue('success');
      
      const result = await withRetry(operation, 3, 100);
      
      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should retry on retryable errors', async () => {
      const operation = jest.fn()
        .mockRejectedValueOnce(new NetworkError('Network error'))
        .mockRejectedValueOnce(new NetworkError('Network error'))
        .mockResolvedValue('success');
      
      const result = await withRetry(operation, 3, 10);
      
      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(3);
    });

    it('should not retry on non-retryable errors', async () => {
      const operation = jest.fn()
        .mockRejectedValue(new ValidationError('Validation error'));
      
      await expect(withRetry(operation, 3, 10)).rejects.toThrow('Validation error');
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should throw after max retries', async () => {
      const operation = jest.fn()
        .mockRejectedValue(new NetworkError('Network error'));
      
      await expect(withRetry(operation, 2, 10)).rejects.toThrow('Network error');
      expect(operation).toHaveBeenCalledTimes(3); // Initial + 2 retries
    });

    it('should use exponential backoff', async () => {
      const operation = jest.fn()
        .mockRejectedValueOnce(new NetworkError('Network error'))
        .mockResolvedValue('success');
      
      const startTime = Date.now();
      await withRetry(operation, 2, 100);
      const endTime = Date.now();
      
      // Should have waited at least 100ms for the first retry
      expect(endTime - startTime).toBeGreaterThanOrEqual(100);
    });
  });

  describe('Error Display Integration', () => {
    it('should show error with retry option for retryable errors', () => {
      const retryFn = jest.fn();
      const error = new NetworkError('Network error');
      
      errorHandler.showErrorWithRetry(error, retryFn, 'Test Error');
      
      // Verify Alert.alert was called with retry option
      const { Alert } = require('react-native');
      expect(Alert.alert).toHaveBeenCalledWith(
        'Test Error',
        error.userMessage,
        expect.arrayContaining([
          expect.objectContaining({ text: 'Retry' }),
          expect.objectContaining({ text: 'Cancel' })
        ])
      );
    });

    it('should show error without retry option for non-retryable errors', () => {
      const retryFn = jest.fn();
      const error = new ValidationError('Validation error');
      
      errorHandler.showErrorWithRetry(error, retryFn, 'Test Error');
      
      const { Alert } = require('react-native');
      expect(Alert.alert).toHaveBeenCalledWith(
        'Test Error',
        error.userMessage,
        expect.arrayContaining([
          expect.objectContaining({ text: 'Cancel' })
        ])
      );
    });
  });

  describe('Error Logging', () => {
    it('should maintain error log history', () => {
      const error1 = new Error('First error');
      const error2 = new Error('Second error');
      
      errorHandler.handleError(error1, 'context1');
      errorHandler.handleError(error2, 'context2');
      
      const logs = errorHandler.getErrorLogs();
      expect(logs).toHaveLength(2);
      expect(logs[0].message).toBe('First error');
      expect(logs[1].message).toBe('Second error');
    });

    it('should limit error log size', () => {
      // Add more than 100 errors
      for (let i = 0; i < 105; i++) {
        errorHandler.handleError(new Error(`Error ${i}`), `context${i}`);
      }
      
      const logs = errorHandler.getErrorLogs();
      expect(logs).toHaveLength(100);
      expect(logs[0].message).toBe('Error 5'); // First 5 should be removed
    });

    it('should clear error logs', () => {
      errorHandler.handleError(new Error('Test error'), 'context');
      expect(errorHandler.getErrorLogs()).toHaveLength(1);
      
      errorHandler.clearErrorLogs();
      expect(errorHandler.getErrorLogs()).toHaveLength(0);
    });
  });

  describe('Error Context Parsing', () => {
    it('should parse error context correctly', () => {
      const error = new Error('Test error');
      const handledError = errorHandler.handleError(error, 'LoginScreen.handleSubmit');
      
      // Verify context is stored (implementation detail may vary)
      expect(handledError).toBeInstanceOf(AppError);
    });
  });

  describe('Error Recovery', () => {
    it('should handle errors during error handling gracefully', () => {
      // Mock console.error to prevent test output pollution
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // This should not throw even if internal error handling fails
      expect(() => {
        errorHandler.handleError(null as any);
      }).not.toThrow();

      consoleSpy.mockRestore();
    });
  });

  describe('Performance', () => {
    it('should handle error processing efficiently', () => {
      const startTime = Date.now();

      // Process multiple errors
      for (let i = 0; i < 50; i++) {
        errorHandler.handleError(new Error(`Error ${i}`), `context${i}`);
      }

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      // Should process 50 errors in less than 1 second
      expect(processingTime).toBeLessThan(1000);
    });
  });
});
