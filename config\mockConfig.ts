// Mock configuration for the app
export const MOCK_CONFIG = {
  // Enable/disable mock mode globally
  ENABLED: true,
  
  // Mock API settings
  API: {
    SIMULATE_NETWORK_DELAY: true,
    MIN_DELAY: 300,
    MAX_DELAY: 1200,
    FAILURE_RATE: 0.02, // 2% chance of simulated failures
  },
  
  // Mock data settings
  DATA: {
    // Default user for quick testing
    DEFAULT_USER_EMAIL: '<EMAIL>',
    DEFAULT_USER_PASSWORD: 'password123',
    
    // Test credentials for easy login
    TEST_CREDENTIALS: [
      { email: '<EMAIL>', password: 'TestPassword123!' },
      { email: '<EMAIL>', password: 'demo123' },
      { email: '<EMAIL>', password: 'password123' },
      { email: '<EMAIL>', password: 'password123' },
    ],
  },
  
  // Feature flags for mock mode
  FEATURES: {
    // Enable mock order tracking simulation
    SIMULATE_ORDER_PROGRESS: true,
    
    // Enable mock push notifications
    SIMULATE_NOTIFICATIONS: true,
    
    // Enable mock location services
    SIMULATE_LOCATION: true,
    
    // Enable mock payment processing
    SIMULATE_PAYMENTS: true,
  },
  
  // Development helpers
  DEV: {
    // Show mock mode indicator in UI
    SHOW_MOCK_INDICATOR: __DEV__,
    
    // Log mock API calls
    LOG_API_CALLS: __DEV__,
    
    // Enable debug features
    ENABLE_DEBUG_FEATURES: __DEV__,
  },
};

// Helper functions
export const isMockEnabled = () => MOCK_CONFIG.ENABLED;

export const getMockCredentials = () => MOCK_CONFIG.DATA.TEST_CREDENTIALS;

export const getDefaultCredentials = () => ({
  email: MOCK_CONFIG.DATA.DEFAULT_USER_EMAIL,
  password: MOCK_CONFIG.DATA.DEFAULT_USER_PASSWORD,
});

export const shouldSimulateNetworkDelay = () => 
  MOCK_CONFIG.ENABLED && MOCK_CONFIG.API.SIMULATE_NETWORK_DELAY;

export const shouldShowMockIndicator = () => 
  MOCK_CONFIG.ENABLED && MOCK_CONFIG.DEV.SHOW_MOCK_INDICATOR;

export const shouldLogApiCalls = () => 
  MOCK_CONFIG.ENABLED && MOCK_CONFIG.DEV.LOG_API_CALLS;

// Update mock configuration at runtime
export const updateMockConfig = (updates: Partial<typeof MOCK_CONFIG>) => {
  Object.assign(MOCK_CONFIG, updates);
};

// Toggle mock mode
export const toggleMockMode = () => {
  MOCK_CONFIG.ENABLED = !MOCK_CONFIG.ENABLED;
  return MOCK_CONFIG.ENABLED;
};

// Reset to default configuration
export const resetMockConfig = () => {
  MOCK_CONFIG.ENABLED = true;
  MOCK_CONFIG.API.SIMULATE_NETWORK_DELAY = true;
  MOCK_CONFIG.API.MIN_DELAY = 300;
  MOCK_CONFIG.API.MAX_DELAY = 1200;
  MOCK_CONFIG.API.FAILURE_RATE = 0.02;
};

export default MOCK_CONFIG;
