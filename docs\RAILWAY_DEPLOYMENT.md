# Railway Deployment Guide for FoodWay Customer App

This guide covers deploying the FoodWay Customer App to Railway with PostgreSQL and Redis integration.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Initial Setup](#initial-setup)
3. [Environment Configuration](#environment-configuration)
4. [Database Setup](#database-setup)
5. [Deployment](#deployment)
6. [Post-Deployment](#post-deployment)
7. [Monitoring and Maintenance](#monitoring-and-maintenance)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

Before deploying to Railway, ensure you have:

- Node.js 18+ installed
- Railway CLI installed: `npm install -g @railway/cli`
- Railway account (sign up at [railway.app](https://railway.app))
- Git repository with your code
- Required API keys for external services

## Initial Setup

### 1. Install Railway CLI

```bash
npm install -g @railway/cli
```

### 2. Login to Railway

```bash
railway login
```

### 3. Run Setup Script

We've provided an automated setup script:

```bash
node scripts/railway-setup.js
```

This script will:
- Create or link to a Railway project
- Add PostgreSQL and Redis services
- Set up basic environment variables
- Guide you through API key configuration

### 4. Manual Setup (Alternative)

If you prefer manual setup:

```bash
# Create new project
railway init foodway-customer-app

# Or link to existing project
railway link

# Add services
railway add postgresql
railway add redis
```

## Environment Configuration

### Required Environment Variables

Set these variables using `railway variables set KEY=value`:

#### Application Settings
```bash
NODE_ENV=production
PORT=3000
JWT_SECRET=your_jwt_secret_here
REFRESH_TOKEN_SECRET=your_refresh_token_secret
SESSION_SECRET=your_session_secret
```

#### Database & Cache
```bash
# These are automatically set by Railway services
DATABASE_URL=postgresql://...
REDIS_URL=redis://...
```

#### Payment Integration
```bash
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

#### External Services
```bash
GOOGLE_MAPS_API_KEY=your_google_maps_key
FIREBASE_SERVER_KEY=your_firebase_key
EMAIL_SERVICE_API_KEY=your_email_service_key
CLOUDINARY_CLOUD_NAME=your_cloudinary_name
CLOUDINARY_API_KEY=your_cloudinary_key
CLOUDINARY_API_SECRET=your_cloudinary_secret
```

#### Security & Performance
```bash
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGIN=https://your-frontend-domain.com
CORS_CREDENTIALS=true
CACHE_TTL=3600
```

### Environment Files

For local development, copy `.env.example` to `.env.local`:

```bash
cp .env.example .env.local
```

Edit `.env.local` with your local development values.

## Database Setup

### PostgreSQL Configuration

Railway automatically provisions PostgreSQL with:
- Latest PostgreSQL version
- Automatic backups
- Connection pooling
- SSL encryption

### Redis Configuration

Railway automatically provisions Redis with:
- Latest Redis version
- Persistence enabled
- Memory optimization
- SSL encryption

### Database Migrations

Run migrations after deployment:

```bash
# Using Railway CLI
railway run node -e "require('./services/database/index.js').DatabaseMigrations.runAllMigrations()"

# Or using the deployment script
node scripts/railway-deploy.js
```

### Database Schema

The app creates these tables:
- `users` - User accounts and profiles
- `user_addresses` - User delivery addresses
- `user_payment_methods` - Saved payment methods
- `restaurants` - Restaurant information
- `menu_categories` - Menu organization
- `menu_items` - Food items and details
- `orders` - Order records
- `order_items` - Order line items
- `order_tracking` - Delivery tracking
- `reviews` - Restaurant and order reviews
- `notifications` - Push notifications
- `push_tokens` - Device tokens for notifications

## Deployment

### Automated Deployment

Use our deployment script:

```bash
# Full deployment with migrations
npm run railway:deploy

# Or run the script directly
node scripts/railway-deploy.js

# Skip migrations
node scripts/railway-deploy.js --skip-migrations

# Skip build step
node scripts/railway-deploy.js --skip-build
```

### Manual Deployment

```bash
# Deploy to Railway
railway up

# Check deployment status
railway status

# View logs
railway logs
```

### Deployment Configuration

The deployment uses these files:
- `railway.json` - Railway service configuration
- `railway.toml` - Alternative configuration format
- `Dockerfile` - Container configuration
- `.dockerignore` - Files to exclude from build

### Build Process

1. Install dependencies: `npm ci`
2. Run type checking: `npm run type-check`
3. Run tests: `npm test`
4. Build application
5. Deploy to Railway
6. Run database migrations
7. Health check verification

## Post-Deployment

### Verify Deployment

1. **Check deployment status:**
   ```bash
   railway status
   ```

2. **View application logs:**
   ```bash
   railway logs
   ```

3. **Open application:**
   ```bash
   railway open
   ```

4. **Run health check:**
   ```bash
   node scripts/health-check.js https://your-app.railway.app
   ```

### Database Connection

Connect to your databases:

```bash
# PostgreSQL
railway connect postgresql

# Redis
railway connect redis
```

### Environment Variables

View and manage environment variables:

```bash
# List all variables
railway variables

# Set a variable
railway variables set KEY=value

# Delete a variable
railway variables delete KEY
```

## Monitoring and Maintenance

### Health Monitoring

The app includes built-in health checks:

- **Endpoint:** `GET /health`
- **Database connectivity check**
- **Redis connectivity check**
- **Response time monitoring**

### Logging

View application logs:

```bash
# Real-time logs
railway logs --follow

# Filter logs
railway logs --filter error

# Export logs
railway logs --output logs.txt
```

### Performance Monitoring

Monitor your application:

1. **Railway Dashboard:** View metrics in Railway console
2. **Database Performance:** Monitor query performance
3. **Redis Performance:** Monitor cache hit rates
4. **API Response Times:** Monitor endpoint performance

### Scaling

Scale your application:

```bash
# Scale up resources
railway up --scale

# View current scaling
railway status
```

### Backups

Railway automatically handles:
- **Database backups:** Daily automated backups
- **Point-in-time recovery:** Available for PostgreSQL
- **Redis persistence:** RDB and AOF persistence

## Troubleshooting

### Common Issues

#### 1. Deployment Fails

```bash
# Check build logs
railway logs --filter build

# Verify environment variables
railway variables

# Check service status
railway status
```

#### 2. Database Connection Issues

```bash
# Test database connection
railway connect postgresql

# Check database logs
railway logs --service postgresql

# Verify DATABASE_URL
railway variables get DATABASE_URL
```

#### 3. Redis Connection Issues

```bash
# Test Redis connection
railway connect redis

# Check Redis logs
railway logs --service redis

# Verify REDIS_URL
railway variables get REDIS_URL
```

#### 4. Environment Variable Issues

```bash
# List all variables
railway variables

# Check specific variable
railway variables get VARIABLE_NAME

# Set missing variable
railway variables set VARIABLE_NAME=value
```

### Debug Commands

```bash
# Connect to application shell
railway shell

# Run database migrations manually
railway run node -e "require('./services/database/index.js').DatabaseMigrations.runAllMigrations()"

# Check application health
railway run node scripts/health-check.js

# View environment in production
railway run env
```

### Getting Help

1. **Railway Documentation:** [docs.railway.app](https://docs.railway.app)
2. **Railway Discord:** [discord.gg/railway](https://discord.gg/railway)
3. **Railway Support:** [railway.app/help](https://railway.app/help)
4. **Project Issues:** Check application logs and health endpoints

### Performance Optimization

1. **Database Optimization:**
   - Use connection pooling
   - Optimize queries with indexes
   - Monitor slow queries

2. **Redis Optimization:**
   - Set appropriate TTL values
   - Use Redis for session storage
   - Implement cache invalidation

3. **Application Optimization:**
   - Enable gzip compression
   - Use CDN for static assets
   - Implement rate limiting

## Next Steps

After successful deployment:

1. Set up monitoring and alerting
2. Configure custom domain
3. Set up CI/CD pipeline
4. Implement backup strategies
5. Plan scaling strategy
6. Set up staging environment

For more detailed information, refer to the [Railway Documentation](https://docs.railway.app) and the project's API documentation.
