import { useAuthStore } from '../../store/authStore';
import { API_CONFIG, RETRY_CONFIG } from '../../utils/constants';
import {
    errorHandler,
    NetworkError,
    withRetry
} from '../../utils/errorHandler';
import { axiosInstance, AxiosRequestConfig } from '../config/axios';

// Network status detection
let isOnline = true;
let networkListeners: (() => void)[] = [];

// Simple network status detection for React Native
if (typeof window !== 'undefined' && window.addEventListener) {
  window.addEventListener('online', () => {
    isOnline = true;
    networkListeners.forEach(listener => listener());
  });

  window.addEventListener('offline', () => {
    isOnline = false;
  });
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  timestamp?: string;
}

export interface ApiRequestConfig extends AxiosRequestConfig {
  skipAuth?: boolean;
  skipRetry?: boolean;
  retries?: number;
}

class EnhancedApiClient {
  private baseURL: string;
  private defaultTimeout: number;
  private requestQueue: Array<() => Promise<any>> = [];
  private isProcessingQueue = false;

  constructor() {
    this.baseURL = process.env.EXPO_PUBLIC_API_URL || API_CONFIG.BASE_URL;
    this.defaultTimeout = API_CONFIG.TIMEOUT;
  }

  private async getAuthToken(): Promise<string | null> {
    try {
      return await useAuthStore.getState().getToken();
    } catch (error) {
      errorHandler.handleError(error, 'EnhancedApiClient.getAuthToken');
      return null;
    }
  }

  private async makeRequest<T>(
    endpoint: string,
    config: ApiRequestConfig = {}
  ): Promise<T> {
    // Check network connectivity
    if (!isOnline) {
      throw new NetworkError('No internet connection');
    }

    const {
      skipAuth = false,
      retries,
      skipRetry,
      ...requestConfig
    } = config;

    try {
      const response = await axiosInstance.request<T>({
        url: endpoint,
        timeout: config.timeout || this.defaultTimeout,
        headers: {
          ...(skipAuth && { skipAuth: true }),
          ...config.headers,
        },
        ...requestConfig,
      });

      return response.data;
    } catch (error) {
      // Axios interceptors will handle error transformation
      throw error;
    }
  }

  private async requestWithRetry<T>(
    endpoint: string,
    config: ApiRequestConfig = {}
  ): Promise<T> {
    const { skipRetry = false, retries = RETRY_CONFIG.MAX_RETRIES } = config;

    if (skipRetry) {
      return this.makeRequest<T>(endpoint, config);
    }

    return withRetry(
      () => this.makeRequest<T>(endpoint, config),
      retries,
      RETRY_CONFIG.RETRY_DELAY
    );
  }

  // Queue requests when offline
  private async queueRequest<T>(requestFn: () => Promise<T>): Promise<T> {
    if (isOnline) {
      return requestFn();
    }

    return new Promise((resolve, reject) => {
      this.requestQueue.push(async () => {
        try {
          const result = await requestFn();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      // Process queue when back online
      const onlineListener = () => {
        this.processQueue();
        networkListeners = networkListeners.filter(l => l !== onlineListener);
      };
      networkListeners.push(onlineListener);
    });
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.requestQueue.length > 0 && isOnline) {
      const request = this.requestQueue.shift();
      if (request) {
        try {
          await request();
        } catch (error) {
          errorHandler.handleError(error, 'EnhancedApiClient.processQueue');
        }
      }
    }

    this.isProcessingQueue = false;
  }

  // Public HTTP methods
  async get<T>(endpoint: string, params?: Record<string, any>, config?: ApiRequestConfig): Promise<T> {
    let url = endpoint;

    if (params) {
      const searchParams = new URLSearchParams();
      Object.keys(params).forEach(key => {
        if (params[key] !== undefined && params[key] !== null) {
          searchParams.append(key, String(params[key]));
        }
      });
      url += `?${searchParams.toString()}`;
    }

    return this.queueRequest(() => this.requestWithRetry<T>(url, { ...config, method: 'GET' }));
  }

  async post<T>(endpoint: string, data?: any, config?: ApiRequestConfig): Promise<T> {
    return this.queueRequest(() => this.requestWithRetry<T>(endpoint, {
      ...config,
      method: 'POST',
      data,
    }));
  }

  async put<T>(endpoint: string, data?: any, config?: ApiRequestConfig): Promise<T> {
    return this.queueRequest(() => this.requestWithRetry<T>(endpoint, {
      ...config,
      method: 'PUT',
      data,
    }));
  }

  async patch<T>(endpoint: string, data?: any, config?: ApiRequestConfig): Promise<T> {
    return this.queueRequest(() => this.requestWithRetry<T>(endpoint, {
      ...config,
      method: 'PATCH',
      data,
    }));
  }

  async delete<T>(endpoint: string, config?: ApiRequestConfig): Promise<T> {
    return this.queueRequest(() => this.requestWithRetry<T>(endpoint, {
      ...config,
      method: 'DELETE',
    }));
  }

  // File upload with progress
  async uploadFile<T>(
    endpoint: string,
    file: File | Blob,
    additionalData?: Record<string, any>,
    onProgress?: (progress: number) => void
  ): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);

    if (additionalData) {
      Object.keys(additionalData).forEach(key => {
        formData.append(key, String(additionalData[key]));
      });
    }

    return this.queueRequest(() => this.requestWithRetry<T>(endpoint, {
      method: 'POST',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }));
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await this.get('/health', undefined, { skipAuth: true, timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }

  // Get network status
  getNetworkStatus(): boolean {
    return isOnline;
  }

  // Clear request queue
  clearQueue(): void {
    this.requestQueue = [];
  }
}

// Create and export singleton instance
export const enhancedApiClient = new EnhancedApiClient();
export default enhancedApiClient;
