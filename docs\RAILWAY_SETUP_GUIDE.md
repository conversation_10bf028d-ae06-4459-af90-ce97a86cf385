# FoodWay Customer App - Railway Deployment Guide

## Overview
This guide provides step-by-step instructions for deploying the FoodWay backend API to Railway with PostgreSQL and Redis services.

## Prerequisites
- Railway account (sign up at [railway.app](https://railway.app))
- Railway CLI installed
- Git repository with your backend code
- Stripe account for payments
- Cloudinary account for image uploads

## 1. Railway CLI Installation

### Install Railway CLI
```bash
# Using npm
npm install -g @railway/cli

# Using curl (Linux/macOS)
curl -fsSL https://railway.app/install.sh | sh

# Using PowerShell (Windows)
iwr https://railway.app/install.ps1 | iex
```

### Login to Railway
```bash
railway login
```

## 2. Project Setup

### Create New Railway Project
```bash
# Create new project
railway init foodway-backend

# Or connect to existing project
railway link [project-id]
```

### Project Structure for Railway
```
backend/
├── src/                 # Application source code
├── migrations/          # Database migrations
├── package.json
├── railway.json         # Railway configuration
├── Dockerfile          # Optional: Custom Docker setup
└── .env.example        # Environment template
```

### Railway Configuration (railway.json)
```json
{
  "$schema": "https://railway.app/railway.schema.json",
  "build": {
    "builder": "NIXPACKS",
    "buildCommand": "npm install && npm run build",
    "watchPatterns": ["src/**"]
  },
  "deploy": {
    "startCommand": "npm start",
    "healthcheckPath": "/health",
    "healthcheckTimeout": 100,
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}
```

## 3. Database Services Setup

### Add PostgreSQL Service
```bash
# Add PostgreSQL to your project
railway add postgresql

# Or via Railway dashboard:
# 1. Go to your project dashboard
# 2. Click "New Service"
# 3. Select "Database" → "PostgreSQL"
```

### Add Redis Service
```bash
# Add Redis to your project
railway add redis

# Or via Railway dashboard:
# 1. Go to your project dashboard
# 2. Click "New Service"
# 3. Select "Database" → "Redis"
```

### Database Configuration
Railway automatically provides these environment variables:
- `DATABASE_URL` - PostgreSQL connection string
- `REDIS_URL` - Redis connection string

## 4. Environment Variables Setup

### Set Environment Variables
```bash
# Set via CLI
railway variables set NODE_ENV=production
railway variables set JWT_SECRET=your_super_secure_jwt_secret_here
railway variables set REFRESH_TOKEN_SECRET=your_refresh_token_secret_here
railway variables set STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
railway variables set STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Set multiple variables from file
railway variables set --from-file .env.production
```

### Required Environment Variables
```bash
# App Configuration
NODE_ENV=production
PORT=3000
API_VERSION=v1

# Database (automatically provided by Railway)
DATABASE_URL=postgresql://...
REDIS_URL=redis://...

# Authentication
JWT_SECRET=your_super_secure_jwt_secret
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_SECRET=your_refresh_token_secret
REFRESH_TOKEN_EXPIRES_IN=7d

# Payment Processing
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Email Service
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password

# File Upload
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# External APIs
GOOGLE_MAPS_API_KEY=your_google_maps_key

# Security
BCRYPT_ROUNDS=12
CORS_ORIGIN=https://your-frontend-domain.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### Environment Variables via Dashboard
1. Go to Railway dashboard
2. Select your project
3. Click on your service
4. Go to "Variables" tab
5. Add variables one by one

## 5. Database Migration

### Create Migration Script (migrations/run.js)
```javascript
const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

async function runMigrations() {
  try {
    console.log('🚀 Starting database migrations...');
    
    // Create users table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        phone VARCHAR(20),
        avatar_url TEXT,
        email_verified BOOLEAN DEFAULT FALSE,
        phone_verified BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);
    
    // Add more table creation queries here...
    
    console.log('✅ All migrations completed successfully!');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  runMigrations();
}

module.exports = { runMigrations };
```

### Run Migrations on Railway
```bash
# Run migrations via Railway CLI
railway run node migrations/run.js

# Or add to package.json scripts and run
railway run npm run migrate
```

## 6. Deployment Process

### Deploy to Railway
```bash
# Deploy current branch
railway up

# Deploy specific branch
railway up --branch main

# Deploy with detached mode
railway up --detach
```

### Automatic Deployments
1. Connect GitHub repository:
   - Go to Railway dashboard
   - Select your service
   - Go to "Settings" → "Source"
   - Connect GitHub repository
   - Select branch for auto-deployment

2. Configure deployment triggers:
   - Push to main branch
   - Pull request merges
   - Manual deployments

## 7. Custom Domain Setup

### Add Custom Domain
```bash
# Add domain via CLI
railway domain add api.foodway.com

# Or via dashboard:
# 1. Go to service settings
# 2. Click "Domains"
# 3. Add custom domain
```

### SSL Certificate
Railway automatically provides SSL certificates for custom domains.

## 8. Monitoring and Logging

### View Logs
```bash
# View real-time logs
railway logs

# View logs with follow
railway logs --follow

# View logs for specific service
railway logs --service backend
```

### Health Check Endpoint
Add health check to your app:
```javascript
// src/routes/health.js
app.get('/health', async (req, res) => {
  try {
    // Check database connection
    await db.query('SELECT 1');
    
    // Check Redis connection
    await redis.ping();
    
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      error: error.message
    });
  }
});
```

## 9. Scaling and Performance

### Vertical Scaling
1. Go to Railway dashboard
2. Select your service
3. Go to "Settings" → "Resources"
4. Adjust CPU and memory limits

### Horizontal Scaling
Railway supports horizontal scaling for stateless applications:
```bash
# Scale to multiple instances
railway scale --replicas 3
```

### Database Connection Pooling
Configure connection pooling for better performance:
```javascript
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 20,                    // Maximum connections
  idleTimeoutMillis: 30000,   // Close idle connections after 30s
  connectionTimeoutMillis: 2000, // Connection timeout
});
```

## 10. Security Best Practices

### Environment Security
- Never commit secrets to Git
- Use Railway's environment variables
- Rotate secrets regularly
- Use different secrets for different environments

### Network Security
- Enable CORS with specific origins
- Use HTTPS only in production
- Implement rate limiting
- Validate all inputs

### Database Security
- Use connection pooling
- Implement proper indexing
- Regular backups (Railway handles this)
- Monitor for suspicious queries

## 11. Backup and Recovery

### Database Backups
Railway automatically backs up PostgreSQL databases:
- Daily backups retained for 7 days
- Weekly backups retained for 4 weeks
- Monthly backups retained for 3 months

### Manual Backup
```bash
# Create manual backup
railway pg:backup create

# List backups
railway pg:backup list

# Restore from backup
railway pg:backup restore [backup-id]
```

## 12. Troubleshooting

### Common Issues

#### Build Failures
```bash
# Check build logs
railway logs --build

# Common solutions:
# 1. Check package.json scripts
# 2. Verify Node.js version compatibility
# 3. Check for missing dependencies
```

#### Database Connection Issues
```bash
# Test database connection
railway connect postgresql

# Check environment variables
railway variables

# Verify DATABASE_URL format
```

#### Memory Issues
```bash
# Check memory usage
railway metrics

# Increase memory limit in dashboard
# Optimize application memory usage
```

### Debug Mode
```bash
# Enable debug logging
railway variables set DEBUG=*

# View detailed logs
railway logs --follow
```

## 13. Production Checklist

### Pre-deployment
- [ ] All environment variables set
- [ ] Database migrations tested
- [ ] Health check endpoint working
- [ ] Error handling implemented
- [ ] Logging configured
- [ ] Rate limiting enabled
- [ ] CORS configured
- [ ] SSL/HTTPS enforced

### Post-deployment
- [ ] Health check passing
- [ ] Database connectivity verified
- [ ] Redis connectivity verified
- [ ] API endpoints responding
- [ ] Authentication working
- [ ] Payment processing tested
- [ ] Email delivery working
- [ ] Monitoring alerts configured

## 14. Maintenance

### Regular Tasks
- Monitor application logs
- Check database performance
- Update dependencies
- Review security alerts
- Monitor resource usage
- Test backup restoration

### Updates and Deployments
```bash
# Deploy updates
git push origin main  # If auto-deploy enabled

# Or manual deployment
railway up

# Rollback if needed
railway rollback [deployment-id]
```

This guide provides comprehensive instructions for deploying and maintaining your FoodWay backend on Railway with proper monitoring, security, and scalability considerations.
