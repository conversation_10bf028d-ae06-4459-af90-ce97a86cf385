// Quick Login Test with Real User
const axios = require('axios');

const BASE_URL = 'https://backend-production-f106.up.railway.app/api/v1';

async function testLogin() {
  console.log('🔐 Testing login with real user credentials...');
  
  const credentials = {
    email: '<EMAIL>',
    password: '/Abd/0321'
  };
  
  try {
    console.log('📧 Email:', credentials.email);
    console.log('🔄 Attempting login...');
    
    const response = await axios.post(`${BASE_URL}/auth/login`, credentials, {
      timeout: 15000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Login successful!');
    console.log('Status:', response.status);
    console.log('Token received:', response.data.data.token ? 'Yes' : 'No');
    console.log('User ID:', response.data.data.user.id);
    console.log('Email verified:', response.data.data.user.emailVerified);
    
    // Test one protected endpoint
    const token = response.data.data.token;
    console.log('\n🛡️ Testing protected endpoint...');
    
    const profileResponse = await axios.get(`${BASE_URL}/user/profile`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 15000
    });
    
    console.log('✅ Profile access successful!');
    console.log('Profile data keys:', Object.keys(profileResponse.data.data));
    
    return token;
    
  } catch (error) {
    console.log('❌ Error occurred:');
    console.log('Status:', error.response?.status);
    console.log('Message:', error.response?.data?.error?.message || error.message);
    console.log('Full error:', error.response?.data);
    return null;
  }
}

testLogin().then(token => {
  if (token) {
    console.log('\n🎉 Authentication working! Token available for further testing.');
  } else {
    console.log('\n🔴 Authentication failed.');
  }
}).catch(console.error);
