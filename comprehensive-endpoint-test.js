// Comprehensive Endpoint Test with Working Authentication
const axios = require('axios');

const BASE_URL = 'https://backend-production-f106.up.railway.app/api/v1';

const apiClient = axios.create({
  baseURL: BASE_URL,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

let authToken = null;
let testUser = null;

async function setupAuthentication() {
  console.log('🔐 Setting up authentication...');
  
  // Register a new user for testing
  const registerData = {
    email: `testuser${Date.now()}@foodway.com`,
    password: 'TestPassword123!',
    firstName: 'Test',
    lastName: 'User',
    phone: '+1234567890'
  };

  try {
    const registerResponse = await apiClient.post('/auth/register', registerData);
    console.log('✅ User registered successfully');

    // Login to get token
    const loginResponse = await apiClient.post('/auth/login', {
      email: registerData.email,
      password: registerData.password
    });

    authToken = loginResponse.data.data.token;
    testUser = loginResponse.data.data.user;
    console.log('✅ Authentication token obtained');
    return true;
  } catch (error) {
    console.log('❌ Authentication setup failed:', error.message);
    return false;
  }
}

async function testEndpoint(method, endpoint, data = null, requiresAuth = false) {
  const config = {
    method: method.toLowerCase(),
    url: endpoint,
  };

  if (data) {
    config.data = data;
  }

  if (requiresAuth && authToken) {
    config.headers = { 'Authorization': `Bearer ${authToken}` };
  }

  // Handle health endpoint special case
  if (endpoint === '/health') {
    config.baseURL = 'https://backend-production-f106.up.railway.app';
  }

  try {
    const response = await apiClient.request(config);
    return {
      success: true,
      status: response.status,
      data: response.data,
      responseTime: response.headers['x-response-time'] || 'N/A'
    };
  } catch (error) {
    return {
      success: false,
      status: error.response?.status || 0,
      error: error.response?.data || error.message,
      responseTime: 'N/A'
    };
  }
}

async function runComprehensiveTests() {
  console.log('🚀 Starting Comprehensive Endpoint Tests');
  console.log('=' * 60);

  const testResults = {
    total: 0,
    passed: 0,
    failed: 0,
    categories: {}
  };

  // Define all test cases
  const testSuites = {
    'System Health': [
      { method: 'GET', endpoint: '/health', requiresAuth: false }
    ],
    
    'Authentication': [
      { method: 'POST', endpoint: '/auth/forgot-password', data: { email: '<EMAIL>' }, requiresAuth: false },
      { method: 'POST', endpoint: '/auth/logout', requiresAuth: true }
    ],
    
    'User Management': [
      { method: 'GET', endpoint: '/user/profile', requiresAuth: true },
      { method: 'PUT', endpoint: '/user/profile', data: { firstName: 'Updated', lastName: 'Name' }, requiresAuth: true },
      { method: 'GET', endpoint: '/user/addresses', requiresAuth: true },
      { method: 'POST', endpoint: '/user/addresses', data: {
        street: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zipCode: '12345',
        country: 'US',
        isDefault: false,
        label: 'Home'
      }, requiresAuth: true },
      { method: 'GET', endpoint: '/user/payment-methods', requiresAuth: true },
      { method: 'GET', endpoint: '/user/favorites', requiresAuth: true },
      { method: 'GET', endpoint: '/user/notification-settings', requiresAuth: true }
    ],
    
    'Restaurants': [
      { method: 'GET', endpoint: '/restaurants', requiresAuth: false },
      { method: 'GET', endpoint: '/restaurants/featured?limit=10', requiresAuth: false },
      { method: 'GET', endpoint: '/restaurants/nearby?lat=40.7128&lng=-74.006&radius=5000', requiresAuth: false },
      { method: 'GET', endpoint: '/restaurants/search?q=pizza&lat=40.7128&lng=-74.006', requiresAuth: false },
      { method: 'GET', endpoint: '/categories', requiresAuth: false }
    ],
    
    'Orders': [
      { method: 'GET', endpoint: '/orders', requiresAuth: true },
      { method: 'POST', endpoint: '/orders', data: {
        restaurantId: 'test-restaurant-id',
        items: [{
          menuItemId: 'test-item-id',
          quantity: 2,
          price: 12.99,
          specialInstructions: 'No onions'
        }],
        deliveryAddress: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          zipCode: '10001'
        },
        paymentMethodId: 'test-payment-id'
      }, requiresAuth: true }
    ],
    
    'Reviews': [
      { method: 'POST', endpoint: '/reviews', data: {
        restaurantId: 'test-restaurant-id',
        rating: 5,
        comment: 'Great food!'
      }, requiresAuth: true }
    ],
    
    'Notifications': [
      { method: 'GET', endpoint: '/notifications', requiresAuth: true },
      { method: 'PATCH', endpoint: '/notifications/read-all', requiresAuth: true }
    ],
    
    'Promotions': [
      { method: 'GET', endpoint: '/promotions', requiresAuth: false },
      { method: 'POST', endpoint: '/promotions/validate', data: { code: 'TESTCODE' }, requiresAuth: true }
    ]
  };

  // Run tests for each category
  for (const [category, tests] of Object.entries(testSuites)) {
    console.log(`\n📂 Testing ${category}:`);
    console.log('-' * 40);
    
    testResults.categories[category] = {
      total: tests.length,
      passed: 0,
      failed: 0,
      tests: []
    };

    for (const test of tests) {
      testResults.total++;
      
      console.log(`🔄 ${test.method} ${test.endpoint}`);
      
      const result = await testEndpoint(
        test.method,
        test.endpoint,
        test.data,
        test.requiresAuth
      );

      const testResult = {
        method: test.method,
        endpoint: test.endpoint,
        status: result.status,
        success: result.success,
        responseTime: result.responseTime,
        error: result.success ? null : result.error
      };

      if (result.success) {
        console.log(`  ✅ ${result.status} - Success`);
        testResults.passed++;
        testResults.categories[category].passed++;
      } else {
        console.log(`  ❌ ${result.status} - ${result.error?.error?.message || result.error}`);
        testResults.failed++;
        testResults.categories[category].failed++;
      }

      testResults.categories[category].tests.push(testResult);
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  return testResults;
}

function generateReport(results) {
  console.log('\n' + '=' * 60);
  console.log('📊 COMPREHENSIVE TEST REPORT');
  console.log('=' * 60);
  
  console.log(`\n📈 Overall Results:`);
  console.log(`Total Tests: ${results.total}`);
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`📊 Success Rate: ${((results.passed / results.total) * 100).toFixed(1)}%`);
  
  console.log(`\n📋 Category Breakdown:`);
  for (const [category, data] of Object.entries(results.categories)) {
    const successRate = ((data.passed / data.total) * 100).toFixed(1);
    console.log(`${category}: ${data.passed}/${data.total} (${successRate}%)`);
  }
  
  console.log(`\n🔍 Detailed Results:`);
  for (const [category, data] of Object.entries(results.categories)) {
    console.log(`\n${category.toUpperCase()}:`);
    for (const test of data.tests) {
      const status = test.success ? '✅' : '❌';
      console.log(`  ${status} ${test.method} ${test.endpoint} (${test.status})`);
      if (!test.success && test.error) {
        const errorMsg = test.error?.error?.message || test.error;
        console.log(`     Error: ${errorMsg}`);
      }
    }
  }
}

async function main() {
  // Setup authentication first
  const authSuccess = await setupAuthentication();
  
  if (!authSuccess) {
    console.log('❌ Cannot proceed without authentication');
    return;
  }

  // Run comprehensive tests
  const results = await runComprehensiveTests();
  
  // Generate detailed report
  generateReport(results);
  
  console.log('\n🎯 Key Findings:');
  if (results.passed === results.total) {
    console.log('🟢 All endpoints are working correctly!');
  } else if (results.passed > results.total * 0.7) {
    console.log('🟡 Most endpoints working, some issues to address');
  } else {
    console.log('🔴 Significant issues found, requires attention');
  }
}

main().catch(console.error);
