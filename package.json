{"name": "customer_app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:all": "eas build --platform all", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "type-check": "tsc --noEmit", "railway:deploy": "railway up", "railway:logs": "railway logs", "railway:status": "railway status"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@stripe/stripe-react-native": "^0.45.0", "@tanstack/react-query": "^5.81.5", "@types/pg": "^8.15.4", "axios": "^1.10.0", "dotenv": "^17.0.0", "expo": "53.0.15", "expo-blur": "~14.1.5", "expo-camera": "^16.1.10", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-linking": "^7.1.6", "expo-location": "^18.1.6", "expo-notifications": "^0.31.3", "expo-router": "^5.1.2", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "^0.4.5", "expo-system-ui": "~5.0.9", "expo-web-browser": "~14.2.0", "ioredis": "^5.6.1", "pg": "^8.16.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-maps": "^1.20.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "redis": "^5.5.6", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@railway/cli": "^4.5.4", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "jest-environment-node": "^30.0.2", "typescript": "~5.8.3"}, "private": true}