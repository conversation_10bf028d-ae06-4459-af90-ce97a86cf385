// Mock for react-native/Libraries/TurboModule/TurboModuleRegistry
// This is used by native modules that don't work on web

const TurboModuleRegistry = {
  get: function(name) {
    console.warn(`TurboModuleRegistry.get('${name}') called on web - returning null`);
    return null;
  },
  getEnforcing: function(name) {
    console.warn(`TurboModuleRegistry.getEnforcing('${name}') called on web - returning null`);
    return null;
  }
};

module.exports = TurboModuleRegistry;
module.exports.default = TurboModuleRegistry;
