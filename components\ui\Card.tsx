import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { COLORS, SPACING, BORDER_RADIUS, SHADOWS } from '../../utils/constants';

interface CardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  padding?: 'none' | 'small' | 'medium' | 'large';
  shadow?: 'none' | 'small' | 'medium' | 'large';
  borderRadius?: 'none' | 'small' | 'medium' | 'large';
}

export default function Card({
  children,
  style,
  padding = 'medium',
  shadow = 'medium',
  borderRadius = 'medium',
}: CardProps) {
  const getCardStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      backgroundColor: COLORS.white,
    };

    // Padding
    switch (padding) {
      case 'none':
        break;
      case 'small':
        baseStyle.padding = SPACING.sm;
        break;
      case 'large':
        baseStyle.padding = SPACING.lg;
        break;
      default:
        baseStyle.padding = SPACING.md;
    }

    // Border radius
    switch (borderRadius) {
      case 'none':
        baseStyle.borderRadius = 0;
        break;
      case 'small':
        baseStyle.borderRadius = BORDER_RADIUS.sm;
        break;
      case 'large':
        baseStyle.borderRadius = BORDER_RADIUS.xl;
        break;
      default:
        baseStyle.borderRadius = BORDER_RADIUS.lg;
    }

    // Shadow
    switch (shadow) {
      case 'none':
        break;
      case 'small':
        Object.assign(baseStyle, SHADOWS.sm);
        break;
      case 'large':
        Object.assign(baseStyle, SHADOWS.lg);
        break;
      default:
        Object.assign(baseStyle, SHADOWS.md);
    }

    return baseStyle;
  };

  return <View style={[getCardStyle(), style]}>{children}</View>;
}
