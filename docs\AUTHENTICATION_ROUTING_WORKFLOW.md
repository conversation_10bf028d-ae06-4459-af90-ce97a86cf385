# Authentication and Routing Workflow

This document explains how the FoodWay customer app determines whether to show the signin screen or the main app screens when the app starts up.

## Overview

The FoodWay app uses a **conditional rendering approach** where the authentication state determines which screens are displayed. The signin screen appears first for unauthenticated users, while authenticated users see the main app content.

## App Entry Point Flow

### 1. Application Startup
```
package.json → "main": "expo-router/entry"
↓
Expo Router Entry Point
↓
app/_layout.tsx (Root Layout)
↓
app/index.tsx (Home Screen - First Screen)
```

### 2. Root Layout (`app/_layout.tsx`)
The root layout sets up the navigation structure but doesn't handle authentication:

<augment_code_snippet path="app/_layout.tsx" mode="EXCERPT">
````typescript
export default function RootLayout() {
  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="index" />
      <Stack.Screen name="(auth)" />
      <Stack.Screen name="(tabs)" />
      {/* Other screens */}
    </Stack>
  );
}
````
</augment_code_snippet>

### 3. Home Screen Authentication Check (`app/index.tsx`)
The **main authentication logic** happens in the home screen (`app/index.tsx`):

<augment_code_snippet path="app/index.tsx" mode="EXCERPT">
````typescript
export default function HomeScreen() {
  const { user, isAuthenticated } = useAuthStore();

  // Authentication check - this determines what users see first
  if (!isAuthenticated) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.authPrompt}>
          <Ionicons name="restaurant" size={64} color={COLORS.primary} />
          <Text style={styles.authTitle}>Welcome to FoodWay</Text>
          <Text style={styles.authSubtitle}>
            Please sign in to discover amazing restaurants near you
          </Text>
          <TouchableOpacity
            style={styles.authButton}
            onPress={() => router.push('/(auth)/login' as any)}
          >
            <Text style={styles.authButtonText}>Sign In</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Main app content for authenticated users
  return (
    <SafeAreaView style={styles.container}>
      {/* Home screen content */}
    </SafeAreaView>
  );
}
````
</augment_code_snippet>

## Authentication State Management

### Zustand Store (`store/authStore.ts`)
The app uses Zustand for authentication state management with persistence:

<augment_code_snippet path="store/authStore.ts" mode="EXCERPT">
````typescript
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      // ... actions
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
````
</augment_code_snippet>

### Token Management
- **Access tokens** and **refresh tokens** are stored securely using Expo SecureStore
- **User data** and **authentication status** are persisted using AsyncStorage
- **Automatic token refresh** happens when the app starts

## Authentication Flow Scenarios

### Scenario 1: First Time User (No Authentication)
```
App Starts
↓
app/index.tsx loads
↓
useAuthStore() → isAuthenticated: false
↓
Renders signin prompt with "Sign In" button
↓
User taps "Sign In"
↓
Navigates to /(auth)/login
```

### Scenario 2: Returning User (Valid Token)
```
App Starts
↓
app/index.tsx loads
↓
useAuthStore() → isAuthenticated: true (from persisted state)
↓
Renders main home screen content
↓
Background: Token refresh validation occurs
```

### Scenario 3: Returning User (Expired Token)
```
App Starts
↓
app/index.tsx loads
↓
useAuthStore() → isAuthenticated: true (from persisted state)
↓
Background: Token refresh fails
↓
authStore.logout() called automatically
↓
isAuthenticated becomes false
↓
Screen re-renders to show signin prompt
```

## Navigation Structure

### Authentication Routes (`app/(auth)/`)
```
app/(auth)/
├── _layout.tsx          → Auth stack layout
├── login.tsx           → Sign in screen
└── register.tsx        → Sign up screen
```

### Main App Routes (`app/(tabs)/`)
```
app/(tabs)/
├── _layout.tsx          → Tab navigation layout
├── index.tsx           → Home tab
├── search.tsx          → Search tab
├── cart.tsx            → Cart tab
├── orders.tsx          → Orders tab
└── profile.tsx         → Profile tab
```

## Route Guards and Protection

### Tab-Level Protection
Some tabs also implement their own authentication checks:

<augment_code_snippet path="app/(tabs)/profile.tsx" mode="EXCERPT">
````typescript
export default function ProfileScreen() {
  const { user, logout, isAuthenticated } = useAuthStore();

  if (!isAuthenticated || !user) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.authPrompt}>
          <Text style={styles.authTitle}>Sign In Required</Text>
          <TouchableOpacity
            style={styles.authButton}
            onPress={() => router.push('/(auth)/login')}
          >
            <Text style={styles.authButtonText}>Sign In</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }
  // Profile content...
}
````
</augment_code_snippet>

## Login Success Flow

### After Successful Login
```
User submits login form
↓
authStore.login() called
↓
API request to /auth/login
↓
Tokens stored securely
↓
isAuthenticated set to true
↓
router.replace('/') navigates back to home
↓
Home screen re-renders with main content
```

<augment_code_snippet path="app/(auth)/login.tsx" mode="EXCERPT">
````typescript
const handleLogin = useCallback(async () => {
  try {
    // Attempt login
    await login(formData);
    
    // Navigate on success
    router.replace('/' as any);
  } catch (error) {
    // Error handling...
  }
}, []);
````
</augment_code_snippet>

## Key Design Decisions

### 1. **Conditional Rendering vs Route Guards**
- Uses conditional rendering in the home screen instead of route-level guards
- Provides immediate feedback without navigation delays
- Simpler implementation for this app structure

### 2. **Persistent Authentication State**
- Authentication state persists across app restarts
- Automatic token refresh on app startup
- Graceful fallback to signin when tokens are invalid

### 3. **Single Entry Point**
- All users start at `app/index.tsx`
- Authentication check determines the UI
- No complex routing logic needed

## Security Considerations

### Token Storage
- **Access tokens**: Stored in Expo SecureStore (encrypted)
- **Refresh tokens**: Stored in Expo SecureStore (encrypted)
- **User data**: Stored in AsyncStorage (for quick access)

### Token Validation
- Automatic token refresh on app startup
- Background validation of stored tokens
- Automatic logout when tokens are invalid

## Summary

The FoodWay app's authentication workflow ensures that:

1. **First-time users** see a signin prompt immediately upon opening the app
2. **Returning users** with valid authentication see the main app content
3. **Users with expired tokens** are automatically signed out and shown the signin prompt
4. **Navigation** is seamless between authenticated and unauthenticated states

The signin screen effectively becomes the "first screen" for any user who needs to authenticate, while authenticated users bypass it entirely and see the main home screen content.
